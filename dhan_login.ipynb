{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-28 13:39:02,203 - INFO - Dhanhq instance created.\n", "2025-02-28 13:39:02,431 - INFO - Dhan API login successful!\n", "2025-02-28 13:39:02,432 - INFO - Successfully logged in. You can now use dhan_api_instance to make API calls.\n"]}], "source": ["import logging\n", "from dhanhq import dhanhq  # Assuming dhanhq.py is in the same directory\n", "\n", "# Set up basic logging (optional, but good practice)\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "\n", "def login_dhan_api(client_id, access_token):\n", "     \n", "    try:\n", "        # Instantiate the dhanhq class\n", "        dhan_instance = dhanhq(client_id=client_id, access_token=access_token)\n", "        logging.info(\"Dhanhq instance created.\")\n", "\n", "        # Test the connection by fetching fund limits (a simple API call)\n", "        fund_limits_response = dhan_instance.get_fund_limits()\n", "\n", "        if fund_limits_response['status'] == 'success':\n", "            logging.info(\"Dhan API login successful!\")\n", "            return dhan_instance\n", "        else:\n", "            logging.error(\"Dhan API login failed.\")\n", "            logging.error(f\"Error Remarks: {fund_limits_response.get('remarks')}\")\n", "            logging.error(f\"Error Data: {fund_limits_response.get('data')}\")\n", "            return None\n", "\n", "    except Exception as e:\n", "        logging.error(f\"Exception during Dhan API login: {e}\")\n", "        return None\n", "\n", "if __name__ == \"__main__\":\n", "    # Replace with your actual Client ID and Access Token from Dhan API\n", "    my_client_id = \"1000596243\"\n", "    my_access_token = \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzQzMTgwMDU3LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTAwMDU5NjI0MyJ9.YHGgkCHER4JuaUD1pd8uXLGSut0pmyZ02ncW3YswoWyq0LO67QDCKbEwP9DXg-0oupAYKaSQTaOM3Skv-bw6hg\"\n", "\n", "    dhan_api_instance = login_dhan_api(my_client_id, my_access_token)\n", "\n", "    if dhan_api_instance:\n", "        logging.info(\"Successfully logged in. You can now use dhan_api_instance to make API calls.\")\n", "        \n", "    else:\n", "        logging.error(\"Dhan API login was not successful. Check logs for errors.\")"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.2"}}, "nbformat": 4, "nbformat_minor": 2}