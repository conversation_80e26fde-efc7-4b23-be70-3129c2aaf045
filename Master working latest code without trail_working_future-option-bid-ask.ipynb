import asyncio
import logging
import time
import random
import statistics
import concurrent.futures
import pandas as pd
import pyotp
from datetime import datetime, timedelta
from collections import deque, defaultdict
from queue import SimpleQueue, Queue
from NorenRestApiPy.NorenApi import <PERSON><PERSON><PERSON><PERSON>
from dataclasses import dataclass, field
from typing import Dict, Deque, Optional, List, Callable, Any, Set
from functools import lru_cache
import nest_asyncio
from asyncio import Lock
import numpy as np
from numba import njit
from enum import Enum
from numba_indicators import supertrend_numba,jma_numba_direction_wrapper,candle_height_numba, improved_jma_numba_wrapper
from typing import Callable, Dict, List, Any, Tuple
import threading
import csv
import os
import json
from dateutil import parser
import fastapi
import uvicorn
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import queue
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
## big ass globals
retracement_monitors = {}
monitor_flags = {}

@dataclass
class OHLCV:
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: float = 0
    tick_count: int = 0
    
    @classmethod
    def from_tick(cls, tick: Dict, timestamp: datetime):
        price = tick['ltp']
        return cls(
            timestamp=timestamp,
            open=price,
            high=price,
            low=price,
            close=price,
            volume=tick.get('volume', 0),
            tick_count=1
        )
@dataclass
class ResamplingBuffer:
    timeframe: str
    current_candle: Optional[OHLCV] = None
    buffer: deque = field(default_factory=lambda: deque(maxlen=500))

    def get_candle_start_time(self, tick_time: datetime) -> datetime:
        """Align tick time to the appropriate candle start time."""
        timeframe_value = int(''.join(filter(str.isdigit, self.timeframe)))
        timeframe_unit = ''.join(filter(str.isalpha, self.timeframe))
        
        if timeframe_unit == 's':  # seconds-based timeframes
            if timeframe_value >= 60:  # Handle multi-minute cases
                minutes = (tick_time.minute * 60 + tick_time.second) // timeframe_value * (timeframe_value // 60)
                aligned_time = tick_time.replace(minute=0, second=0, microsecond=0) + timedelta(minutes=minutes)
            else:
                aligned_time = tick_time - timedelta(seconds=tick_time.second % timeframe_value)

        elif timeframe_unit == 'm':  # minutes
            aligned_time = tick_time - timedelta(minutes=tick_time.minute % timeframe_value)
            aligned_time = aligned_time.replace(second=0, microsecond=0)
        elif timeframe_unit == 'h':  # hours
            aligned_time = tick_time - timedelta(hours=tick_time.hour % timeframe_value)
            aligned_time = aligned_time.replace(minute=0, second=0, microsecond=0)
        else:
            raise ValueError(f"Unsupported timeframe: {self.timeframe}")

        # Always clear milliseconds and microseconds
        return aligned_time.replace(microsecond=0)

class AdvancedResampler:
    def __init__(self, valid_timeframes):
        self.buffers: Dict[str, Dict[str, ResamplingBuffer]] = defaultdict(dict)
        self.valid_timeframes = valid_timeframes
        self.indicator_manager = EnhancedIndicatorManager()
        self.locks = defaultdict(asyncio.Lock)  # Lock for each token to ensure sequential processing

    async def process_tick(self, token: str, tick: dict, resampling_enabled: Dict[str, bool]):
        """Process a new tick and update the candles if resampling is enabled."""
        tick_time = datetime.fromisoformat(tick['tt'])
        tick_price = tick['ltp']
        
        for timeframe in self.valid_timeframes:
            if not resampling_enabled.get(timeframe, False):
                continue

            # Initialize the buffer if it doesn't exist
            if timeframe not in self.buffers[token]:
                self.buffers[token][timeframe] = ResamplingBuffer(timeframe=timeframe)
            
            buffer = self.buffers[token][timeframe]
            candle_start_time = buffer.get_candle_start_time(tick_time)

            # Handle a new candle bucket
            if not buffer.current_candle or buffer.current_candle.timestamp != candle_start_time:
                if buffer.current_candle:
                    # Check for out-of-order candle before appending
                    if buffer.buffer and buffer.current_candle.timestamp <= buffer.buffer[-1].timestamp:  # fix for non sorted candle sequence
                        print(f"Skipping out-of-order candle: {buffer.current_candle.timestamp}")
                    else:
                        buffer.buffer.append(buffer.current_candle)
                        # Debug log to verify the buffer state
                        # print(f"Appended Candle: {buffer.current_candle.timestamp}")
                        # print(f"Buffer Timestamps: {[c.timestamp for c in buffer.buffer]}")

                    # Calculate indicators for the finalized candle
                    await self.indicator_manager.calculate_indicator(
                        token=token,
                        timeframe=timeframe,
                        candles=list(buffer.buffer)
                    )
                
                # Start a new candle
                buffer.current_candle = OHLCV.from_tick(tick, candle_start_time)
            else:
                # Update the existing candle
                buffer.current_candle.high = max(buffer.current_candle.high, tick_price)
                buffer.current_candle.low = min(buffer.current_candle.low, tick_price)
                buffer.current_candle.close = tick_price
                buffer.current_candle.tick_count += 1
                buffer.current_candle.volume += tick.get('volume', 0)
   
    def get_resampled_data(self, token: str, timeframe: str, n_candles: Optional[int] = None):
        """Retrieve resampled data for a token and timeframe."""
        if token not in self.buffers or timeframe not in self.buffers[token]:
            return []
        
        buffer = self.buffers[token][timeframe].buffer
        if n_candles is None:
            return list(buffer)
        return list(buffer)[-n_candles:]


@dataclass
class IndicatorConfig:
    name: str
    function: Callable
    params: Dict[str, Any]
    timeframes: List[str]

@dataclass
class CandleIndicatorData:
    candle: 'OHLCV'  # Assuming OHLCV is defined elsewhere
    indicators: Dict[str, float] = field(default_factory=dict)

class EnhancedIndicatorManager:
    def __init__(self):
        self.indicators: Dict[str, Dict[str, Dict[str, Any]]] = defaultdict(lambda: defaultdict(dict))
        self.configs: Dict[str, IndicatorConfig] = {}
        self.candle_indicators: Dict[str, Dict[str, List[CandleIndicatorData]]] = defaultdict(lambda: defaultdict(list))
        
        ## event Monitoring system with async queue start
        self.subscriptions: Dict[Tuple[str, str, str], deque] = defaultdict(lambda: deque(maxlen=50))
        self.subscription_times: Dict[Tuple[str, str, str], datetime] = {}  # Store subscription times separately
        self.callbacks: Dict[Tuple[str, str, str], List[Callable]] = defaultdict(list)
        self.callback_queue: asyncio.Queue = asyncio.Queue()
        self._callback_task: Optional[asyncio.Task] = None
        ## event Monitoring system with async queue end

    def register_indicator(self, name: str, function: Callable, params: Dict[str, Any], timeframes: List[str]):
        """Register an indicator configuration"""
        self.configs[name] = IndicatorConfig(name, function, params, timeframes)

    async def start_callback_processor(self):
        """Start the async callback processing task"""
        if self._callback_task is None:
            self._callback_task = asyncio.create_task(self._process_callbacks())

    async def stop_callback_processor(self):
        """Stop the async callback processing task"""
        if self._callback_task:
            self._callback_task.cancel()
            try:
                await self._callback_task
            except asyncio.CancelledError:
                pass
            self._callback_task = None

    async def _safe_callback_execution(self, callback, *args):
        try:
            if asyncio.iscoroutinefunction(callback):
                await callback(*args)
            else:
                await asyncio.to_thread(callback, *args)
        except Exception as e:
            print(f"Callback execution error: {e}")
            

    async def _process_callbacks(self):
        while True:
            try:
                token, timeframe, indicator, value, timestamp = await self.callback_queue.get()
                key = (token, timeframe, indicator)
                callbacks = self.callbacks.get(key, [])
                
                if callbacks:
                    await asyncio.gather(
                        *[self._safe_callback_execution(callback, token, timeframe, indicator, value, timestamp) 
                        for callback in callbacks],
                        return_exceptions=True
                    )
                
                self.callback_queue.task_done()
            
            except Exception as e:
                print(f"Callback processing error: {e}")

    async def subscribe(self, token: str, timeframe: str, indicator: str, callback: Optional[Callable] = None):
        """Asynchronously subscribe to an indicator with deduplication"""
        key = (token, timeframe, indicator)
        print(f"Registering callback for: {key}")
        await self.start_callback_processor()

        # Store subscription time separately (no need to store it inside deque)
        if key not in self.subscription_times:
            self.subscription_times[key] = datetime.now()

        # Add the callback to the list of callbacks
        if callback:
            # Directly add the callback if it's already an async function
            if asyncio.iscoroutinefunction(callback):
                if callback not in self.callbacks[key]:
                    self.callbacks[key].append(callback)
            else:
                # Wrap synchronous callbacks in an async function
                async def async_wrapper(*args, **kwargs):
                    return callback(*args, **kwargs)
                
                if async_wrapper not in self.callbacks[key]:
                    self.callbacks[key].append(async_wrapper)

        # Initialize the deque for historical data (timestamp, value)
        self.subscriptions[key] = deque(maxlen=50)
        return key

    async def notify_subscribers(self, token: str, timeframe: str, indicator: str, value: float, timestamp: datetime):
        key = (token, timeframe, indicator)
        subscription_time = self.subscription_times.get(key)
        if subscription_time and timestamp >= subscription_time:
            if key in self.subscriptions:
                if not any(existing_timestamp == timestamp and existing_value == value 
                        for existing_timestamp, existing_value in self.subscriptions[key]):
                    self.subscriptions[key].append((timestamp, value))

            if key in self.callbacks:

                await self.callback_queue.put((token, timeframe, indicator, value, timestamp))
                
    def get_numpy_arrays(self, candles: List['OHLCV']) -> tuple:
        """Convert list of OHLCV candles to numpy arrays"""
        if not candles:
            return np.array([]), np.array([]), np.array([]), np.array([])
        
        high = np.array([c.high for c in candles])
        low = np.array([c.low for c in candles])
        close = np.array([c.close for c in candles])
        timestamps = np.array([c.timestamp for c in candles])
        
        return high, low, close, timestamps

    async def calculate_indicator(self, token: str, timeframe: str, candles: List['OHLCV']):
        high, low, close, timestamps = self.get_numpy_arrays(candles)

        if len(close) == 0:
            return

        candle_data_list = [CandleIndicatorData(candle=candle) for candle in candles]
        # Get the current running event loop
        loop = asyncio.get_running_loop()
        # Use run_in_executor for parallel indicator calculations
        indicator_futures = {
            name: loop.run_in_executor(
                None,  # Use the default executor
                self._call_indicator_function,
                config.function,
                high, low, close,
                config.params
            )
            for name, config in self.configs.items()
            if timeframe in config.timeframes
        }
        # Wait for all futures and process results
        for name, future in indicator_futures.items():
            try:
                results = await future
                if isinstance(results, tuple):
                    # Handle multiple return values
                    for i, result in enumerate(results):
                        indicator_name = f"{name}_{i}"

                        self.indicators[token][timeframe][indicator_name] = {
                            'values': result,
                            'timestamps': timestamps
                        }
                        for idx, value in enumerate(result):
                            if idx < len(candle_data_list):
                                candle_data_list[idx].indicators[indicator_name] = value
                                await self.notify_subscribers(token, timeframe, indicator_name, value, timestamps[idx])
                else:
                    # Single return value indicator
                    self.indicators[token][timeframe][name] = {
                        'values': results,
                        'timestamps': timestamps
                    }
                    for idx, value in enumerate(results):
                        if idx < len(candle_data_list):
                            candle_data_list[idx].indicators[name] = value
                            await self.notify_subscribers(token, timeframe, name, value, timestamps[idx])
            except Exception as e:
                logging.error(f"Error calculating {name} for {token} {timeframe}: {e}")

        # Update the candle_indicators structure
        self.candle_indicators[token][timeframe] = candle_data_list

    # Helper method to ensure correct function calls
    def _call_indicator_function(self, func, high, low, close, params):
        # Filter params to include only those accepted by the function
        valid_params = {k: v for k, v in params.items() if k in func.__code__.co_varnames}
        return func(high, low, close, **valid_params)
        
    def get_latest_candle_with_indicators(self, token: str, timeframe: str) -> Optional[CandleIndicatorData]:
        """Get the latest candle with its indicator values"""
        if token in self.candle_indicators and timeframe in self.candle_indicators[token]:
            candle_data_list = self.candle_indicators[token][timeframe]
            if candle_data_list:
                return candle_data_list[-1]
        return None

    def get_candles_with_indicators(self, token: str, timeframe: str, n_candles: Optional[int] = None) -> List[CandleIndicatorData]:
        """Get historical candles with their indicator values"""
        if token in self.candle_indicators and timeframe in self.candle_indicators[token]:
            candle_data_list = self.candle_indicators[token][timeframe]
            if n_candles is not None:
                return candle_data_list[-n_candles:]
            return candle_data_list
        return []

    # Keep existing methods for backward compatibility
    def get_latest_indicator(self, token: str, timeframe: str, indicator_name: str) -> tuple:
        """Get latest indicator value and its timestamp"""
        if token in self.indicators and timeframe in self.indicators[token]:
            indicator_data = self.indicators[token][timeframe].get(indicator_name)
            if indicator_data and len(indicator_data['values']) > 0:
                return (
                    indicator_data['values'][-1],
                    indicator_data['timestamps'][-1]
                )
        return None, None

    def get_indicator_history(self, token: str, timeframe: str, indicator_name: str, n_values: int = None) -> tuple:
        """Get historical indicator values and timestamps"""
        if token in self.indicators and timeframe in self.indicators[token]:
            indicator_data = self.indicators[token][timeframe].get(indicator_name)
            if indicator_data:
                if n_values:
                    return (
                        indicator_data['values'][-n_values:],
                        indicator_data['timestamps'][-n_values:]
                    )
                return indicator_data['values'], indicator_data['timestamps']
        return np.array([]), np.array([])
    
class TickCollector:
    VALID_TIMEFRAMES = ['5s','15s','60s','120s','300s']
    def __init__(self,resampler: AdvancedResampler, credentials_file="usercred.xlsx"):
        self.processing_lock = asyncio.Lock()
        self.api = None
        self.feed_opened = False
        #self.tick_queue = SimpleQueue()
        self.tick_queue = asyncio.Queue()
        self.ring_buffers = defaultdict(lambda: deque(maxlen=100))
        self.resampling_enabled = defaultdict(dict)
        self.last_tick_time = {}
        self.active_subscriptions = set()
        
        self.logger = logging.getLogger("TickCollector")
        self.resampler = resampler  # Inject resampler instead of creating a new one # grok        
        #self.resampler = AdvancedResampler(valid_timeframes=self.VALID_TIMEFRAMES)
        self.option_manager = None  # To store OptionManager reference        
        self.future_manager = None
        
        # Initialize API
        self._initialize_api(credentials_file)
        self.resampler.indicator_manager = EnhancedIndicatorManager()

        self.strategy_manager = None

        self.resampler.indicator_manager.register_indicator(
            name='supertrend',
            function=supertrend_numba,
            params={'length': 3, 'multiplier': 4},
            timeframes=['5s','15s','60s','120s', '300s']
        )

        self.resampler.indicator_manager.register_indicator(
            name='jma_switch',
            function=jma_numba_direction_wrapper,
            params={'length': 5, 'phase': 50, 'power': 1},
            timeframes=['5s','15s','60s','120s', '300s']
        )
        
        self.resampler.indicator_manager.register_indicator(
            name='jma',
            function=improved_jma_numba_wrapper,
            params={'base_length': 5, 'base_phase': 100, 'power': 2},
            timeframes=['5s','15s','60s','120s', '300s']
        )
        
        self.resampler.indicator_manager.register_indicator(
            name='candle_height',
            function=candle_height_numba,
            params={'lookback': 10},
            timeframes=['5s','15s','60s','120s', '300s']
        )

    def set_option_manager(self, option_manager):
        """Set the OptionManager for handling option ticks."""
        self.option_manager = option_manager

    async def handle_indicator_change(self, **kwargs):
        """Handle indicator change events."""
        self.logger.info(
            f"Indicator {kwargs['indicator_name']} changed for {kwargs['token']} "
            f"on {kwargs['timeframe']}: {kwargs['prev_value']} -> {kwargs['curr_value']}"
        )
        # Add your custom indicator change handling logic here

    async def handle_indicator_error(self, **kwargs):
        """Handle indicator calculation errors."""
        self.logger.error(
            f"Error calculating {kwargs['indicator_name']} for {kwargs['token']} "
            f"on {kwargs['timeframe']}: {kwargs['error']}"
        )


    def _initialize_api(self, credentials_file):
        self.api = NorenApi(
            host="https://api.shoonya.com/NorenWClientTP/",
            websocket="wss://api.shoonya.com/NorenWSTP/"
        )
        credentials = pd.read_excel('usercred.xlsx')
        user, password, vendor_code, app_key, imei, qr_code = credentials.iloc[0]
        factor2 = pyotp.TOTP(qr_code).now()

        self.api.login_result = self.api.login(
            userid=user,
            password=password,
            twoFA=factor2,
            vendor_code=vendor_code,
            api_secret=app_key,
            imei=imei
        )

    def initialize_resampling_enabled(self, token):
        """Initialize resampling_enabled for the token."""
        if token not in self.resampling_enabled:
            self.resampling_enabled[token] = {tf: False for tf in self.VALID_TIMEFRAMES}
            self.logger.info(f"Initialized resampling_enabled for token {token}")

    async def set_resampling(self, token, timeframe, enable):
        """Enable or disable resampling for a token and timeframe."""
        if token in self.resampling_enabled and timeframe in self.VALID_TIMEFRAMES:
            self.resampling_enabled[token][timeframe] = enable
            self.logger.info(f"Resampling {'enabled' if enable else 'disabled'} for token {token} and timeframe {timeframe}")
            
            
    ### perfect working simple
    def event_handler_feed_update(self, tick_data):
        """Handles incoming tick data for both indices and options."""
        #print(tick_data)
        try:
            # Extract required fields
            token = tick_data.get('tk')
            price = tick_data.get('lp')
            timestamp = tick_data.get('ft')

            # Skip if required fields are missing
            if not all([token, price, timestamp]):
                return  # Skip silently without logging

            # Convert timestamp to ISO format
            try:
                timest = datetime.fromtimestamp(int(timestamp)).isoformat()
            except (ValueError, TypeError):
                return  # Skip if timestamp is invalid

            # Create new tick
            new_tick = {'tt': timest, 'ltp': float(price)}

            # Handle option ticks if OptionManager is present
            if self.option_manager and token in self.option_manager.option_subscriptions:
                numeric_subscriptions = {sub.split('|')[1] for sub in self.active_subscriptions}
                if token not in numeric_subscriptions:
                    # Handle option tick asynchronously
                    asyncio.run_coroutine_threadsafe(
                        self.option_manager.handle_option_tick(token, new_tick), 
                        loop
                    )
                else:
                    # Add to tick queue
                    self.tick_queue.put_nowait((token, new_tick))
            else:
                # Add to tick queue for regular indices
                self.tick_queue.put_nowait((token, new_tick))

        except Exception as e:
            # Log unexpected errors
            self.logger.error(f"Error processing tick data: {e}")

    async def process_tick_queue(self):
        while True:
            while not self.tick_queue.empty():
                token, tick = await self.tick_queue.get()
                # Use a lock to ensure thread-safe operations on ring_buffers and last_tick_time
                async with self.processing_lock:
                    # Avoid unnecessary new dictionary creation by updating in-place
                    self.ring_buffers[token].append(tick)
                    self.last_tick_time[token] = datetime.fromisoformat(tick['tt'])
                    
                    if token in self.resampling_enabled:
                        await self.resampler.process_tick(
                            token,
                            tick,
                            self.resampling_enabled[token]
                        )
            # Use a smaller sleep to make the loop more responsive if needed
            await asyncio.sleep(0.001)  # Reduced from 0.01 to be more responsive
    

    async def connect_and_subscribe(self):
        """Connects to WebSocket and subscribes to tick data."""
        retry_delay = 1
        max_retry_delay = 32
        max_retries = 10
        retries = 0
        
        while retries < max_retries:
            try:
                self.api.start_websocket(
                    order_update_callback=self.event_handler_order_update,
                    subscribe_callback=self.event_handler_feed_update,
                    socket_open_callback=self.open_callback,
                    socket_close_callback=self.close_callback
                )
                await self.wait_for_feed_open(timeout=30)
                self.logger.info("WebSocket connected successfully.")
                
                # # Add subscriptions
                #await self.manage_subscriptions('add', 'MCX|447850')
                #await self.manage_subscriptions('add', 'MCX|437800')#
                await self.manage_subscriptions('add', 'NSE|26000')
                #await self.manage_subscriptions('add', 'NSE|26009')

                retry_delay = 1
                retries = 0
                await self.monitor_connection()
                
            except Exception as e:
                self.logger.error(f"WebSocket connection error: {e}")
                retries += 1
                self.logger.info(f"Reconnecting in {retry_delay} seconds... (Attempt {retries}/{max_retries})")
                await asyncio.sleep(retry_delay)
                retry_delay = min(retry_delay * 2, max_retry_delay)

        if retries >= max_retries:
            self.logger.error("Max retries reached. Exiting.")
            raise Exception("Max retries reached")

    async def manage_subscriptions(self, command, subscription):
        """Manages subscriptions for tick data."""
        token = subscription.split('|')[1]

        if command == 'add':
            if subscription not in self.active_subscriptions:
                self.api.subscribe([subscription])
                self.active_subscriptions.add(subscription)
                self.logger.info(f"Subscribed to {subscription}")
                
                self.initialize_resampling_enabled(token)
                for timeframe in self.VALID_TIMEFRAMES:
                    await self.set_resampling(token, timeframe, True)
            else:
                self.logger.warning(f"Already subscribed to {subscription}")
        elif command == 'remove':
            if subscription in self.active_subscriptions:
                self.api.unsubscribe([subscription])
                self.active_subscriptions.remove(subscription)
                self.logger.info(f"Unsubscribed from {subscription}")
            else:
                self.logger.warning(f"Not subscribed to {subscription}")

    async def wait_for_feed_open(self, timeout):
        """Waits for WebSocket feed to open."""
        start_time = asyncio.get_event_loop().time()
        while not self.feed_opened:
            if asyncio.get_event_loop().time() - start_time > timeout:
                raise TimeoutError("Timed out waiting for feed to open")
            await asyncio.sleep(1)

    async def monitor_connection(self):
        """Monitors the WebSocket connection."""
        while True:
            if not self.feed_opened:
                self.logger.warning("Feed closed unexpectedly. Reconnecting...")
                raise Exception("Feed closed")
            await asyncio.sleep(5)

    def close_callback(self):
        """Callback function for WebSocket closure."""
        self.feed_opened = False
        self.logger.warning("WebSocket connection closed.")

    def open_callback(self):
        """Callback function for WebSocket opening."""
        if not self.feed_opened:
            self.feed_opened = True
            self.logger.info('Feed Opened')

    def event_handler_order_update(self, data):
        """Handles order updates."""
        self.logger.info(f"Order update: {data}")
        
    def load_index_config(self):
        with open('config.json') as f:
            config = json.load(f)
            return {str(idx['token']): idx for idx in config['indices']}           

class OptionManager:
    def __init__(self, tick_collector):
        self.logger = logging.getLogger("OptionManager")
        self.config = self._load_config()
        self.csv_cache = {}  # Cache loaded CSV data
        self.option_data = {}  # For storing option tokens and symbols by index token
        self.option_ticks = defaultdict(lambda: {})  # Separate storage for option ticks
        self.tick_collector = tick_collector  # Dependency Injection for index ticks
        self.option_subscriptions = set()  # Separate subscription tracking for options
        self._setup_options()        
        self._subscribe_to_options()  # Subscribe to options immediately
        self.warned_tokens = set()

    def _load_config(self):
        try:
            with open('config.json', 'r') as config_file:
                return json.load(config_file)
        except FileNotFoundError:
            self.logger.error("Config file 'config.json' not found.")
            raise
        except json.JSONDecodeError:
            self.logger.error("Config file 'config.json' is not valid JSON.")
            raise
        except Exception as e:
            self.logger.error(f"Error loading config: {e}")
            raise

    def _setup_options(self):
        for index in self.config['indices']:
            df = self._load_csv_data(index['index_name'])
            if df is not None:
                df = df.sort_values(by='Expiry')
                self.option_data[index['token']] = self._get_options(df, index['token'], index['strike_difference'], index['option_exchange'])
   
    def _load_csv_data(self, index_name):
        # Cache CSVs by index name to avoid repeated loading
        if index_name not in self.csv_cache:
            for idx in self.config['indices']:
                if idx['index_name'] == index_name:
                    try:
                        df = pd.read_csv(idx['trading_symbol_file'])
                        df = df[df['Symbol'] == index_name]
                        self.csv_cache[index_name] = df
                        self.logger.debug(f"Loaded CSV for {index_name}.")
                    except Exception as e:
                        self.logger.error(f"Failed to load CSV for {index_name}: {e}")
                        self.csv_cache[index_name] = None
        return self.csv_cache.get(index_name)

    def _get_options(self, df, index_token, strike_difference, option_exchange):
        nearest_expiry = df['Expiry'].iloc[0]
        #
        ltp = self.tick_collector.ring_buffers[str(index_token)][-1]['ltp'] if str(index_token) in self.tick_collector.ring_buffers and self.tick_collector.ring_buffers[str(index_token)] else None
        
        
        if ltp:
            atm_strike = round(ltp / strike_difference) * strike_difference
            self.logger.debug(f"LTP: {ltp}, ATM Strike: {atm_strike}")
            options = {}
            self.logger.debug(f"Generated options for {index_token}: {options}")
            
            # ATM Options
            atm_data = df[(df['Expiry'] == nearest_expiry) & (df['StrikePrice'] == atm_strike)]
            for _, row in atm_data.iterrows():
                options[f"ATM_{row['OptionType']}"] = {'token': str(row['Token']), 'symbol': row['TradingSymbol']}            
            # ITM and OTM Options
            for option_type in ['CE', 'PE']:
                if option_type == 'CE':
                    itm_options = df[(df['Expiry'] == nearest_expiry) & (df['OptionType'] == option_type) & (df['StrikePrice'] < atm_strike)].nlargest(5, 'StrikePrice')
                    otm_options = df[(df['Expiry'] == nearest_expiry) & (df['OptionType'] == option_type) & (df['StrikePrice'] > atm_strike)].nsmallest(5, 'StrikePrice')
                else:  # PE
                    itm_options = df[(df['Expiry'] == nearest_expiry) & (df['OptionType'] == option_type) & (df['StrikePrice'] > atm_strike)].nsmallest(5, 'StrikePrice')
                    otm_options = df[(df['Expiry'] == nearest_expiry) & (df['OptionType'] == option_type) & (df['StrikePrice'] < atm_strike)].nlargest(5, 'StrikePrice')
                
                for i, row in enumerate(itm_options.itertuples(), start=1):
                    options[f"ITM_{option_type}_{i}"] = {'token': str(row.Token), 'symbol': row.TradingSymbol}
                for i, row in enumerate(otm_options.itertuples(), start=1):
                    options[f"OTM_{option_type}_{i}"] = {'token': str(row.Token), 'symbol': row.TradingSymbol}
            return options
        return {}
    
    def _subscribe_to_options(self):
        """Subscribe to all options for each index at initialization."""
        for index in self.config['indices']:
            index_token = str(index['token'])
            if index_token in self.option_data:
                for option in self.option_data[index_token].values():
                    subscription = f"{index['option_exchange']}|{option['token']}"
                    self.tick_collector.api.subscribe([subscription])
                    self.option_subscriptions.add(option['token'])  # Only numeric part for comparison
                    self.logger.info(f"Subscribed to option {subscription} for index {index_token}")

    async def update_options(self):
        while True:
            for index in self.config['indices']:
                df = self._load_csv_data(index['index_name'])
                if df is not None:
                    new_options = self._get_options(df, index['token'], index['strike_difference'], index['option_exchange'])
                    
                    if new_options:
                        await self._update_option_subscriptions(index, new_options)
                        # Update option_data with new designations but retain existing tokens
                        self.option_data[index['token']] = self._merge_options(self.option_data[index['token']], new_options)

            await asyncio.sleep(10)  # Adjust this interval based on how often you want to check for index movement

    def _merge_options(self, current_options, new_options):
        merged = current_options.copy()
        for key, new_option in new_options.items():
            # If the token exists in current options, update its designation
            for current_key, current_option in list(merged.items()):
                if current_option['token'] == new_option['token'] and current_key != key:
                    merged[key] = merged.pop(current_key)
                    break
            else:
                # If it's a new token, add it
                merged[key] = new_option
        return merged

    async def _update_option_subscriptions(self, index, new_options):
        index_token = str(index['token'])
        for key, new_option in new_options.items():
            subscription = f"{index['option_exchange']}|{new_option['token']}"
            if new_option['token'] not in self.option_subscriptions:
                self.tick_collector.api.subscribe([subscription])
                self.option_subscriptions.add(new_option['token'])
                self.logger.info(f"Subscribed to new option {subscription} for index {index_token}")

    
    ### simple with ask bid -works
    async def handle_option_tick(self, token, tick):
        for index_token, options in self.option_data.items():
            for option in options.values():
                if option['token'] == token:
                    if index_token not in self.option_ticks:
                        self.option_ticks[index_token] = {}
                    if token not in self.option_ticks[index_token]:
                        self.option_ticks[index_token][token] = deque(maxlen=200)
                    
                    # Store the comprehensive tick data with all market depth levels
                    self.option_ticks[index_token][token].append(tick)
                    
                    # Optional: Log detailed market depth if debug is enabled
                    if self.logger.isEnabledFor(logging.DEBUG):
                        # Create a summary of the market depth info
                        bid_info = []
                        ask_info = []
                        
                        for i in range(1, 6):
                            if f'bp{i}' in tick and f'bq{i}' in tick:
                                bid_info.append(f"L{i}:{tick[f'bp{i}']}x{tick[f'bq{i}']}")
                            if f'sp{i}' in tick and f'sq{i}' in tick:
                                ask_info.append(f"L{i}:{tick[f'sp{i}']}x{tick[f'sq{i}']}")
                        
                        depth_info = f"Bids: {', '.join(bid_info)} | Asks: {', '.join(ask_info)}"
                        vol_info = f"Vol: {tick.get('v', 'N/A')}"
                        
                        self.logger.debug(f"Updated option tick for {index_token} with token {token}: LTP:{tick['ltp']} {depth_info} {vol_info}")
                    return
                    
        if token not in self.warned_tokens:
            self.logger.warning(f"Received tick for unknown option token: {token}")
            self.warned_tokens.add(token)
    
class StrategyManager:
    def __init__(self, tick_collector, trade_manager):       
        self.tick_collector = tick_collector
        self.indicator_manager = tick_collector.resampler.indicator_manager
        self.strategies = {}
        self.monitored_keys = {}
        self.trade_manager = trade_manager
        self.trade_manager.strategy_manager = self
        self.trade_controller = TradeController(self)  # Initialize and attach TradeController
        
        # Add a logger
        self.logger = logging.getLogger("StrategyManager")
        

    async def add_strategy(self, token, timeframe, strategy_id, indicators, 
                         strategy_logic, default_sl=None, default_target=None):
        if token not in self.tick_collector.resampling_enabled:
            raise ValueError(f"Token {token} is not being resampled.")
        if timeframe not in self.tick_collector.resampling_enabled[token]:
            raise ValueError(f"Timeframe {timeframe} is not being resampled for Token {token}.")

        for indicator in indicators:
            base_indicator = indicator.rsplit('_', 1)[0]
            if base_indicator not in self.indicator_manager.configs:
                raise ValueError(f"Indicator {indicator} is not registered.")
            valid_timeframes = self.indicator_manager.configs[base_indicator].timeframes
            if timeframe not in valid_timeframes:
                raise ValueError(
                    f"Indicator {indicator} does not support Timeframe {timeframe}. "
                    f"Supported timeframes: {valid_timeframes}"
                )

        strategy_key = (token, timeframe, strategy_id)
        if strategy_key not in self.strategies:
            self.strategies[strategy_key] = {
                "indicators": indicators,
                "logic": strategy_logic,
                "enabled": True,
                "task": None,
                "default_sl": default_sl,
                "default_target": default_target
            }

        for indicator in indicators:
            key = (token, timeframe, indicator)
            self.monitored_keys[key] = {"latest": None, "previous": None}
            if key not in self.indicator_manager.subscriptions:
                await self.indicator_manager.subscribe(token, timeframe, indicator)

        task = asyncio.create_task(self.monitor_indicators(token, timeframe, indicators, strategy_id))
        self.strategies[strategy_key]["task"] = task

        asyncio.create_task(broadcast_strategy_updates())
    
    ### only call logic if indicator  value change and is not candle height
    async def monitor_indicators(self, token, timeframe, indicators, strategy_id):
        keys = [(token, timeframe, indicator) for indicator in indicators]
        while True:
            for key in keys:
                if key in self.indicator_manager.subscriptions:
                    deque_data = self.indicator_manager.subscriptions[key]
                    if deque_data:
                        timestamp, current_value = deque_data[-1]
                        state = self.monitored_keys[key]
                        state["previous"] = state["latest"]
                        if state["latest"] != current_value:
                            state["latest"] = current_value
                            # Check if the changed indicator is not 'candle_height_1'
                            if key[2] != 'candle_height_1':
                                strategy = self.strategies.get((token, timeframe, strategy_id))
                                if strategy and strategy["enabled"]:
                                    await self.evaluate_strategy_logic(token, timeframe, strategy_id, indicators)
            await asyncio.sleep(0.1)

    async def evaluate_strategy_logic(self, token, timeframe, strategy_id, indicators):
        strategy = self.strategies.get((token, timeframe, strategy_id))
        if not strategy or not strategy["enabled"]:
            return
            
        states = {
            indicator: self.monitored_keys[(token, timeframe, indicator)]
            for indicator in indicators
        }
        await strategy["logic"](token, timeframe, strategy_id, states, 
                              self.tick_collector, self.trade_manager)

    async def enable_strategy(self, token, timeframe, strategy_id):
        if (token, timeframe, strategy_id) in self.strategies:
            self.strategies[(token, timeframe, strategy_id)]["enabled"] = True
      
            asyncio.create_task(broadcast_strategy_updates())
            self.logger.info(f"Strategy {strategy_id} for Token: {token}, Timeframe: {timeframe} enabled.")

    async def disable_strategy(self, token, timeframe, strategy_id):
        if (token, timeframe, strategy_id) in self.strategies:
            self.strategies[(token, timeframe, strategy_id)]["enabled"] = False

            asyncio.create_task(broadcast_strategy_updates())
            self.logger.info(f"Strategy {strategy_id} for Token: {token}, Timeframe: {timeframe} disabled.")

    async def stop_strategy(self, token, timeframe, strategy_id):
        if (token, timeframe, strategy_id) in self.strategies:
            strategy = self.strategies[(token, timeframe, strategy_id)]
            if strategy["task"]:
                strategy["task"].cancel()
            del self.strategies[(token, timeframe, strategy_id)]

            asyncio.create_task(broadcast_strategy_updates())
import math   
import statistics      
@dataclass
class Trade:     
    index_token: str
    timeframe: str
    option_token: str
    option_symbol: str
    action: str
    entry_price: float
    entry_time: str
    order_id: str
    default_sl: Optional[float] = None
    default_target: Optional[float] = None
    trail_sl: Optional[float] = None
    trail_sl_trigger: Optional[float] = None
    max_profit: float = 0
    current_pnl: float = 0
    trade_history: List[Dict] = field(default_factory=list)
    exit_reason: Optional[str] = None
    exit_price: Optional[float] = None
    exit_time: Optional[str] = None
    highest_price: float = field(init=False)  # New field
    
    def __post_init__(self):
        if self.trade_history is None:
            self.trade_history = []    
        self.highest_price = self.entry_price  # Initialize with entry price
        
        self._record_event("Trade Opened", details={
            "entry_price": self.entry_price,
            "option_symbol": self.option_symbol,
            "action": self.action,
            "timeframe": self.timeframe
        })
               
    @property
    def trade_key(self):
        return f"{self.order_id}_{self.option_symbol}"

    def _record_event(self, event_type, details=None):
        if details is None:
            details = {}
        event = {
            "timestamp": datetime.now().isoformat(),
            "event_type": event_type,
            **details
        }
        self.trade_history.append(event)

    def update_pnl(self, current_price: float):
        
        if self.exit_price is not None:
            return  # 🚨 Prevent updates on closed trades!
    
        multiplier = 1 if self.action == "BUY" else 1  # Fix for SELL trades
        new_pnl = (current_price - self.entry_price) * multiplier
        
        if new_pnl != self.current_pnl:  # Only update if there's a change
            self.current_pnl = new_pnl
            self.max_profit = max(self.max_profit, self.current_pnl)
            
            self._record_event("PnL Update", details={
                "current_price": current_price,
                "current_pnl": self.current_pnl,
                "max_profit": self.max_profit
            })
            
            # Send real-time updates to UI
            asyncio.create_task(broadcast_trade_updates())

        return self.current_pnl

class TradeManager:
    def __init__(self, tick_collector, option_manager):
        self.active_trades: Dict[str, Trade] = {}
        self.closed_trades: List[Trade] = []
        self.tick_collector = tick_collector
        self.option_manager = option_manager
        self.monitoring_tasks = {}
        self.pnl_tasks = {}
        self.strategy_manager = None
        self.total_pnl = 0
        self.daily_pnl = defaultdict(float)
        
        self.trade_lock = asyncio.Lock()
        self.signal_lock = Lock()
        #self.active_trades: Dict[str, Any] = {}
        
        # Add a logger
        self.logger = logging.getLogger("TradeManager")
    
    def _generate_unique_order_id(self):
        while True:
            order_id = f"ORD-{random.randint(10000, 99999)}"
            if not any(trade.order_id == order_id for trade in self.active_trades.values()):
                return order_id

    async def execute_trade(self, index_token: str, timeframe: str, option_token: str, 
                          option_symbol: str, action: str, price: float, 
                          timestamp: str, default_sl: float = None, 
                          default_target: float = None) -> Optional[str]:
        index_token = str(index_token)
        if self.check_active_trade(index_token, timeframe):
            self.logger.info(f"Active trade exists for {index_token} on {timeframe}")
            return None

        order_id = self._generate_unique_order_id()
        new_trade = Trade(
            index_token=index_token,
            timeframe=timeframe,
            option_token=option_token,
            option_symbol=option_symbol,
            action=action,
            entry_price=price,
            entry_time=timestamp,
            order_id=order_id,
            default_sl=default_sl,
            default_target=default_target
        )
        
        if default_sl is not None:
            new_trade.trail_sl = default_sl
            new_trade.trail_sl_trigger = new_trade.entry_price - new_trade.trail_sl
            new_trade._record_event("Trail SL Set", details={"trail_sl_trigger": new_trade.trail_sl_trigger})
            self.logger.info(f"Trail SL set for {new_trade.trade_key} at {new_trade.trail_sl_trigger}")

        self.active_trades[new_trade.trade_key] = new_trade
        self.logger.info(f"Trade executed: {action} {option_symbol} ({option_token}) "
                        f"Index: {index_token} TF: {timeframe} Order: {order_id} "
                        f"Price: {price} Time: {timestamp}")
        
        self.monitoring_tasks[new_trade.trade_key] = asyncio.create_task(self._monitor_trade(new_trade.trade_key))
        
        self.pnl_tasks[new_trade.trade_key] = asyncio.create_task(
            self._update_trade_pnl(new_trade.trade_key)
        )
               
        asyncio.create_task(broadcast_trade_updates())
        return order_id
    
    async def _update_trade_pnl(self, trade_key: str):
        while True:
            async with self.trade_lock:  # 🔒 Ensures safe access
                if trade_key not in self.active_trades:
                    break
                trade = self.active_trades[trade_key]

            # Fetch the latest price
            option_ticks = self.option_manager.option_ticks.get(
                int(trade.index_token), {}
            ).get(trade.option_token, deque())
            if option_ticks:
                current_price = option_ticks[-1]['ltp']
                trade.update_pnl(current_price)

            await asyncio.sleep(0.1)

    async def _monitor_trade(self, trade_key: str):
        while trade_key in self.active_trades:
            trade = self.active_trades[trade_key]
            option_ticks = self.option_manager.option_ticks.get(
                int(trade.index_token), {}
            ).get(trade.option_token, deque())
            
            if option_ticks:
                current_price = option_ticks[-1]['ltp']
                current_tick_time = option_ticks[-1]['tt']
                
                ### new add - Update highest_price for trailing logic
                if current_price > trade.highest_price:  ### new add
                    trade.highest_price = current_price  ### new add
                
                await self._check_sl_target(trade_key, current_price)
                await self._check_trailing_sl(trade_key, current_price)
                # Broadcast changes
                asyncio.create_task(broadcast_trade_updates())
            await asyncio.sleep(.5)
            
    async def _check_sl_target(self, trade_key: str, current_price: float):
        trade = self.active_trades.get(trade_key)
        if not trade:
            return

        if trade.action == "BUY":
            if (trade.default_sl and 
                current_price <= (trade.entry_price - trade.default_sl)):
                trade.exit_reason = "SL Hit"
                await self.close_trade(
                    trade.index_token, trade.timeframe, trade.option_symbol,
                    trade.option_token, trade.order_id, current_price,
                    datetime.now().isoformat()
                )
            elif (trade.default_target and 
                  current_price >= (trade.entry_price + trade.default_target)):
                trade.exit_reason = "Target Hit"
                await self.close_trade(
                    trade.index_token, trade.timeframe, trade.option_symbol,
                    trade.option_token, trade.order_id, current_price,
                    datetime.now().isoformat()
                )
        else:  # SELL
            if (trade.default_sl and 
                current_price <= (trade.entry_price - trade.default_sl)):
                trade.exit_reason = "SL Hit"
                await self.close_trade(
                    trade.index_token, trade.timeframe, trade.option_symbol,
                    trade.option_token, trade.order_id, current_price,
                    datetime.now().isoformat()
                )
            elif (trade.default_target and 
                  current_price >= (trade.entry_price + trade.default_target)):
                trade.exit_reason = "Target Hit"
                await self.close_trade(
                    trade.index_token, trade.timeframe, trade.option_symbol,
                    trade.option_token, trade.order_id, current_price,
                    datetime.now().isoformat()
                )

    
    async def _check_trailing_sl(self, trade_key: str, current_price: float):
        pass
    # async def _check_trailing_sl(self, trade_key: str, current_price: float):
    #     trade = self.active_trades.get(trade_key)
    #     if not trade or trade.exit_price is not None:
    #         return

    #     # Get tick data (ltp is option premium)
    #     option_ticks = self.option_manager.option_ticks.get(
    #         int(trade.index_token), {}
    #     ).get(trade.option_token, deque())
        
    #     if len(option_ticks) < 10:
    #         return
        
    #     tf_seconds = self._parse_timeframe_to_seconds(trade.timeframe)
        
    #     # Initialize trade metrics
    #     if not hasattr(trade, 'trade_metrics'):
    #         trade.trade_metrics = {
    #             'entry_time': datetime.now(),
    #             'phase': 'evaluation',
    #             'volatility_short': 0,
    #             'volatility_long': 0,
    #             'avg_range_short': 0,
    #             'avg_range_long': 0,
    #             'confirm_count': 0,
    #             'profit_milestones': [0.1, 0.2, 0.5, 1.0, 2.0],
    #             'milestone_reached': 0,
    #             'market_condition': 'unknown',
    #             'price_history': deque(maxlen=max(150, tf_seconds // 2)),
    #             'last_tick_count': 0,
    #             'evaluation_period': max(15, min(tf_seconds // 4, 60)),
    #             'trailing_ratio': 2.0,
    #             'stop_moved_count': 0,
    #             'last_price_peak': trade.entry_price,
    #             'price_momentum': 0,
    #             'initial_trigger_set': False  # Flag to track initial reset
    #         }
    #         trade._record_event("Trailing SL Initialized", details={
    #             "entry_price": trade.entry_price,
    #             "timeframe": trade.timeframe,
    #             "default_sl": trade.default_sl,
    #             "initial_trail_sl_trigger": trade.trail_sl_trigger,
    #             "action": trade.action
    #         })
    #         self.logger.info(f"Trailing SL initialized for {trade_key} with trigger: {trade.trail_sl_trigger}")
        
    #     metrics = trade.trade_metrics
    #     current_tick_count = len(option_ticks)
        
    #     if current_tick_count > metrics['last_tick_count']:
    #         new_ticks = list(option_ticks)[metrics['last_tick_count'] - current_tick_count:]
    #         for tick in new_ticks:
    #             price = tick['ltp']
    #             metrics['price_history'].append(price)
    #         metrics['last_tick_count'] = current_tick_count
        
    #     # Profit calculation (premium-based for call/put buys)
    #     current_profit_pct = (current_price / trade.entry_price - 1) * 100
        
    #     # Update metrics
    #     elapsed_seconds = (datetime.now() - metrics['entry_time']).total_seconds()
    #     self._update_trade_phase(trade, current_profit_pct, elapsed_seconds, tf_seconds)
    #     self._calculate_market_metrics(trade, option_ticks, tf_seconds)
    #     self._update_price_momentum(trade, current_price)
        
    #     # Calculate adaptive trailing distance
    #     trail_distance = self._calculate_adaptive_trail_distance(trade, current_price, tf_seconds)
        
    #     # Reset initial trigger if too wide (only on first pass with enough data)
    #     if not metrics['initial_trigger_set'] and len(option_ticks) >= 10:
    #         initial_distance = trade.entry_price - trade.trail_sl_trigger  # Distance from entry
    #         volatility = metrics['volatility_short'] if metrics['volatility_short'] > 0 else statistics.stdev([tick['ltp'] for tick in list(option_ticks)[-10:]])
    #         pa_initial_distance = max(volatility * 2.0, trade.entry_price * 0.005)  # 2× volatility or 0.5% of entry
            
    #         if initial_distance > volatility * 3.0:  # If default_sl is > 3× volatility, reset
    #             new_initial_trigger = trade.entry_price - pa_initial_distance
    #             if new_initial_trigger > trade.trail_sl_trigger:  # Only reset if tighter
    #                 trade.trail_sl_trigger = new_initial_trigger
    #                 trade.trail_sl = pa_initial_distance
    #                 trade._record_event("Initial Trigger Reset", details={
    #                     "old_trigger": trade.entry_price - initial_distance,
    #                     "new_trigger": trade.trail_sl_trigger,
    #                     "pa_distance": pa_initial_distance,
    #                     "volatility": volatility,
    #                     "action": trade.action
    #                 })
    #                 self.logger.info(
    #                     f"Reset trail_sl_trigger for {trade_key} to {trade.trail_sl_trigger:.2f} "
    #                     f"based on PA (Volatility: {volatility:.2f}, Default SL was {initial_distance})"
    #                 )
    #         metrics['initial_trigger_set'] = True
        
    #     # New trigger trails upward as premium rises
    #     new_trigger = trade.highest_price - trail_distance
        
    #     # Adaptive update threshold
    #     improvement_threshold = self._get_adaptive_improvement_threshold(trade, current_price)
    #     significant_improvement = (
    #         ((new_trigger - trade.trail_sl_trigger) / trade.trail_sl_trigger) * 100 >= improvement_threshold
    #     )
        
    #     # Price action condition
    #     should_update_stop = (
    #         abs(metrics['price_momentum']) > metrics['volatility_short'] * 1.5 or
    #         current_profit_pct > 50 or
    #         (metrics['phase'] == 'evaluation' and current_profit_pct <= 0)
    #     )
        
    #     # Update trigger if it tightens
    #     if new_trigger > trade.trail_sl_trigger and (should_update_stop or significant_improvement):
    #         old_trigger = trade.trail_sl_trigger
    #         trade.trail_sl_trigger = new_trigger
    #         trade.trail_sl = trade.highest_price - trade.trail_sl_trigger
    #         metrics['stop_moved_count'] += 1
    #         if current_price > metrics['last_price_peak']:
    #             metrics['last_price_peak'] = current_price
            
    #         trade._record_event("Trigger Updated", details={
    #             "new_trigger": trade.trail_sl_trigger,
    #             "old_trigger": old_trigger,
    #             "trail_sl": trade.trail_sl,
    #             "phase": metrics['phase'],
    #             "profit_pct": current_profit_pct,
    #             "momentum": metrics['price_momentum'],
    #             "action": trade.action
    #         })
    #         self.logger.info(
    #             f"Trail_sl_trigger for {trade_key} updated to {trade.trail_sl_trigger:.2f} "
    #             f"(Phase: {metrics['phase']}, Profit: {current_profit_pct:.1f}%, "
    #             f"Trail Distance: {trade.trail_sl:.2f}, Action: {trade.action})"
    #         )
    #         asyncio.create_task(broadcast_trade_updates())
        
    #     # Check stop hit (premium drops below trigger)
    #     required_confirms = self._get_required_confirmations(trade, tf_seconds)
    #     price_discount_pct = ((trade.highest_price - current_price) / trade.highest_price) * 100
        
    #     if current_price <= trade.trail_sl_trigger:
    #         if price_discount_pct > 8 and metrics['phase'] in ['established', 'runner']:
    #             metrics['confirm_count'] = required_confirms
    #         metrics['confirm_count'] += 1
            
    #         if metrics['confirm_count'] >= required_confirms:
    #             trade.exit_reason = f"Trailing SL Hit ({metrics['phase']} phase)"
    #             await self.close_trade(
    #                 trade.index_token, trade.timeframe, trade.option_symbol,
    #                 trade.option_token, trade.order_id, current_price,
    #                 datetime.now().isoformat()
    #             )
    #             self.logger.info(
    #                 f"Trade {trade_key} exited at {current_price:.2f} - "
    #                 f"Trailing SL Hit ({metrics['phase']} phase, {current_profit_pct:.1f}% profit, Action: {trade.action})"
    #             )
    #             asyncio.create_task(broadcast_trade_updates())
    #         else:
    #             self.logger.debug(
    #                 f"Trail SL hit {metrics['confirm_count']}/{required_confirms} "
    #                 f"for {trade_key} ({metrics['phase']} phase)"
    #             )
    #     else:
    #         metrics['confirm_count'] = max(0, metrics['confirm_count'] - 2)

    # Supporting Functions
    def _parse_timeframe_to_seconds(self, timeframe):
        """Convert timeframe string to seconds"""
        try:
            if isinstance(timeframe, (int, float)):
                return int(timeframe)
            if timeframe.endswith('s'):
                return int(timeframe[:-1])
            elif timeframe.endswith('m'):
                return int(timeframe[:-1]) * 60
            elif timeframe.endswith('h'):
                return int(timeframe[:-1]) * 3600
            else:
                return int(timeframe)
        except (ValueError, AttributeError):
            self.logger.warning(f"Couldn't parse timeframe '{timeframe}', using default 60s")
            return 60

    def _update_trade_phase(self, trade, current_profit_pct, elapsed_seconds, tf_seconds):
        """Update trade phase based on profit and time"""
        metrics = trade.trade_metrics
        
        for i, milestone in enumerate(metrics['profit_milestones']):
            if current_profit_pct >= milestone * 100 and i > metrics['milestone_reached']:
                metrics['milestone_reached'] = i
                trade._record_event("Profit Milestone", details={
                    "milestone": f"{milestone * 100}%",
                    "current_profit": f"{current_profit_pct:.1f}%",
                    "elapsed_time": f"{elapsed_seconds:.0f}s"
                })
        
        if metrics['phase'] == 'evaluation' and elapsed_seconds < metrics['evaluation_period']:
            return
        
        if current_profit_pct <= -5 and elapsed_seconds < tf_seconds * 3:
            new_phase = 'early'
        elif current_profit_pct >= 80 or metrics['milestone_reached'] >= 3:
            new_phase = 'runner'
        elif current_profit_pct > 15 or elapsed_seconds > tf_seconds * 5:
            new_phase = 'established'
        elif metrics['phase'] == 'evaluation':
            new_phase = 'early'
        else:
            new_phase = metrics['phase']
        
        if new_phase != metrics['phase']:  # Fixed typo from 'pahse'
            trade._record_event("Phase Change", details={
                "old_phase": metrics['phase'],
                "new_phase": new_phase,
                "profit_pct": f"{current_profit_pct:.1f}%",
                "elapsed_time": f"{elapsed_seconds:.0f}s"
            })
            metrics['phase'] = new_phase

    def _calculate_market_metrics(self, trade, option_ticks, tf_seconds):
        """Calculate market metrics without linregress"""
        metrics = trade.trade_metrics
        
        short_window = max(10, min(40, tf_seconds // 5))
        long_window = max(40, min(200, tf_seconds))
        
        recent_ticks = list(option_ticks)
        if len(recent_ticks) < short_window:
            return
        
        recent_prices = [tick['ltp'] for tick in recent_ticks]
        short_prices = recent_prices[-short_window:]
        long_prices = recent_prices[-min(len(recent_prices), long_window):]
        
        short_ranges = [abs(short_prices[i] - short_prices[i-1]) for i in range(1, len(short_prices))]
        long_ranges = [abs(long_prices[i] - long_prices[i-1]) for i in range(1, len(long_prices))]
        
        metrics['avg_range_short'] = sum(short_ranges) / len(short_ranges) if short_ranges else 0
        metrics['avg_range_long'] = sum(long_ranges) / len(long_ranges) if long_ranges else 0
        
        metrics['volatility_short'] = statistics.stdev(short_prices) if len(short_prices) > 1 else 0
        metrics['volatility_long'] = statistics.stdev(long_prices) if len(long_prices) > 1 else 0
        
        if len(short_prices) > 10 and len(long_prices) > 20:
            short_direction = sum(1 if short_prices[i] > short_prices[i-1] else -1 for i in range(1, len(short_prices)))
            trend_strength = abs(short_direction) / (len(short_prices) - 1)
            
            rel_volatility = metrics['volatility_short'] / metrics['volatility_long'] if metrics['volatility_long'] else 1
            
            if trend_strength > 0.7:
                metrics['market_condition'] = 'trending'
            elif rel_volatility > 1.3:
                metrics['market_condition'] = 'volatile'
            else:
                metrics['market_condition'] = 'ranging'

    def _update_price_momentum(self, trade, current_price):
        """Calculate momentum without linregress"""
        metrics = trade.trade_metrics
        price_history = list(metrics['price_history'])
        
        if len(price_history) < 5:
            metrics['price_momentum'] = 0
            return
        
        recent_prices = price_history[-5:]
        start_price = recent_prices[0]
        end_price = recent_prices[-1]
        
        if start_price != 0:
            metrics['price_momentum'] = (end_price - start_price) / start_price
        else:
            metrics['price_momentum'] = 0

    def _get_adaptive_improvement_threshold(self, trade, current_price):
        """Adaptive threshold for trigger updates"""
        metrics = trade.trade_metrics
        current_profit_pct = (current_price / trade.entry_price - 1) * 100
        
        base_threshold = max(0.3, metrics['volatility_short'] / current_price * 100)
        profit_factor = max(0.5, 1.0 - (current_profit_pct / 200))
        market_factor = 1.5 if metrics['market_condition'] == 'volatile' else 0.8 if metrics['market_condition'] == 'trending' else 1.0
        return base_threshold * profit_factor * market_factor

    def _calculate_adaptive_trail_distance(self, trade, current_price, tf_seconds):
        """Calculate adaptive trail distance"""
        metrics = trade.trade_metrics
        current_profit_pct = (current_price / trade.entry_price - 1) * 100
        
        base_multiplier = {
            'evaluation': 6.0,
            'early': 4.0,
            'established': 3.5,
            'runner': 3.0
        }.get(metrics['phase'], 3.0)
        
        market_factor = 1.8 if metrics['market_condition'] == 'volatile' else 1.4 if metrics['market_condition'] == 'ranging' else 1.0
        momentum_factor = max(0.8, min(1.5, 1.0 + abs(metrics['price_momentum']) * 10))
        tf_factor = math.pow(tf_seconds / 60, 1/3) * 1.2
        
        metrics['trailing_ratio'] = base_multiplier * market_factor * momentum_factor * tf_factor
        base_distance = max(metrics['volatility_short'] * 2.5, metrics['avg_range_short'] * 3.5)
        trail_distance = base_distance * metrics['trailing_ratio']
        
        min_distance = max(current_price * 0.005 * tf_factor, metrics['volatility_short'] * 2.0)
        if current_profit_pct > 100:
            tightening_factor = 1.0 - min(0.4, (current_profit_pct - 100) / 250)
            trail_distance *= tightening_factor
        
        # Don't enforce default_sl here; it's handled by _check_sl_target
        return max(min_distance, trail_distance)

    def _get_required_confirmations(self, trade, tf_seconds):
        """Determine required confirmations"""
        metrics = trade.trade_metrics
        
        base_confirms = {
            'evaluation': 6,
            'early': 5,
            'established': 4,
            'runner': 3
        }.get(metrics['phase'], 3)
        
        tf_factor = max(0.5, min(1.5, math.pow(60 / tf_seconds, 1/3))) if tf_seconds > 0 else 1
        market_factor = 1.4 if metrics['market_condition'] == 'volatile' else 1.2 if metrics['market_condition'] == 'ranging' else 1.0
        
        return max(2, round(base_confirms * tf_factor * market_factor))
    
            
            
    def update_sl(self, trade_key: str, new_sl: float):
        trade = self.active_trades.get(trade_key)
        if trade:
            trade.default_sl = new_sl
            trade._record_event("SL Updated", details={"new_sl": new_sl})
            self.logger.info(f"Updated SL for {trade_key} to {new_sl}")
            # Notify clients of update
            asyncio.create_task(broadcast_trade_updates())

    def update_target(self, trade_key: str, new_target: float):
        trade = self.active_trades.get(trade_key)
        if trade:
            trade.default_target = new_target
            trade._record_event("Target Updated", details={"new_target": new_target})
            self.logger.info(f"Updated target for {trade_key} to {new_target}")
            # Notify clients of update
            asyncio.create_task(broadcast_trade_updates())

    def set_trailing_sl(self, trade_key: str, trail_sl: float):
        trade = self.active_trades.get(trade_key)
        if trade:
            trade.trail_sl = trail_sl
            trade.trail_sl_trigger = None
            trade._record_event("Trail SL Set", details={"trail_sl": trail_sl})
            self.logger.info(f"Set trailing SL for {trade_key} to {trail_sl}")
            # Notify clients of update
            asyncio.create_task(broadcast_trade_updates())
            

    async def exit_all_trades(self):
        trade_keys = list(self.active_trades.keys())
        for trade_key in trade_keys:
            trade = self.active_trades[trade_key]
            option_ticks = self.option_manager.option_ticks.get(
                int(trade.index_token), {}
            ).get(trade.option_token, deque())
            
            if option_ticks:
                current_price = option_ticks[-1]['ltp']
                trade.exit_reason = "Force Exit"
                await self.close_trade(
                    trade.index_token, trade.timeframe, trade.option_symbol,
                    trade.option_token, trade.order_id, current_price,
                    datetime.now().isoformat()
                )
        asyncio.create_task(broadcast_trade_updates())

    async def get_active_positions(self) -> list:
        positions = []
        for trade in self.active_trades.values():
            positions.append({
                "trade_id": trade.trade_key,
                "symbol": trade.option_symbol,
                "action": trade.action,
                "entry_price": trade.entry_price,
                "current_pnl": trade.current_pnl,
                "max_profit": trade.max_profit,
                "sl": trade.default_sl,
                "target": trade.default_target,
                'trail_sl_trigger': trade.trail_sl_trigger,
                "trail_sl": trade.trail_sl
            })
        return positions

    async def get_trade_history(self, trade_key: str) -> list:
        trade = self.active_trades.get(trade_key)
        if trade:
            return trade.trade_history
        
        for closed_trade in self.closed_trades:
            if closed_trade.trade_key == trade_key:
                return closed_trade.trade_history
        return []

    async def get_trade_history_display(self):
        all_trades = self.closed_trades + list(self.active_trades.values())
        if not all_trades:
            return "No trades found"
            
        trade_history = []
        total_pnl = 0
        
        for trade in all_trades:
            trade_info = {
                "Trade Key": trade.trade_key,
                "Date": trade.entry_time.split('T')[0],
                "Time": trade.entry_time.split('T')[1][:8],
                "Symbol": trade.option_symbol,
                "Signal": trade.action,
                "Entry": f"{trade.entry_price:.2f}",
                "Exit": f"{trade.exit_price:.2f}" if trade.exit_price else "Active",
                "Exit Time": trade.exit_time.split('T')[1][:8] if trade.exit_time else "-",
                "P&L": f"{trade.current_pnl:.2f}",
                "Status": "Closed" if trade in self.closed_trades else "Active",
                "Exit Reason": trade.exit_reason if trade.exit_reason else "-"
            }
            trade_history.append(trade_info)
            if trade.exit_price:
                total_pnl += trade.current_pnl
        
        trade_history.sort(key=lambda x: x['Date'] + x['Time'], reverse=True)
        
        headers = ["Trade Key", "Date", "Time", "Symbol", "Signal", "Entry", "Exit", "Exit Time", "P&L", "Status", "Exit Reason"]        
        output = "\nTrade History:\n" + "-" * 150 + "\n"
        output += "{:<25} {:<12} {:<10} {:<15} {:<8} {:<10} {:<10} {:<12} {:<12} {:<8} {:<15}\n".format(*headers)
        output += "-" * 150 + "\n"        
        for trade in trade_history:
            output += "{Trade Key:<25} {Date:<12} {Time:<10} {Symbol:<15} {Signal:<8} {Entry:<10} {Exit:<10} {Exit Time:<12} {P&L:<12} {Status:<8} {Exit Reason:<15}\n".format(**trade)
        
        output += "-" * 150 + "\n"
        output += f"Total P&L: {total_pnl:.2f}\n"        
        return output

        
    async def close_trade(self, index_token: str, timeframe: str, option_symbol: str,
                      option_token: str = None, order_id: str = None,
                      price: float = None, timestamp: str = None):
        async with self.trade_lock:  # 🔒 Ensures safe modification
            trade_key = next(
                (key for key, trade in self.active_trades.items()
                if trade.order_id == order_id and trade.option_symbol == option_symbol),
                None
            )
            if trade_key and trade_key in self.active_trades:
                trade = self.active_trades[trade_key]
                trade.exit_price = price
                trade.exit_time = timestamp
                trade.exit_reason = "Normal Exit" if not trade.exit_reason else trade.exit_reason

                # Cancel tasks safely
                task_monitor = self.monitoring_tasks.pop(trade_key, None)
                task_pnl = self.pnl_tasks.pop(trade_key, None)
                
                if task_monitor:
                    task_monitor.cancel()
                    try:
                        await task_monitor  # Wait for cancellation
                    except asyncio.CancelledError:
                        pass
                
                if task_pnl:
                    task_pnl.cancel()
                    try:
                        await task_pnl  # Wait for cancellation
                    except asyncio.CancelledError:
                        pass

                self.closed_trades.append(trade)
                del self.active_trades[trade_key]

                self.logger.info(f"Closed trade: {trade_key} at {price}")

                await broadcast_trade_updates()


    def check_active_trade(self, index_token: str, timeframe: str) -> bool:
        index_token = str(index_token)
        return any(
            str(trade.index_token) == index_token and trade.timeframe == timeframe
            for trade in self.active_trades.values()
        )

class TradeController:
    def __init__(self, strategy_manager):
        self.strategy_actions = {}  # Stores manual buy/sell permissions for each strategy
        self.strategy_manager = strategy_manager
        self.strategy_manager.trade_controller = self  # Attach to StrategyManager
        print("TradeController initialized and attached to StrategyManager.")

    def set_manual_control(self, strategy_key, enable_buy, enable_sell):        
    
        # Check if the strategy is enabled in StrategyManager
        strategy = self.strategy_manager.strategies.get(strategy_key)
        if not strategy or not strategy["enabled"]:
            print(f"Cannot set manual control: Strategy {strategy_key} is not enabled.")
            return

        # Set manual control
        if strategy_key not in self.strategy_actions:
            self.strategy_actions[strategy_key] = {"enable_buy": True, "enable_sell": True}
        self.strategy_actions[strategy_key]["enable_buy"] = enable_buy
        self.strategy_actions[strategy_key]["enable_sell"] = enable_sell
        print(f"Manual control updated for {strategy_key}: BUY={enable_buy}, SELL={enable_sell}")        
        asyncio.create_task(broadcast_strategy_updates())

    def is_buy_allowed(self, strategy_key):
        strategy = self.strategy_manager.strategies.get(strategy_key)
        if not strategy or not strategy["enabled"]:
            print(f"Cannot set manual control: Strategy {strategy_key} is not enabled.")
            return
        """Check if BUY is allowed based on manual control."""
        return self.strategy_actions.get(strategy_key, {"enable_buy": True})["enable_buy"]

    def is_sell_allowed(self, strategy_key):
        strategy = self.strategy_manager.strategies.get(strategy_key)
        if not strategy or not strategy["enabled"]:
            print(f"Cannot set manual control: Strategy {strategy_key} is not enabled.")
            return
        """Check if SELL is allowed based on manual control."""
        return self.strategy_actions.get(strategy_key, {"enable_sell": True})["enable_sell"]
    

## with 5 minute-no supertrend logic
async def bull_and_bear_trade_logic(token: str, timeframe: str, strategy_id: str, states: Dict,
                                   tick_collector: Any, trade_manager: Any):
    """Enhanced trade logic with specific JMA and Supertrend signal combinations"""
    
    logger = logging.getLogger("Logic")
    
    if not hasattr(bull_and_bear_trade_logic, 'monitoring_status'):
        bull_and_bear_trade_logic.monitoring_status = {}
    monitoring_status = bull_and_bear_trade_logic.monitoring_status
    
    # Get all required states and indicators
    supertrend = states.get("supertrend_1", {"latest": None, "previous": None})
    jma = states.get("jma_1", {"latest": None, "previous": None})
    
    # Basic validations
    latest_tick = tick_collector.ring_buffers.get(token, [None])[-1]
    if not latest_tick:
        return
        
    latest_price = latest_tick["ltp"]
    latest_time = latest_tick["tt"]
    
    # Get required managers and data
    option_manager = tick_collector.option_manager
    option_data = option_manager.option_data.get(int(token), {})
    option_ticks = option_manager.option_ticks.get(int(token), {})
    strategy = trade_manager.strategy_manager.strategies.get((token, timeframe, strategy_id))
    
    strategy_key = (token, timeframe, strategy_id)
    trade_controller = trade_manager.strategy_manager.trade_controller
    buy_allowed = trade_controller.is_buy_allowed(strategy_key)
    sell_allowed = trade_controller.is_sell_allowed(strategy_key)

    monitor_lock = asyncio.Lock()

    async def execute_option_action(action: str) -> None:
        """Execute a new trade with the given action (BUY/SELL)"""
        try:
            option_type = "CE" if action == "BUY" else "PE"
            atm_option = option_data.get(f"ATM_{option_type}")
            
            if not atm_option:
                logger.warning(f"No ATM option found for {option_type}")
                return
                
            option_token = atm_option['token']
            option_symbol = atm_option['symbol']
            
            option_ticks_data = option_ticks.get(option_token, [])
            if option_ticks_data and latest_time:
                option_ltp = option_ticks_data[-1]['ltp']
                option_ltp_time = option_ticks_data[-1]['tt']
                await trade_manager.execute_trade(
                    token, timeframe, option_token, option_symbol,
                    action, option_ltp, option_ltp_time,
                    strategy['default_sl'], strategy['default_target']
                )
            else:
                logger.warning(f"No option ticks data available for {option_symbol}")
        except Exception as e:
            logger.error(f"Error in execute_option_action: {e}")
            import traceback
            logger.error(traceback.format_exc())

    async def close_option_position(active_trade: Any) -> None:
        """Close the given active trade"""
        try:
            if active_trade and active_trade.option_token in option_ticks:
                ticks_for_option = option_ticks[active_trade.option_token]
                if ticks_for_option:
                    option_ltp = ticks_for_option[-1]['ltp']
                    option_ltp_time = ticks_for_option[-1]['tt']
                    if option_ltp is not None and option_ltp_time is not None:
                        await trade_manager.close_trade(
                            token, timeframe, active_trade.option_symbol,
                            active_trade.option_token, active_trade.order_id,
                            option_ltp, option_ltp_time
                        )
                    else:
                        logger.warning("Missing option LTP or latest time for closing trade")
                else:
                    logger.warning(f"No ticks available for option {active_trade.option_token}")
            else:
                logger.warning("No active trade or option ticks found for closing")
        except Exception as e:
            logger.error(f"Error in close_option_position: {e}")
            import traceback
            logger.error(traceback.format_exc())

    async def monitor_retracement(target_price: float, action: str) -> None:
        try:
            async with monitor_lock:
                # Convert ISO timestamp string to Unix timestamp
                def iso_to_timestamp(iso_time: str) -> float:
                    if isinstance(iso_time, (int, float)):
                        return float(iso_time)
                    try:
                        dt = datetime.strptime(iso_time, '%Y-%m-%dT%H:%M:%S')
                        return dt.timestamp()
                    except ValueError as e:
                        logger.error(f"Failed to parse timestamp: {iso_time}, Error: {e}")
                        raise
                
                # Convert start_time to Unix timestamp
                start_time = iso_to_timestamp(latest_time)
                
                logger.info(f"Starting retracement monitoring - Action: {action}, Target: {target_price:.2f}, "
                        f"Start time: {latest_time}")
                
                while True:
                    current_jma = states.get("jma_1", {}).get("latest")
                    
                    current_supertrend = states.get("supertrend_1", {}).get("latest")
                    
                    # Exit monitoring if signal conditions change
                    if action == "BUY" and (current_jma == -1): #or current_supertrend == -1):
                        logger.warning(f"Exiting BUY monitoring - Indicators changed: JMA: {current_jma}, Supertrend: {current_supertrend}")
                        monitoring_status[strategy_key] = False
                        return
                    elif action == "SELL" and (current_jma == 1): #or current_supertrend == 1):
                        logger.warning(f"Exiting SELL monitoring - Indicators changed: JMA: {current_jma}, Supertrend: {current_supertrend}")
                        monitoring_status[strategy_key] = False
                        return

                    latest_tick = tick_collector.ring_buffers.get(token, [None])[-1]
                    if not latest_tick:
                        logger.warning("Exiting monitoring - No tick data available")
                        monitoring_status[strategy_key] = False
                        return
                    
                    try:
                        current_price = float(latest_tick["ltp"])
                    except (ValueError, TypeError):
                        logger.error(f"Invalid current_price: {latest_tick['ltp']}")
                        return

                    try:
                        target_price = float(target_price)
                    except (ValueError, TypeError):
                        logger.error(f"Invalid target_price: {target_price}")
                        return
                    
                    if (action == "BUY" and current_price <= target_price) or \
                    (action == "SELL" and current_price >= target_price):
                        if monitoring_status.get(strategy_key, False):
                            logger.info(f"Retracement target met - Price: {current_price:.2f}, Target: {target_price:.2f}")
                            await execute_option_action(action)
                            monitoring_status[strategy_key] = False
                        return
                    
                    # Convert current timestamp and check timeout
                    current_time = iso_to_timestamp(latest_tick["tt"])
                    time_elapsed = current_time - start_time
                    
                    if time_elapsed > 300:  # 5 minutes timeout
                        logger.warning(f"Monitoring timeout after {time_elapsed:.1f}s - Final Price: {current_price:.2f}, Target: {target_price:.2f}")
                        monitoring_status[strategy_key] = False
                        return
                        
                    await asyncio.sleep(0.1)
                    
        except Exception as e:
            logger.error(f"Error in retracement monitoring: {str(e)}")
            logger.error(traceback.format_exc())
            monitoring_status[strategy_key] = False
        finally:
            monitoring_status[strategy_key] = False

    try:
        # First check for active trades and process exits
        active_trade = None
        for trade in trade_manager.active_trades.values():
            if trade.index_token == token and trade.timeframe == timeframe:
                active_trade = trade
                break

        # Process exit signals with updated conditions
        if active_trade:
            if active_trade.action == "BUY":
                # Exit long if JMA changes to -1 OR Supertrend changes to -1
                jma_exit = jma["latest"] == -1 and jma["previous"] != -1
                sup_exit = supertrend["latest"] == -1 and supertrend["previous"] != -1
                
                if jma_exit or sup_exit:
                    await close_option_position(active_trade)
                    monitoring_status[strategy_key] = False
                    
            elif active_trade.action == "SELL":
                # Exit short if JMA changes to 1 OR Supertrend changes to 1
                jma_exit = jma["latest"] == 1 and jma["previous"] != 1
                sup_exit = supertrend["latest"] == 1 and supertrend["previous"] != 1
                
                if jma_exit or sup_exit:
                    await close_option_position(active_trade)
                    monitoring_status[strategy_key] = False

        # Check for new entry signals with updated conditions
        action = None
        
        # For 5-minute timeframe, only check JMA signal
        if timeframe == '300s' or '60s':
            # Buy signal for 5m: JMA changes to 1 from any state (ignore Supertrend)
            if jma["latest"] == 1 and jma["previous"] != 1:
                action = "BUY"
                
            # Sell signal for 5m: JMA changes to -1 from any state (ignore Supertrend)
            elif jma["latest"] == -1 and jma["previous"] != -1:
                action = "SELL"
        else:
            # For all other timeframes, keep existing logic requiring both JMA and Supertrend
            # Buy signal: JMA changes to 1 from any state AND Supertrend is 1
            if jma["latest"] == 1 and jma["previous"] != 1:# and supertrend["latest"] == 1:
                action = "BUY"
                
            # Sell signal: JMA changes to -1 from any state AND Supertrend is -1
            elif jma["latest"] == -1 and jma["previous"] != -1:# and supertrend["latest"] == -1:
                action = "SELL"

        # Process entry signals
        if action:
            # Cancel any existing retracement monitoring
            monitoring_status[strategy_key] = False
            
            last_candle = tick_collector.resampler.indicator_manager.get_latest_candle_with_indicators(
                token, timeframe)
                
            if last_candle:
                candle_ratio = last_candle.indicators['candle_height_1']
                
                if candle_ratio > 2 and not monitoring_status.get(strategy_key, False):
                    high_price = last_candle.candle.high
                    low_price = last_candle.candle.low
                    
                    if action == "BUY" and buy_allowed:
                        target_price = low_price + (high_price - low_price) * 0.5
                        monitoring_status[strategy_key] = True
                        logger.info(f"Monitoring retracement for buy at {target_price}")
                        asyncio.create_task(monitor_retracement(target_price, action))
                    elif action == "SELL" and sell_allowed:
                        target_price = high_price - (high_price - low_price) * 0.5
                        monitoring_status[strategy_key] = True
                        logger.info(f"Monitoring retracement for sell at {target_price}")
                        asyncio.create_task(monitor_retracement(target_price, action))
                        
                else:
                    if action == "BUY" and buy_allowed:
                        await execute_option_action(action)
                    elif action == "SELL" and sell_allowed:
                        await execute_option_action(action)

    except Exception as e:
        print(f"Error in trade logic for strategy {strategy_id}: {e}")
        import traceback
        print(traceback.format_exc())
        monitoring_status[strategy_key] = False

async def setup_strategies(tick_collector, trade_manager):
    """Set up strategies using the TickCollector and TradeManager instances."""
    strategy_manager = StrategyManager(tick_collector, trade_manager)

    # await strategy_manager.add_strategy(
    #     token="447850",
    #     timeframe="5s",
    #     strategy_id="5s_logic",  
    #     indicators=["supertrend_1", "jma_1", "candle_height_1"],
    #     strategy_logic=bull_and_bear_trade_logic,
    #     default_sl=30,
    #     default_target=40
    # )   
    
    # await strategy_manager.add_strategy(
    #     token="26000",
    #     timeframe="15s",
    #     strategy_id="5s_logic",  
    #     indicators=["supertrend_1", "jma_1", "candle_height_1"],
    #     strategy_logic=bull_and_bear_trade_logic,
    #     default_sl=20,
    #     default_target=25
    # ) 
    
    await strategy_manager.add_strategy(
        token="26000",
        timeframe="15s",
        strategy_id="5s_logic",  
        indicators=["supertrend_1", "jma_1", "candle_height_1"],
        strategy_logic=bull_and_bear_trade_logic,
        default_sl=10,
        default_target=20
    )   
    
    await strategy_manager.add_strategy(
        token="26000",
        timeframe="60s",
        strategy_id="5s_logic",  
        indicators=["supertrend_1", "jma_1", "candle_height_1"],
        strategy_logic=bull_and_bear_trade_logic,
        default_sl=10,
        default_target=20
    ) 
    
    # await strategy_manager.add_strategy(
    #     token="26000",
    #     timeframe="15s",
    #     strategy_id="5s_logic",  
    #     indicators=["supertrend_1", "jma_1", "candle_height_1"],
    #     strategy_logic=bull_and_bear_trade_logic,
    #     default_sl=10,
    #     default_target=15
    # ) 
    
    # await strategy_manager.add_strategy(
    #     token="26009",
    #     timeframe="15s",
    #     strategy_id="5s_logic",  
    #     indicators=["supertrend_1", "jma_1", "candle_height_1"],
    #     strategy_logic=bull_and_bear_trade_logic,
    #     default_sl=20,
    #     default_target=30
    # ) 
    
    # await strategy_manager.add_strategy(
    #     token="439830",
    #     timeframe="60s",
    #     strategy_id="5s_logic",  
    #     indicators=["supertrend_1", "jma_1", "candle_height_1"],
    #     strategy_logic=bull_and_bear_trade_logic,
    #     default_sl=20,
    #     default_target=50
    #  ) 
    
    # await strategy_manager.add_strategy(
    #     token="26009",
    #     timeframe="5s",
    #     strategy_id="5s_logic",  
    #     indicators=["supertrend_1", "jma_1", "candle_height_1"],
    #     strategy_logic=bull_and_bear_trade_logic,
    #     default_sl=15,
    #     default_target=30
    # ) 
    return strategy_manager

async def main():
    
    # Configure the root logger at the start of main()
    logging.basicConfig(
        level=logging.INFO,  # Set the logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",  # Log format
        handlers=[
            #logging.StreamHandler(),  # Log to console
            logging.FileHandler("app.log")  # Log to a file
        ]
    )    
    
    collector = None
    try:
        # Initialize AdvancedResampler outside of TickCollector
        resampler = AdvancedResampler(valid_timeframes=TickCollector.VALID_TIMEFRAMES) 
        collector = TickCollector(resampler=resampler)
        global global_tick_collector
        global_tick_collector = collector

        option_manager = OptionManager(collector)
        #future_manager = FutureManager(collector)
        
        #collector.future_manager = future_manager
        collector.option_manager = option_manager
        trade_manager = TradeManager(collector, option_manager)
        collector.trade_manager = trade_manager  

        # Add delayed strategy manager setup method to TickCollector
        async def delayed_setup():
            await asyncio.sleep(20)
            collector.strategy_manager = await setup_strategies(collector, trade_manager)
            # Start the option update task
            asyncio.create_task(option_manager.update_options()) 
            #asyncio.create_task(future_manager.update_futures())
            asyncio.create_task(broadcast_strategy_updates())        

        # Start essential tasks
        tasks = [
            asyncio.create_task(collector.connect_and_subscribe()),
            asyncio.create_task(collector.process_tick_queue()),
            asyncio.create_task(delayed_setup())            
        ]  
        
        tasks.append(asyncio.create_task(start_server()))
        # Wait for all tasks to complete
        await asyncio.gather(*tasks)
        
    except Exception as e:
        logging.error(f"Error in main: {e}")
        raise
    finally:
        if collector and collector.api:
            # Implement cleanup logic here
            for subscription in collector.active_subscriptions.copy():
                await collector.manage_subscriptions('remove', subscription)
            collector.feed_opened = False
            # Add server shutdown logic here if possible
            
# Ensure we're not blocking the IPython environment
loop = asyncio.get_event_loop()
loop.set_debug(True)
if loop.is_running():
    nest_asyncio.apply()
# Start the main function as an asyncio task
asyncio.create_task(main())
# If not running, keep the loop alive
if not loop.is_running():
    
    try:
        #loop.run_forever()
        asyncio.create_task(main())
    except KeyboardInterrupt:
        logging.info("Received exit signal. Cleaning up...")
    finally:
        loop.close()
        logging.info("Event loop closed. Exiting.")
        #trade_logger.shutdown()
#################################
#################################

app = FastAPI()

# Store connected clients
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],  # Adjust based on your frontend port
    allow_methods=["*"],
    allow_headers=["*"],
)
clients: Set[WebSocket] = set()
global_tick_collector = None

class TradeActionRequest(BaseModel):
    action: str
    trade_key: Optional[str] = None
    new_sl: Optional[float] = None
    new_target: Optional[float] = None
    trail_sl: Optional[float] = None
    
class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)

last_broadcast_trades = defaultdict(lambda: None)
last_broadcast_strategies = defaultdict(lambda: None)

def delta_encode(current_state, last_state):
    delta = {}
    for key in current_state:
        if current_state[key] != last_state.get(key):
            delta[key] = current_state[key]
    return delta

async def broadcast_trade_updates():
    """Broadcasts updates to all connected WebSocket clients with delta encoding."""
    try:
        if not clients:
            return

        if global_tick_collector is None or global_tick_collector.trade_manager is None:
            print("TradeManager not initialized for broadcast.")
            return

        trade_manager = global_tick_collector.trade_manager
        current_state = {
            "active_trades": await trade_manager.get_active_positions(),
            "trade_history": await trade_manager.get_trade_history_display()
        }
        delta = delta_encode(current_state, last_broadcast_trades)

        if delta:  # Only broadcast if there's an actual change
            message = {
                "type": "update",
                **delta
            }
            last_broadcast_trades.update(current_state)  # Update for next comparison
            json_message = json.dumps(message, cls=CustomJSONEncoder)
            coroutines = [client.send_text(json_message) for client in set(clients)]
            results = await asyncio.gather(*coroutines, return_exceptions=True)
            for result in results:
                if isinstance(result, Exception):
                    print(f"Error broadcasting to client: {result}")
                    clients.discard(client)
    except Exception as e:
        print(f"Error during broadcast: {e}")
        
# Pydantic model for strategy control requests
class StrategyControlRequest(BaseModel):
    token: str
    timeframe: str
    strategy_id: str

class TradeControlRequest(BaseModel):
    token: str
    timeframe: str
    strategy_id: str
    enable_buy: bool
    enable_sell: bool

async def broadcast_strategy_updates():
    """Broadcasts strategy updates to all connected WebSocket clients with delta encoding."""
    try:
        if not clients:
            return

        strategy_manager = global_tick_collector.strategy_manager
        if not strategy_manager:
            print("StrategyManager not initialized for broadcast.")
            return

        current_state = []
        for (token, timeframe, strategy_id), strategy in strategy_manager.strategies.items():
            trade_controller = strategy_manager.trade_controller
            trade_control = trade_controller.strategy_actions.get(
                (token, timeframe, strategy_id), {"enable_buy": True, "enable_sell": True}
            )
            current_state.append({
                "token": token,
                "timeframe": timeframe,
                "strategy_id": strategy_id,
                "enabled": strategy["enabled"],
                "enable_buy": trade_control["enable_buy"],
                "enable_sell": trade_control["enable_sell"]
            })

        delta = delta_encode({"strategy_states": current_state}, last_broadcast_strategies)

        if delta:  # Only broadcast if there's an actual change
            message = {
                "type": "strategy_update",
                **delta
            }
            last_broadcast_strategies.update({"strategy_states": current_state})  # Update for next comparison
            json_message = json.dumps(message, cls=CustomJSONEncoder)
            coroutines = [client.send_text(json_message) for client in set(clients)]
            results = await asyncio.gather(*coroutines, return_exceptions=True)
            
            
            for client, result in zip(set(clients), results):
                if isinstance(result, Exception):
                    print(f"Error broadcasting to client: {result}")
                    clients.discard(client)
        
    except Exception as e:
        print(f"Error during strategy broadcast: {e}")
        
# Endpoint to enable a strategy
@app.post("/enable_strategy")
async def enable_strategy(request: StrategyControlRequest):
    try:
        await global_tick_collector.strategy_manager.enable_strategy(
            request.token, request.timeframe, request.strategy_id
        )
        await broadcast_strategy_updates()  # Broadcast the update
        return {"status": "success", "message": f"Strategy {request.strategy_id} enabled."}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

# Endpoint to disable a strategy
@app.post("/disable_strategy")
async def disable_strategy(request: StrategyControlRequest):
    try:
        await global_tick_collector.strategy_manager.disable_strategy(
            request.token, request.timeframe, request.strategy_id
        )
        await broadcast_strategy_updates()  # Broadcast the update
        return {"status": "success", "message": f"Strategy {request.strategy_id} disabled."}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

# Endpoint to toggle buy/sell for a strategy
@app.post("/set_trade_control")
async def set_trade_control(request: TradeControlRequest):
    try:
        strategy_key = (request.token, request.timeframe, request.strategy_id)
        strategy = global_tick_collector.strategy_manager.strategies.get(strategy_key)
        
        # Check if the strategy exists and is enabled
        
        if not strategy or not strategy["enabled"]:
            raise HTTPException(
                status_code=400,
                detail=f"Cannot set trade control: Strategy {request.strategy_id} is not enabled."
            )
        
        # Set manual control for the strategy
        global_tick_collector.strategy_manager.trade_controller.set_manual_control(
            strategy_key, request.enable_buy, request.enable_sell
        )
        await broadcast_strategy_updates()  # Broadcast the update
        
        return {
            "status": "success",
            "message": f"Trade control updated for {request.strategy_id}."
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

# Endpoint to get the current state of a strategy
@app.get("/get_strategy_state")
async def get_strategy_state(token: str, timeframe: str, strategy_id: str):
    try:
        strategy_key = (token, timeframe, strategy_id)
        strategy = global_tick_collector.strategy_manager.strategies.get(strategy_key)
        if not strategy:
            raise HTTPException(status_code=404, detail="Strategy not found.")
        
        trade_controller = global_tick_collector.strategy_manager.trade_controller
        trade_control = trade_controller.strategy_actions.get(strategy_key, {"enable_buy": True, "enable_sell": True})
        
        return {
            "status": "success",
            "strategy_id": strategy_id,
            "enabled": strategy["enabled"],
            "enable_buy": trade_control["enable_buy"],
            "enable_sell": trade_control["enable_sell"]
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
    
@app.get("/get_all_strategies")
async def get_all_strategies():
    if not global_tick_collector or not global_tick_collector.strategy_manager:
        raise HTTPException(status_code=500, detail="StrategyManager not initialized")
    
    strategy_manager = global_tick_collector.strategy_manager
    strategies = []
    
    for (token, timeframe, strategy_id), strategy in strategy_manager.strategies.items():
        trade_controller = strategy_manager.trade_controller
        trade_control = trade_controller.strategy_actions.get(
            (token, timeframe, strategy_id), {"enable_buy": True, "enable_sell": True}
        )
        strategies.append({
            "token": token,
            "timeframe": timeframe,
            "strategy_id": strategy_id,
            "enabled": strategy["enabled"],
            "enable_buy": trade_control["enable_buy"],
            "enable_sell": trade_control["enable_sell"]
        })
    
    return strategies
        
@app.websocket("/ws/trades")
async def websocket_endpoint(websocket: WebSocket):
    await websocket.accept()
    clients.add(websocket)
    await broadcast_strategy_updates() 
    
    try:
        while True:
            data = await websocket.receive_text()
            try:
                #command = TradeActionRequest.parse_raw(data)
                command = TradeActionRequest.model_validate(json.loads(data))  
                if not global_tick_collector or not global_tick_collector.trade_manager:
                    await websocket.send_text("TradeManager not initialized")
                    continue
                
                trade_manager = global_tick_collector.trade_manager
                
                if command.action == "update_sl" and command.trade_key and command.new_sl is not None:
                    # Find trade by symbol instead of trade_key
                    trade_key = next((key for key, trade in trade_manager.active_trades.items() 
                                    if trade.option_symbol == command.trade_key), None)
                    if trade_key:
                        trade_manager.update_sl(trade_key, float(command.new_sl))
                        
                elif command.action == "update_target" and command.trade_key and command.new_target is not None:
                    trade_key = next((key for key, trade in trade_manager.active_trades.items() 
                                    if trade.option_symbol == command.trade_key), None)
                    if trade_key:
                        trade_manager.update_target(trade_key, float(command.new_target))
                        
                elif command.action == "set_trailing_sl" and command.trade_key and command.trail_sl is not None:
                    trade_key = next((key for key, trade in trade_manager.active_trades.items() 
                                    if trade.option_symbol == command.trade_key), None)
                    if trade_key:
                        trade_manager.set_trailing_sl(trade_key, float(command.trail_sl))
                        
                elif command.action == "close_trade" and command.trade_key:
                    trade_key = next((key for key, trade in trade_manager.active_trades.items() 
                                    if trade.option_symbol == command.trade_key), None)
                    if trade_key:
                        trade = trade_manager.active_trades[trade_key]
                        option_ticks = trade_manager.option_manager.option_ticks.get(
                            int(trade.index_token), {}).get(trade.option_token, deque())
                        current_price = option_ticks[-1]['ltp'] if option_ticks else trade.entry_price
                        
                        # Set exit reason for manual close
                        trade.exit_reason = "Manual Close"
                        
                        await trade_manager.close_trade(
                            index_token=trade.index_token,
                            timeframe=trade.timeframe,
                            option_symbol=trade.option_symbol,
                            option_token=trade.option_token,
                            order_id=trade.order_id,
                            price=current_price,
                            timestamp=datetime.now().isoformat()                            
                        )
                
                elif command.action == "exit_all_trades":
                    await trade_manager.exit_all_trades()                
                await broadcast_trade_updates()                
            except Exception as e:
                
                print(f"Error processing command: {e}")
                await websocket.send_text(f"Error: {str(e)}")                
    except WebSocketDisconnect:
        clients.discard(websocket)
    finally:
        clients.discard(websocket)
        
#templates = Jinja2Templates(directory="templates")
@app.get("/")
async def root():
    return {"message": "Trade WebSocket server running"}

# Analytics Endpoints
@app.get("/analytics/performance")
async def get_performance_analytics():
    """Get performance metrics by index and timeframe"""
    if not hasattr(global_tick_collector, 'trade_manager'):
        return {"error": "Trade manager not initialized"}
    
    class SimpleAnalyzer:
        def __init__(self, trade_manager):
            self.trade_manager = trade_manager
            self.index_map = self._load_index_map()
            
        def _load_index_map(self):
            try:
                with open('config.json') as f:
                    return {str(i['token']): i['index_name'] for i in json.load(f)['indices']}
            except:
                return {}

        def _get_index_from_symbol(self, symbol: str) -> str:
            for index_name in set(self.index_map.values()):
                if symbol.startswith(index_name):
                    return index_name
            return "Unknown"

        async def analyze(self):
            all_trades = self.trade_manager.closed_trades + list(self.trade_manager.active_trades.values())
            metrics = defaultdict(lambda: {
                'total_pnl': 0,
                'timeframes': defaultdict(lambda: {'pnl': 0, 'trades': 0}),
                'expiries': defaultdict(lambda: {'pnl': 0, 'trades': 0})
            })

            for trade in all_trades:
                index_name = self._get_index_from_symbol(trade.option_symbol)
                expiry = trade.option_symbol.replace(index_name, "")[:6]  # Simple expiry extract
                pnl = trade.current_pnl
                
                metrics[index_name]['total_pnl'] += pnl
                metrics[index_name]['timeframes'][trade.timeframe]['pnl'] += pnl
                metrics[index_name]['timeframes'][trade.timeframe]['trades'] += 1
                metrics[index_name]['expiries'][expiry]['pnl'] += pnl
                metrics[index_name]['expiries'][expiry]['trades'] += 1

            # Convert to serializable format
            return {
                index: {
                    'total_pnl': round(data['total_pnl'], 2),
                    'timeframes': dict(data['timeframes']),
                    'expiries': dict(data['expiries'])
                }
                for index, data in metrics.items()
            }
    
    analyzer = SimpleAnalyzer(global_tick_collector.trade_manager)
    return await analyzer.analyze()

@app.get("/analytics/symbol-breakdown")
async def get_symbol_breakdown():
    """Get trade distribution by symbol"""
    if not hasattr(global_tick_collector, 'trade_manager'):
        return {"error": "Trade manager not initialized"}
    
    tm = global_tick_collector.trade_manager
    symbols = defaultdict(int)
    
    for trade in tm.closed_trades + list(tm.active_trades.values()):
        symbols[trade.option_symbol] += 1
    
    return dict(symbols)
# -------------------------------------------------------------------

async def start_server():
    config = uvicorn.Config(app, host="0.0.0.0", port=8000)
    server = uvicorn.Server(config)
    
    # Wait for setup to complete
    while global_tick_collector is None or global_tick_collector.strategy_manager is None:
        await asyncio.sleep(1)        
    await server.serve()

token = "26000" #or full token "MCX|434272" depending on how you stored it
timeframe = "60s"
# Later, to get the latest candle with its indicators:
# To get last N candles with their indicators:
last_10_candles = global_tick_collector.resampler.indicator_manager.get_candles_with_indicators(token, timeframe, n_candles=300
                                                                                                )
for candle_data in last_10_candles:
    print(f"Time: {candle_data.candle.timestamp}, Close: {candle_data.candle.close}, jma-1: {candle_data.indicators['jma_1']}, jma_0: : {candle_data.indicators['jma_0']}")

# Get complete trade history
history = await global_tick_collector.trade_manager.get_trade_history_display()
print(history)

await global_tick_collector.strategy_manager.enable_strategy("437800", "15s", "5s_logic")

strategy_key = ("437800", "5", "5s_logic")
global_tick_collector.strategy_manager.trade_controller.set_manual_control(strategy_key, enable_buy=False, enable_sell=False)
global_tick_collector.strategy_manager.trade_controller.is_buy_allowed(strategy_key)
global_tick_collector.strategy_manager.trade_controller.strategy_actions

last_candle = global_tick_collector.resampler.indicator_manager.get_latest_candle_with_indicators('437800', "15s")
print(last_candle.indicators['candle_height_1'])

await global_tick_collector.strategy_manager.add_strategy(
        token="26009",
        timeframe="15s",
        strategy_id="5s_logic",  
        indicators=["supertrend_1", "jma_1", "candle_height_1"],
        strategy_logic=bull_and_bear_trade_logic,
        default_sl=20,
        default_target=30
    )

await global_tick_collector.strategy_manager.disable_strategy('26009' , "15s", "5s_logic" )

await global_tick_collector.manage_subscriptions('add','MCX|441306')

global_tick_collector.ring_buffers

await global_tick_collector.manage_subscriptions("add","NSE|26009")

import re
import csv
from datetime import datetime
import pandas as pd

def parse_log_file(log_file_path):
    """
    Parse the log file and extract trade information.
    """
    trades = {}
    with open(log_file_path, 'r') as file:
        for line in file:
            line = line.strip()
            
            # Skip empty lines
            if not line:
                continue
                
            # Extract trade execution information
            if "Trade executed:" in line:
                match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}) - TradeManager - INFO - Trade executed: (BUY|SELL) ([A-Z0-9]+) \((\d+)\) Index: (\d+) TF: (\d+)s Order: (ORD-\d+) Price: ([\d.]+) Time: ([\d-]+T[\d:]+)', line)
                if match:
                    timestamp, action, instrument, instrument_id, index_token, timeframe, order_id, price, trade_time = match.groups()
                    
                    trades[order_id] = {
                        'order_id': order_id,
                        'action': action,  # BUY means call option, SELL means put option
                        'instrument': instrument,
                        'instrument_id': instrument_id,
                        'index_token': index_token,
                        'timeframe': f"{int(timeframe)//60}m" if int(timeframe) >= 60 else f"{timeframe}s",
                        'entry_price': float(price),
                        'entry_time': trade_time,
                        'entry_timestamp': timestamp,
                        'exit_price': None,
                        'exit_time': None,
                        'exit_timestamp': None,
                        'pnl': None,
                        'pnl_percent': None,
                        'trade_duration': None,
                        'status': 'open',
                        'option_type': 'CALL' if action == 'BUY' else 'PUT'  # Track option type for clarity
                    }
            
            # Extract trade closing information
            elif "Closed trade:" in line:
                match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}) - TradeManager - INFO - Closed trade: (ORD-\d+)_([A-Z0-9]+) at ([\d.]+)', line)
                if match:
                    timestamp, order_id, instrument, exit_price = match.groups()
                    
                    if order_id in trades:
                        trades[order_id]['exit_price'] = float(exit_price)
                        trades[order_id]['exit_timestamp'] = timestamp
                        
                        # Extract exit time from timestamp
                        exit_time = datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S,%f')
                        trades[order_id]['exit_time'] = exit_time.strftime('%Y-%m-%dT%H:%M:%S')
                        
                        # Calculate PnL - ALL trades are actually BUY positions for options
                        # For both call and put options, profit is when exit_price > entry_price
                        entry_price = trades[order_id]['entry_price']
                        exit_price = float(exit_price)
                        
                        # All trades are buys (of either calls or puts), so profit is always when exit > entry
                        trades[order_id]['pnl'] = exit_price - entry_price
                        
                        trades[order_id]['pnl_percent'] = (trades[order_id]['pnl'] / entry_price) * 100
                        
                        # Calculate trade duration
                        entry_time = datetime.strptime(trades[order_id]['entry_timestamp'], '%Y-%m-%d %H:%M:%S,%f')
                        trade_duration = exit_time - entry_time
                        trades[order_id]['trade_duration'] = str(trade_duration)
                        
                        trades[order_id]['status'] = 'closed'
    
    return trades

def generate_trade_analysis(trades):
    """
    Generate detailed trade analysis.
    """
    # Convert to DataFrame for easier analysis
    df = pd.DataFrame.from_dict(trades, orient='index')
    
    # Filter out any trades that weren't closed
    closed_trades = df[df['status'] == 'closed'].copy()
    
    # Add winning/losing classification
    closed_trades['result'] = closed_trades['pnl'].apply(lambda x: 'win' if x > 0 else 'loss')
    
    # Group trades by index and timeframe
    grouped = closed_trades.groupby(['index_token', 'timeframe'])
    
    # Create analysis dict
    analysis = {}
    
    for (index_token, timeframe), group in grouped:
        key = f"{index_token}_{timeframe}"
        
        if key not in analysis:
            analysis[key] = {
                'index_token': index_token,
                'timeframe': timeframe,
                'win_trades': [],
                'loss_trades': [],
                'total_trades': len(group),
                'win_count': len(group[group['result'] == 'win']),
                'loss_count': len(group[group['result'] == 'loss']),
                'win_rate': len(group[group['result'] == 'win']) / len(group) * 100 if len(group) > 0 else 0,
                'total_pnl': group['pnl'].sum(),
                'avg_win': group[group['result'] == 'win']['pnl'].mean() if len(group[group['result'] == 'win']) > 0 else 0,
                'avg_loss': group[group['result'] == 'loss']['pnl'].mean() if len(group[group['result'] == 'loss']) > 0 else 0,
                'largest_win': group['pnl'].max() if len(group) > 0 else 0,
                'largest_loss': group['pnl'].min() if len(group) > 0 else 0
            }
        
        # Add individual trades
        for _, trade in group.iterrows():
            trade_info = {
                'order_id': trade['order_id'],
                'instrument': trade['instrument'],
                'action': trade['action'],
                'option_type': trade['option_type'] if 'option_type' in trade else ('CALL' if trade['action'] == 'BUY' else 'PUT'),
                'entry_time': trade['entry_time'],
                'entry_price': trade['entry_price'],
                'exit_time': trade['exit_time'],
                'exit_price': trade['exit_price'],
                'pnl': trade['pnl'],
                'pnl_percent': trade['pnl_percent'],
                'trade_duration': trade['trade_duration']
            }
            
            if trade['result'] == 'win':
                analysis[key]['win_trades'].append(trade_info)
            else:
                analysis[key]['loss_trades'].append(trade_info)
    
    return analysis

def write_results_to_csv(analysis, output_file):
    """
    Write the analysis results to a CSV file.
    """
    with open(output_file, 'w', newline='') as csvfile:
        # Write summary section
        summary_writer = csv.writer(csvfile)
        summary_writer.writerow(['INDEX SUMMARY'])
        summary_writer.writerow(['Index Token', 'Timeframe', 'Total Trades', 'Win Count', 'Loss Count', 
                                'Win Rate (%)', 'Total PnL', 'Avg Win', 'Avg Loss', 'Largest Win', 'Largest Loss'])
        
        for index_tf, data in analysis.items():
            summary_writer.writerow([
                data['index_token'], data['timeframe'], data['total_trades'], data['win_count'], data['loss_count'],
                f"{data['win_rate']:.2f}%", f"{data['total_pnl']:.2f}", f"{data['avg_win']:.2f}",
                f"{data['avg_loss']:.2f}", f"{data['largest_win']:.2f}", f"{data['largest_loss']:.2f}"
            ])
        
        # Add empty row for separation
        summary_writer.writerow([])
        
        # Write detailed trade data
        for index_tf, data in analysis.items():
            # Winning trades section
            summary_writer.writerow([f"WINNING TRADES - INDEX: {data['index_token']} - TIMEFRAME: {data['timeframe']}"])
            summary_writer.writerow(['Order ID', 'Instrument', 'Option Type', 'Entry Time', 'Entry Price', 
                                    'Exit Time', 'Exit Price', 'PnL', 'PnL %', 'Duration'])
            
            for trade in sorted(data['win_trades'], key=lambda x: x['pnl'], reverse=True):
                summary_writer.writerow([
                    trade['order_id'], trade['instrument'], trade['option_type'], trade['entry_time'],
                    f"{trade['entry_price']:.2f}", trade['exit_time'], f"{trade['exit_price']:.2f}",
                    f"{trade['pnl']:.2f}", f"{trade['pnl_percent']:.2f}%", trade['trade_duration']
                ])
            
            # Add empty row for separation
            summary_writer.writerow([])
            
            # Losing trades section
            summary_writer.writerow([f"LOSING TRADES - INDEX: {data['index_token']} - TIMEFRAME: {data['timeframe']}"])
            summary_writer.writerow(['Order ID', 'Instrument', 'Option Type', 'Entry Time', 'Entry Price', 
                                    'Exit Time', 'Exit Price', 'PnL', 'PnL %', 'Duration'])
            
            for trade in sorted(data['loss_trades'], key=lambda x: x['pnl']):
                summary_writer.writerow([
                    trade['order_id'], trade['instrument'], trade['option_type'], trade['entry_time'],
                    f"{trade['entry_price']:.2f}", trade['exit_time'], f"{trade['exit_price']:.2f}",
                    f"{trade['pnl']:.2f}", f"{trade['pnl_percent']:.2f}%", trade['trade_duration']
                ])
            
            # Add empty rows for separation between different index/timeframe combinations
            summary_writer.writerow([])
            summary_writer.writerow([])

def main():
    log_file_path = 'app.log'
    output_file = 'analysis.csv'
    
    print(f"Analyzing log file: {log_file_path}")
    trades = parse_log_file(log_file_path)
    print(f"Found {len(trades)} total trades")
    
    closed_trades = {k: v for k, v in trades.items() if v['status'] == 'closed'}
    print(f"Found {len(closed_trades)} closed trades")
    
    analysis = generate_trade_analysis(trades)
    print(f"Generated analysis for {len(analysis)} index/timeframe combinations")
    
    write_results_to_csv(analysis, output_file)
    print(f"Analysis written to {output_file}")
    
    # Print some debug information
    total_win_count = sum(data['win_count'] for data in analysis.values())
    total_loss_count = sum(data['loss_count'] for data in analysis.values())
    total_pnl = sum(data['total_pnl'] for data in analysis.values())
    
    print(f"\nSummary:")
    print(f"Total winning trades: {total_win_count}")
    print(f"Total losing trades: {total_loss_count}")
    if total_win_count + total_loss_count > 0:
        print(f"Win rate: {(total_win_count / (total_win_count + total_loss_count) * 100):.2f}%")
    else:
        print(f"Win rate: N/A (no closed trades)")
    print(f"Total PnL: {total_pnl:.2f}")

if __name__ == "__main__":
    main()