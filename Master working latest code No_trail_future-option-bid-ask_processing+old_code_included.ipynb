{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import asyncio\n", "import logging\n", "import time\n", "import random\n", "import statistics\n", "import concurrent.futures\n", "import pandas as pd\n", "import pyotp\n", "from datetime import datetime, timedelta\n", "from collections import deque, defaultdict\n", "from queue import SimpleQueue, Queue\n", "from NorenRestApiPy.NorenApi import NorenApi\n", "from dataclasses import dataclass, field\n", "from typing import Dict, Deque, Optional, List, Callable, Any, Set\n", "from functools import lru_cache\n", "import nest_asyncio\n", "from asyncio import Lock\n", "import numpy as np\n", "from numba import njit\n", "from enum import Enum\n", "from numba_indicators import supertrend_numba,jma_numba_direction_wrapper,candle_height_numba\n", "from typing import Callable, Dict, List, Any, Tuple\n", "import threading\n", "import csv\n", "import os\n", "import json\n", "from dateutil import parser\n", "import fastapi\n", "import uvicorn\n", "from fastapi.responses import JSONResponse\n", "from pydantic import BaseModel\n", "import queue\n", "from fastapi.templating import Jinja2Templates\n", "from fastapi.responses import HTMLResponse\n", "from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, Request\n", "from fastapi.middleware.cors import CORSMiddleware\n", "## big ass globals\n", "retracement_monitors = {}\n", "monitor_flags = {}\n", "\n", "@dataclass\n", "class OHLCV:\n", "    timestamp: datetime\n", "    open: float\n", "    high: float\n", "    low: float\n", "    close: float\n", "    volume: float = 0\n", "    tick_count: int = 0\n", "    \n", "    @classmethod\n", "    def from_tick(cls, tick: Dict, timestamp: datetime):\n", "        price = tick['ltp']\n", "        return cls(\n", "            timestamp=timestamp,\n", "            open=price,\n", "            high=price,\n", "            low=price,\n", "            close=price,\n", "            volume=tick.get('volume', 0),\n", "            tick_count=1\n", "        )\n", "@dataclass\n", "class ResamplingBuffer:\n", "    timeframe: str\n", "    current_candle: Optional[OHLCV] = None\n", "    buffer: deque = field(default_factory=lambda: deque(maxlen=500))\n", "\n", "    def get_candle_start_time(self, tick_time: datetime) -> datetime:\n", "        \"\"\"Align tick time to the appropriate candle start time.\"\"\"\n", "        timeframe_value = int(''.join(filter(str.isdigit, self.timeframe)))\n", "        timeframe_unit = ''.join(filter(str.isalpha, self.timeframe))\n", "        \n", "        if timeframe_unit == 's':  # seconds-based timeframes\n", "            if timeframe_value >= 60:  # Handle multi-minute cases\n", "                minutes = (tick_time.minute * 60 + tick_time.second) // timeframe_value * (timeframe_value // 60)\n", "                aligned_time = tick_time.replace(minute=0, second=0, microsecond=0) + <PERSON><PERSON>ta(minutes=minutes)\n", "            else:\n", "                aligned_time = tick_time - timed<PERSON>ta(seconds=tick_time.second % timeframe_value)\n", "\n", "        elif timeframe_unit == 'm':  # minutes\n", "            aligned_time = tick_time - timedelta(minutes=tick_time.minute % timeframe_value)\n", "            aligned_time = aligned_time.replace(second=0, microsecond=0)\n", "        elif timeframe_unit == 'h':  # hours\n", "            aligned_time = tick_time - timedelta(hours=tick_time.hour % timeframe_value)\n", "            aligned_time = aligned_time.replace(minute=0, second=0, microsecond=0)\n", "        else:\n", "            raise ValueError(f\"Unsupported timeframe: {self.timeframe}\")\n", "\n", "        # Always clear milliseconds and microseconds\n", "        return aligned_time.replace(microsecond=0)\n", "\n", "class AdvancedResampler:\n", "    def __init__(self, valid_timeframes):\n", "        self.buffers: Dict[str, Dict[str, ResamplingBuffer]] = defaultdict(dict)\n", "        self.valid_timeframes = valid_timeframes\n", "        self.indicator_manager = EnhancedIndicatorManager()\n", "        self.locks = defaultdict(asyncio.Lock)  # Lock for each token to ensure sequential processing\n", "\n", "    async def process_tick(self, token: str, tick: dict, resampling_enabled: Dict[str, bool]):\n", "        \"\"\"Process a new tick and update the candles if resampling is enabled.\"\"\"\n", "        tick_time = datetime.fromisoformat(tick['tt'])\n", "        tick_price = tick['ltp']\n", "        \n", "        for timeframe in self.valid_timeframes:\n", "            if not resampling_enabled.get(timeframe, False):\n", "                continue\n", "\n", "            # Initialize the buffer if it doesn't exist\n", "            if timeframe not in self.buffers[token]:\n", "                self.buffers[token][timeframe] = ResamplingBuffer(timeframe=timeframe)\n", "            \n", "            buffer = self.buffers[token][timeframe]\n", "            candle_start_time = buffer.get_candle_start_time(tick_time)\n", "\n", "            # Handle a new candle bucket\n", "            if not buffer.current_candle or buffer.current_candle.timestamp != candle_start_time:\n", "                if buffer.current_candle:\n", "                    # Check for out-of-order candle before appending\n", "                    if buffer.buffer and buffer.current_candle.timestamp <= buffer.buffer[-1].timestamp:  # fix for non sorted candle sequence\n", "                        print(f\"Skipping out-of-order candle: {buffer.current_candle.timestamp}\")\n", "                    else:\n", "                        buffer.buffer.append(buffer.current_candle)\n", "                        # Debug log to verify the buffer state\n", "                        # print(f\"Appended Candle: {buffer.current_candle.timestamp}\")\n", "                        # print(f\"Buffer Timestamps: {[c.timestamp for c in buffer.buffer]}\")\n", "\n", "                    # Calculate indicators for the finalized candle\n", "                    await self.indicator_manager.calculate_indicator(\n", "                        token=token,\n", "                        timeframe=timeframe,\n", "                        candles=list(buffer.buffer)\n", "                    )\n", "                \n", "                # Start a new candle\n", "                buffer.current_candle = OHLCV.from_tick(tick, candle_start_time)\n", "            else:\n", "                # Update the existing candle\n", "                buffer.current_candle.high = max(buffer.current_candle.high, tick_price)\n", "                buffer.current_candle.low = min(buffer.current_candle.low, tick_price)\n", "                buffer.current_candle.close = tick_price\n", "                buffer.current_candle.tick_count += 1\n", "                buffer.current_candle.volume += tick.get('volume', 0)\n", "   \n", "    def get_resampled_data(self, token: str, timeframe: str, n_candles: Optional[int] = None):\n", "        \"\"\"Retrieve resampled data for a token and timeframe.\"\"\"\n", "        if token not in self.buffers or timeframe not in self.buffers[token]:\n", "            return []\n", "        \n", "        buffer = self.buffers[token][timeframe].buffer\n", "        if n_candles is None:\n", "            return list(buffer)\n", "        return list(buffer)[-n_candles:]\n", "\n", "\n", "@dataclass\n", "class IndicatorConfig:\n", "    name: str\n", "    function: Callable\n", "    params: Dict[str, Any]\n", "    timeframes: List[str]\n", "\n", "@dataclass\n", "class CandleIndicatorData:\n", "    candle: 'OHLCV'  # Assuming OHLCV is defined elsewhere\n", "    indicators: Dict[str, float] = field(default_factory=dict)\n", "\n", "class EnhancedIndicatorManager:\n", "    def __init__(self):\n", "        self.indicators: Dict[str, Dict[str, Dict[str, Any]]] = defaultdict(lambda: defaultdict(dict))\n", "        self.configs: Dict[str, IndicatorConfig] = {}\n", "        self.candle_indicators: Dict[str, Dict[str, List[CandleIndicatorData]]] = defaultdict(lambda: defaultdict(list))\n", "        \n", "        ## event Monitoring system with async queue start\n", "        self.subscriptions: Dict[Tuple[str, str, str], deque] = defaultdict(lambda: deque(maxlen=50))\n", "        self.subscription_times: Dict[Tuple[str, str, str], datetime] = {}  # Store subscription times separately\n", "        self.callbacks: Dict[Tuple[str, str, str], List[Callable]] = defaultdict(list)\n", "        self.callback_queue: asyncio.Queue = asyncio.Queue()\n", "        self._callback_task: Optional[asyncio.Task] = None\n", "        ## event Monitoring system with async queue end\n", "\n", "    def register_indicator(self, name: str, function: Callable, params: Dict[str, Any], timeframes: List[str]):\n", "        \"\"\"Register an indicator configuration\"\"\"\n", "        self.configs[name] = IndicatorConfig(name, function, params, timeframes)\n", "\n", "    async def start_callback_processor(self):\n", "        \"\"\"Start the async callback processing task\"\"\"\n", "        if self._callback_task is None:\n", "            self._callback_task = asyncio.create_task(self._process_callbacks())\n", "\n", "    async def stop_callback_processor(self):\n", "        \"\"\"Stop the async callback processing task\"\"\"\n", "        if self._callback_task:\n", "            self._callback_task.cancel()\n", "            try:\n", "                await self._callback_task\n", "            except async<PERSON>.CancelledError:\n", "                pass\n", "            self._callback_task = None\n", "\n", "    async def _safe_callback_execution(self, callback, *args):\n", "        try:\n", "            if asyncio.iscoroutinefunction(callback):\n", "                await callback(*args)\n", "            else:\n", "                await asyncio.to_thread(callback, *args)\n", "        except Exception as e:\n", "            print(f\"Callback execution error: {e}\")\n", "            \n", "\n", "    async def _process_callbacks(self):\n", "        while True:\n", "            try:\n", "                token, timeframe, indicator, value, timestamp = await self.callback_queue.get()\n", "                key = (token, timeframe, indicator)\n", "                callbacks = self.callbacks.get(key, [])\n", "                \n", "                if callbacks:\n", "                    await asyncio.gather(\n", "                        *[self._safe_callback_execution(callback, token, timeframe, indicator, value, timestamp) \n", "                        for callback in callbacks],\n", "                        return_exceptions=True\n", "                    )\n", "                \n", "                self.callback_queue.task_done()\n", "            \n", "            except Exception as e:\n", "                print(f\"Callback processing error: {e}\")\n", "\n", "    async def subscribe(self, token: str, timeframe: str, indicator: str, callback: Optional[Callable] = None):\n", "        \"\"\"Asynchronously subscribe to an indicator with deduplication\"\"\"\n", "        key = (token, timeframe, indicator)\n", "        print(f\"Registering callback for: {key}\")\n", "        await self.start_callback_processor()\n", "\n", "        # Store subscription time separately (no need to store it inside deque)\n", "        if key not in self.subscription_times:\n", "            self.subscription_times[key] = datetime.now()\n", "\n", "        # Add the callback to the list of callbacks\n", "        if callback:\n", "            # Directly add the callback if it's already an async function\n", "            if asyncio.iscoroutinefunction(callback):\n", "                if callback not in self.callbacks[key]:\n", "                    self.callbacks[key].append(callback)\n", "            else:\n", "                # Wrap synchronous callbacks in an async function\n", "                async def async_wrapper(*args, **kwargs):\n", "                    return callback(*args, **kwargs)\n", "                \n", "                if async_wrapper not in self.callbacks[key]:\n", "                    self.callbacks[key].append(async_wrapper)\n", "\n", "        # Initialize the deque for historical data (timestamp, value)\n", "        self.subscriptions[key] = deque(maxlen=50)\n", "        return key\n", "\n", "    async def notify_subscribers(self, token: str, timeframe: str, indicator: str, value: float, timestamp: datetime):\n", "        key = (token, timeframe, indicator)\n", "        subscription_time = self.subscription_times.get(key)\n", "        if subscription_time and timestamp >= subscription_time:\n", "            if key in self.subscriptions:\n", "                if not any(existing_timestamp == timestamp and existing_value == value \n", "                        for existing_timestamp, existing_value in self.subscriptions[key]):\n", "                    self.subscriptions[key].append((timestamp, value))\n", "\n", "            if key in self.callbacks:\n", "\n", "                await self.callback_queue.put((token, timeframe, indicator, value, timestamp))\n", "                \n", "    def get_numpy_arrays(self, candles: List['OHLCV']) -> tuple:\n", "        \"\"\"Convert list of OHLCV candles to numpy arrays\"\"\"\n", "        if not candles:\n", "            return np.array([]), np.array([]), np.array([]), np.array([])\n", "        \n", "        high = np.array([c.high for c in candles])\n", "        low = np.array([c.low for c in candles])\n", "        close = np.array([c.close for c in candles])\n", "        timestamps = np.array([c.timestamp for c in candles])\n", "        \n", "        return high, low, close, timestamps\n", "\n", "    async def calculate_indicator(self, token: str, timeframe: str, candles: List['OHLCV']):\n", "        high, low, close, timestamps = self.get_numpy_arrays(candles)\n", "\n", "        if len(close) == 0:\n", "            return\n", "\n", "        candle_data_list = [CandleIndicatorData(candle=candle) for candle in candles]\n", "        # Get the current running event loop\n", "        loop = asyncio.get_running_loop()\n", "        # Use run_in_executor for parallel indicator calculations\n", "        indicator_futures = {\n", "            name: loop.run_in_executor(\n", "                None,  # Use the default executor\n", "                self._call_indicator_function,\n", "                config.function,\n", "                high, low, close,\n", "                config.params\n", "            )\n", "            for name, config in self.configs.items()\n", "            if timeframe in config.timeframes\n", "        }\n", "        # Wait for all futures and process results\n", "        for name, future in indicator_futures.items():\n", "            try:\n", "                results = await future\n", "                if isinstance(results, tuple):\n", "                    # Handle multiple return values\n", "                    for i, result in enumerate(results):\n", "                        indicator_name = f\"{name}_{i}\"\n", "\n", "                        self.indicators[token][timeframe][indicator_name] = {\n", "                            'values': result,\n", "                            'timestamps': timestamps\n", "                        }\n", "                        for idx, value in enumerate(result):\n", "                            if idx < len(candle_data_list):\n", "                                candle_data_list[idx].indicators[indicator_name] = value\n", "                                await self.notify_subscribers(token, timeframe, indicator_name, value, timestamps[idx])\n", "                else:\n", "                    # Single return value indicator\n", "                    self.indicators[token][timeframe][name] = {\n", "                        'values': results,\n", "                        'timestamps': timestamps\n", "                    }\n", "                    for idx, value in enumerate(results):\n", "                        if idx < len(candle_data_list):\n", "                            candle_data_list[idx].indicators[name] = value\n", "                            await self.notify_subscribers(token, timeframe, name, value, timestamps[idx])\n", "            except Exception as e:\n", "                logging.error(f\"Error calculating {name} for {token} {timeframe}: {e}\")\n", "\n", "        # Update the candle_indicators structure\n", "        self.candle_indicators[token][timeframe] = candle_data_list\n", "\n", "    # Helper method to ensure correct function calls\n", "    def _call_indicator_function(self, func, high, low, close, params):\n", "        # Filter params to include only those accepted by the function\n", "        valid_params = {k: v for k, v in params.items() if k in func.__code__.co_varnames}\n", "        return func(high, low, close, **valid_params)\n", "        \n", "    def get_latest_candle_with_indicators(self, token: str, timeframe: str) -> Optional[CandleIndicatorData]:\n", "        \"\"\"Get the latest candle with its indicator values\"\"\"\n", "        if token in self.candle_indicators and timeframe in self.candle_indicators[token]:\n", "            candle_data_list = self.candle_indicators[token][timeframe]\n", "            if candle_data_list:\n", "                return candle_data_list[-1]\n", "        return None\n", "\n", "    def get_candles_with_indicators(self, token: str, timeframe: str, n_candles: Optional[int] = None) -> List[CandleIndicatorData]:\n", "        \"\"\"Get historical candles with their indicator values\"\"\"\n", "        if token in self.candle_indicators and timeframe in self.candle_indicators[token]:\n", "            candle_data_list = self.candle_indicators[token][timeframe]\n", "            if n_candles is not None:\n", "                return candle_data_list[-n_candles:]\n", "            return candle_data_list\n", "        return []\n", "\n", "    # Keep existing methods for backward compatibility\n", "    def get_latest_indicator(self, token: str, timeframe: str, indicator_name: str) -> tuple:\n", "        \"\"\"Get latest indicator value and its timestamp\"\"\"\n", "        if token in self.indicators and timeframe in self.indicators[token]:\n", "            indicator_data = self.indicators[token][timeframe].get(indicator_name)\n", "            if indicator_data and len(indicator_data['values']) > 0:\n", "                return (\n", "                    indicator_data['values'][-1],\n", "                    indicator_data['timestamps'][-1]\n", "                )\n", "        return None, None\n", "\n", "    def get_indicator_history(self, token: str, timeframe: str, indicator_name: str, n_values: int = None) -> tuple:\n", "        \"\"\"Get historical indicator values and timestamps\"\"\"\n", "        if token in self.indicators and timeframe in self.indicators[token]:\n", "            indicator_data = self.indicators[token][timeframe].get(indicator_name)\n", "            if indicator_data:\n", "                if n_values:\n", "                    return (\n", "                        indicator_data['values'][-n_values:],\n", "                        indicator_data['timestamps'][-n_values:]\n", "                    )\n", "                return indicator_data['values'], indicator_data['timestamps']\n", "        return np.array([]), np.array([])\n", "    \n", "class TickCollector:\n", "    VALID_TIMEFRAMES = ['5s','15s','60s','120s','300s']\n", "    def __init__(self,resampler: AdvancedResampler, credentials_file=\"usercred.xlsx\"):\n", "        self.processing_lock = asyncio.Lock()\n", "        self.api = None\n", "        self.feed_opened = False\n", "        #self.tick_queue = SimpleQueue()\n", "        self.tick_queue = asyncio.Queue()\n", "        self.ring_buffers = defaultdict(lambda: deque(maxlen=100))\n", "        self.resampling_enabled = defaultdict(dict)\n", "        self.last_tick_time = {}\n", "        self.active_subscriptions = set()\n", "        \n", " \n", "        self.logger = logging.getLogger(\"TickCollector\")\n", "        self.resampler = resampler  # Inject resampler instead of creating a new one # grok        \n", "        #self.resampler = AdvancedResampler(valid_timeframes=self.VALID_TIMEFRAMES)\n", "        self.option_manager = None  # To store OptionManager reference        \n", "        self.future_manager = None\n", "        \n", "        # Initialize API\n", "        self._initialize_api(credentials_file)\n", "        self.resampler.indicator_manager = EnhancedIndicatorManager()\n", "        # fix  stratagy manager start\n", "        self.strategy_manager = None\n", "        # fix end\n", "\n", "        self.resampler.indicator_manager.register_indicator(\n", "            name='supertrend',\n", "            function=supertrend_numba,\n", "            params={'length': 3, 'multiplier': 2},\n", "            timeframes=['5s','15s','60s','120s', '300s']\n", "        )\n", "\n", "        self.resampler.indicator_manager.register_indicator(\n", "            name='jma',\n", "            function=jma_numba_direction_wrapper,\n", "            params={'length': 5, 'phase': 50, 'power': 2},\n", "            timeframes=['5s','15s','60s','120s', '300s']\n", "        )\n", "        \n", "        self.resampler.indicator_manager.register_indicator(\n", "            name='candle_height',\n", "            function=candle_height_numba,\n", "            params={'lookback': 5},\n", "            timeframes=['5s','15s','60s','120s', '300s']\n", "        )\n", "\n", "    def set_option_manager(self, option_manager):\n", "        \"\"\"Set the OptionManager for handling option ticks.\"\"\"\n", "        self.option_manager = option_manager\n", "\n", "    async def handle_indicator_change(self, **kwargs):\n", "        \"\"\"Handle indicator change events.\"\"\"\n", "        self.logger.info(\n", "            f\"Indicator {kwargs['indicator_name']} changed for {kwargs['token']} \"\n", "            f\"on {kwargs['timeframe']}: {kwargs['prev_value']} -> {kwargs['curr_value']}\"\n", "        )\n", "        # Add your custom indicator change handling logic here\n", "\n", "    async def handle_indicator_error(self, **kwargs):\n", "        \"\"\"Handle indicator calculation errors.\"\"\"\n", "        self.logger.error(\n", "            f\"Error calculating {kwargs['indicator_name']} for {kwargs['token']} \"\n", "            f\"on {kwargs['timeframe']}: {kwargs['error']}\"\n", "        )\n", "\n", "\n", "    def _initialize_api(self, credentials_file):\n", "        self.api = NorenApi(\n", "            host=\"https://api.shoonya.com/NorenWClientTP/\",\n", "            websocket=\"wss://api.shoonya.com/NorenWSTP/\"\n", "        )\n", "        credentials = pd.read_excel('usercred.xlsx')\n", "        user, password, vendor_code, app_key, imei, qr_code = credentials.iloc[0]\n", "        factor2 = pyotp.TOTP(qr_code).now()\n", "\n", "        self.api.login_result = self.api.login(\n", "            userid=user,\n", "            password=password,\n", "            twoFA=factor2,\n", "            vendor_code=vendor_code,\n", "            api_secret=app_key,\n", "            imei=imei\n", "        )\n", "\n", "    def initialize_resampling_enabled(self, token):\n", "        \"\"\"Initialize resampling_enabled for the token.\"\"\"\n", "        if token not in self.resampling_enabled:\n", "            self.resampling_enabled[token] = {tf: False for tf in self.VALID_TIMEFRAMES}\n", "            self.logger.info(f\"Initialized resampling_enabled for token {token}\")\n", "\n", "    async def set_resampling(self, token, timeframe, enable):\n", "        \"\"\"Enable or disable resampling for a token and timeframe.\"\"\"\n", "        if token in self.resampling_enabled and timeframe in self.VALID_TIMEFRAMES:\n", "            self.resampling_enabled[token][timeframe] = enable\n", "            self.logger.info(f\"Resampling {'enabled' if enable else 'disabled'} for token {token} and timeframe {timeframe}\")\n", "\n", "    #### perfect working simple\n", "    # def event_handler_feed_update(self, tick_data):\n", "    #     \"\"\"Handles incoming tick data for both indices and options.\"\"\"\n", "    #     #print(tick_data)\n", "    #     try:\n", "    #         # Extract required fields\n", "    #         token = tick_data.get('tk')\n", "    #         price = tick_data.get('lp')\n", "    #         timestamp = tick_data.get('ft')\n", "\n", "    #         # Skip if required fields are missing\n", "    #         if not all([token, price, timestamp]):\n", "    #             return  # Skip silently without logging\n", "\n", "    #         # Convert timestamp to ISO format\n", "    #         try:\n", "    #             timest = datetime.fromtimestamp(int(timestamp)).isoformat()\n", "    #         except (Value<PERSON><PERSON><PERSON>, TypeError):\n", "    #             return  # Skip if timestamp is invalid\n", "\n", "    #         # Create new tick\n", "    #         new_tick = {'tt': timest, 'ltp': float(price)}\n", "\n", "    #         # Handle option ticks if OptionManager is present\n", "    #         if self.option_manager and token in self.option_manager.option_subscriptions:\n", "    #             numeric_subscriptions = {sub.split('|')[1] for sub in self.active_subscriptions}\n", "    #             if token not in numeric_subscriptions:\n", "    #                 # Handle option tick asynchronously\n", "    #                 asyncio.run_coroutine_threadsafe(\n", "    #                     self.option_manager.handle_option_tick(token, new_tick), \n", "    #                     loop\n", "    #                 )\n", "    #             else:\n", "    #                 # Add to tick queue\n", "    #                 self.tick_queue.put_nowait((token, new_tick))\n", "    #         else:\n", "    #             # Add to tick queue for regular indices\n", "    #             self.tick_queue.put_nowait((token, new_tick))\n", "\n", "    #     except Exception as e:\n", "    #         # Log unexpected errors\n", "    #         self.logger.error(f\"Error processing tick data: {e}\")\n", "    \n", "    \n", "    #### ask bid\n", "    # def event_handler_feed_update(self, tick_data):\n", "    #     \"\"\"Handles incoming tick data for both indices and options.\"\"\"\n", "    #     try:\n", "    #         # Extract message type\n", "    #         msg_type = tick_data.get('t')\n", "    #         token = tick_data.get('tk')\n", "    #         price = tick_data.get('lp')\n", "    #         timestamp = tick_data.get('ft')\n", "\n", "    #         # Skip if token or price is missing\n", "    #         if not token or not price:\n", "    #             return  # Skip silently without logging\n", "\n", "    #         # Convert timestamp to ISO format if available, else use current time\n", "    #         if timestamp:\n", "    #             try:\n", "    #                 timest = datetime.fromtimestamp(int(timestamp)).isoformat()\n", "    #             except (Value<PERSON><PERSON><PERSON>, TypeError):\n", "    #                 timest = datetime.now().isoformat()\n", "    #         else:\n", "    #             timest = datetime.now().isoformat()\n", "\n", "    #         # Create new tick with basic data\n", "    #         new_tick = {\n", "    #             'tt': timest, \n", "    #             'ltp': float(price) if price else None\n", "    #         }\n", "            \n", "    #         # Add volume if available\n", "    #         if 'v' in tick_data:\n", "    #             new_tick['v'] = int(tick_data['v'])\n", "            \n", "    #         # For depth messages or initial touchline, extract all levels of market depth\n", "    #         if msg_type in ['dk', 'tk']:\n", "    #             # Extract bid levels (1-5)\n", "    #             for i in range(1, 6):\n", "    #                 bp_key = f'bp{i}'\n", "    #                 bq_key = f'bq{i}'\n", "    #                 bo_key = f'bo{i}'\n", "                    \n", "    #                 if bp_key in tick_data:\n", "    #                     new_tick[bp_key] = float(tick_data[bp_key])\n", "    #                 if bq_key in tick_data:\n", "    #                     new_tick[bq_key] = int(tick_data[bq_key])\n", "    #                 if bo_key in tick_data:\n", "    #                     new_tick[bo_key] = int(tick_data[bo_key])\n", "                \n", "    #             # Extract sell levels (1-5)\n", "    #             for i in range(1, 6):\n", "    #                 sp_key = f'sp{i}'\n", "    #                 sq_key = f'sq{i}'\n", "    #                 so_key = f'so{i}'\n", "                    \n", "    #                 if sp_key in tick_data:\n", "    #                     new_tick[sp_key] = float(tick_data[sp_key])\n", "    #                 if sq_key in tick_data:\n", "    #                     new_tick[sq_key] = int(tick_data[sq_key])\n", "    #                 if so_key in tick_data:\n", "    #                     new_tick[so_key] = int(tick_data[so_key])\n", "                        \n", "    #             # Extract total quantities if available\n", "    #             if 'tbq' in tick_data:\n", "    #                 new_tick['tbq'] = int(tick_data['tbq'])  # Total Buy Quantity\n", "    #             if 'tsq' in tick_data:\n", "    #                 new_tick['tsq'] = int(tick_data['tsq'])  # Total Sell Quantity\n", "            \n", "    #         # For touchline updates, only extract the fields that are present\n", "    #         elif msg_type == 'tf':\n", "    #             # Add any fields that are present in the update\n", "    #             for key in ['bp1', 'bq1', 'sp1', 'sq1']:\n", "    #                 if key in tick_data:\n", "    #                     new_tick[key] = float(tick_data[key]) if key in ['bp1', 'sp1'] else int(tick_data[key])\n", "            \n", "    #         # Handle option ticks if OptionManager is present\n", "    #         if self.option_manager and token in self.option_manager.option_subscriptions:\n", "    #             numeric_subscriptions = {sub.split('|')[1] for sub in self.active_subscriptions}\n", "    #             if token not in numeric_subscriptions:\n", "    #                 # Handle option tick asynchronously\n", "    #                 asyncio.run_coroutine_threadsafe(\n", "    #                     self.option_manager.handle_option_tick(token, new_tick), \n", "    #                     loop\n", "    #                 )\n", "    #             else:\n", "    #                 # Add to tick queue\n", "    #                 self.tick_queue.put_nowait((token, new_tick))\n", "    #         else:\n", "    #             # Add to tick queue for regular indices\n", "    #             self.tick_queue.put_nowait((token, new_tick))\n", "\n", "    #     except Exception as e:\n", "    #         # Log unexpected errors\n", "    #         self.logger.error(f\"Error processing tick data: {e}\", exc_info=True)\n", "    \n", "    #### ask bid with future\n", "    def event_handler_feed_update(self, tick_data):\n", "        \"\"\"Handles incoming tick data for indices, options, and futures.\"\"\"\n", "        try:\n", "            # Extract message type\n", "            msg_type = tick_data.get('t')\n", "            token = tick_data.get('tk')\n", "            price = tick_data.get('lp')\n", "            timestamp = tick_data.get('ft')\n", "\n", "            # Skip if token or price is missing\n", "            if not token or not price:\n", "                return  # Skip silently without logging\n", "\n", "            # Convert timestamp to ISO format if available, else use current time\n", "            if timestamp:\n", "                try:\n", "                    timest = datetime.fromtimestamp(int(timestamp)).isoformat()\n", "                except (ValueErro<PERSON>, TypeError):\n", "                    timest = datetime.now().isoformat()\n", "            else:\n", "                timest = datetime.now().isoformat()\n", "\n", "            # Create new tick with basic data\n", "            new_tick = {\n", "                'tt': timest, \n", "                'ltp': float(price) if price else None\n", "            }\n", "            \n", "            # Add volume if available\n", "            if 'v' in tick_data:\n", "                new_tick['v'] = int(tick_data['v'])\n", "            \n", "            # For depth messages or initial touchline, extract all levels of market depth\n", "            if msg_type in ['dk', 'tk']:\n", "                # Extract bid levels (1-5)\n", "                for i in range(1, 6):\n", "                    bp_key = f'bp{i}'\n", "                    bq_key = f'bq{i}'\n", "                    bo_key = f'bo{i}'\n", "                    \n", "                    if bp_key in tick_data:\n", "                        new_tick[bp_key] = float(tick_data[bp_key])\n", "                    if bq_key in tick_data:\n", "                        new_tick[bq_key] = int(tick_data[bq_key])\n", "                    if bo_key in tick_data:\n", "                        new_tick[bo_key] = int(tick_data[bo_key])\n", "                \n", "                # Extract sell levels (1-5)\n", "                for i in range(1, 6):\n", "                    sp_key = f'sp{i}'\n", "                    sq_key = f'sq{i}'\n", "                    so_key = f'so{i}'\n", "                    \n", "                    if sp_key in tick_data:\n", "                        new_tick[sp_key] = float(tick_data[sp_key])\n", "                    if sq_key in tick_data:\n", "                        new_tick[sq_key] = int(tick_data[sq_key])\n", "                    if so_key in tick_data:\n", "                        new_tick[so_key] = int(tick_data[so_key])\n", "                        \n", "                # Extract total quantities if available\n", "                if 'tbq' in tick_data:\n", "                    new_tick['tbq'] = int(tick_data['tbq'])  # Total Buy Quantity\n", "                if 'tsq' in tick_data:\n", "                    new_tick['tsq'] = int(tick_data['tsq'])  # Total Sell Quantity\n", "            \n", "            # For touchline updates, only extract the fields that are present\n", "            elif msg_type == 'tf':\n", "                # Add any fields that are present in the update\n", "                for key in ['bp1', 'bq1', 'sp1', 'sq1']:\n", "                    if key in tick_data:\n", "                        new_tick[key] = float(tick_data[key]) if key in ['bp1', 'sp1'] else int(tick_data[key])\n", "            \n", "            # Determine where to route the tick\n", "            if self.option_manager and token in self.option_manager.option_subscriptions:\n", "                # Handle option tick asynchronously\n", "                asyncio.run_coroutine_threadsafe(\n", "                    self.option_manager.handle_option_tick(token, new_tick), \n", "                    loop\n", "                )\n", "            elif self.future_manager and token in self.future_manager.future_subscriptions:\n", "                # Handle future tick asynchronously\n", "                asyncio.run_coroutine_threadsafe(\n", "                    self.future_manager.handle_future_tick(token, new_tick), \n", "                    loop\n", "                )\n", "            else:\n", "                # Add to tick queue for regular indices\n", "                self.tick_queue.put_nowait((token, new_tick))\n", "\n", "        except Exception as e:\n", "            # Log unexpected errors\n", "            self.logger.error(f\"Error processing tick data: {e}\", exc_info=True)\n", "        \n", "    \n", "    async def process_tick_queue(self):\n", "        while True:\n", "            while not self.tick_queue.empty():\n", "                token, tick = await self.tick_queue.get()\n", "                # Use a lock to ensure thread-safe operations on ring_buffers and last_tick_time\n", "                async with self.processing_lock:\n", "                    # Avoid unnecessary new dictionary creation by updating in-place\n", "                    self.ring_buffers[token].append(tick)\n", "                    self.last_tick_time[token] = datetime.fromisoformat(tick['tt'])\n", "                    \n", "                    if token in self.resampling_enabled:\n", "                        await self.resampler.process_tick(\n", "                            token,\n", "                            tick,\n", "                            self.resampling_enabled[token]\n", "                        )\n", "            # Use a smaller sleep to make the loop more responsive if needed\n", "            await asyncio.sleep(0.001)  # Reduced from 0.01 to be more responsive\n", "\n", "    async def connect_and_subscribe(self):\n", "        \"\"\"Connects to WebSocket and subscribes to tick data.\"\"\"\n", "        retry_delay = 1\n", "        max_retry_delay = 32\n", "        max_retries = 10\n", "        retries = 0\n", "        \n", "        while retries < max_retries:\n", "            try:\n", "                self.api.start_websocket(\n", "                    order_update_callback=self.event_handler_order_update,\n", "                    subscribe_callback=self.event_handler_feed_update,\n", "                    socket_open_callback=self.open_callback,\n", "                    socket_close_callback=self.close_callback\n", "                )\n", "                await self.wait_for_feed_open(timeout=30)\n", "                self.logger.info(\"WebSocket connected successfully.\")\n", "                \n", "                # # Add subscriptions\n", "                #await self.manage_subscriptions('add', 'MCX|439830')\n", "                #await self.manage_subscriptions('add', 'MCX|437800')#\n", "                await self.manage_subscriptions('add', 'NSE|26000')\n", "                await self.manage_subscriptions('add', 'NSE|26009')\n", "\n", "                retry_delay = 1\n", "                retries = 0\n", "                await self.monitor_connection()\n", "                \n", "            except Exception as e:\n", "                self.logger.error(f\"WebSocket connection error: {e}\")\n", "                retries += 1\n", "                self.logger.info(f\"Reconnecting in {retry_delay} seconds... (Attempt {retries}/{max_retries})\")\n", "                await asyncio.sleep(retry_delay)\n", "                retry_delay = min(retry_delay * 2, max_retry_delay)\n", "\n", "        if retries >= max_retries:\n", "            self.logger.error(\"Max retries reached. Exiting.\")\n", "            raise Exception(\"Max retries reached\")\n", "\n", "    async def manage_subscriptions(self, command, subscription):\n", "        \"\"\"Manages subscriptions for tick data.\"\"\"\n", "        token = subscription.split('|')[1]\n", "\n", "        if command == 'add':\n", "            if subscription not in self.active_subscriptions:\n", "                self.api.subscribe([subscription])\n", "                self.active_subscriptions.add(subscription)\n", "                self.logger.info(f\"Subscribed to {subscription}\")\n", "                \n", "                self.initialize_resampling_enabled(token)\n", "                for timeframe in self.VALID_TIMEFRAMES:\n", "                    await self.set_resampling(token, timeframe, True)\n", "            else:\n", "                self.logger.warning(f\"Already subscribed to {subscription}\")\n", "        elif command == 'remove':\n", "            if subscription in self.active_subscriptions:\n", "                self.api.unsubscribe([subscription])\n", "                self.active_subscriptions.remove(subscription)\n", "                self.logger.info(f\"Unsubscribed from {subscription}\")\n", "            else:\n", "                self.logger.warning(f\"Not subscribed to {subscription}\")\n", "\n", "    async def wait_for_feed_open(self, timeout):\n", "        \"\"\"Waits for WebSocket feed to open.\"\"\"\n", "        start_time = asyncio.get_event_loop().time()\n", "        while not self.feed_opened:\n", "            if asyncio.get_event_loop().time() - start_time > timeout:\n", "                raise TimeoutError(\"Timed out waiting for feed to open\")\n", "            await asyncio.sleep(1)\n", "\n", "    async def monitor_connection(self):\n", "        \"\"\"Monitors the WebSocket connection.\"\"\"\n", "        while True:\n", "            if not self.feed_opened:\n", "                self.logger.warning(\"Feed closed unexpectedly. Reconnecting...\")\n", "                raise Exception(\"Feed closed\")\n", "            await asyncio.sleep(5)\n", "\n", "    def close_callback(self):\n", "        \"\"\"Callback function for WebSocket closure.\"\"\"\n", "        self.feed_opened = False\n", "        self.logger.warning(\"WebSocket connection closed.\")\n", "\n", "    def open_callback(self):\n", "        \"\"\"Callback function for WebSocket opening.\"\"\"\n", "        if not self.feed_opened:\n", "            self.feed_opened = True\n", "            self.logger.info('Feed Opened')\n", "\n", "    def event_handler_order_update(self, data):\n", "        \"\"\"<PERSON>les order updates.\"\"\"\n", "        self.logger.info(f\"Order update: {data}\")\n", "        \n", "    def load_index_config(self):\n", "        with open('config.json') as f:\n", "            config = json.load(f)\n", "            return {str(idx['token']): idx for idx in config['indices']}\n", "        \n", "        \n", "class FutureManager:\n", "    def __init__(self, tick_collector):\n", "        self.logger = logging.getLogger(\"FutureManager\")\n", "        self.config = self._load_config()\n", "        self.csv_cache = {}  # Cache loaded CSV data\n", "        self.future_data = {}  # For storing future tokens and symbols by index token\n", "        self.future_ticks = defaultdict(lambda: {})  # Separate storage for future ticks\n", "        self.tick_collector = tick_collector  # Dependency Injection for index ticks\n", "        self.future_subscriptions = set()  # Separate subscription tracking for futures\n", "        self._setup_futures()\n", "        self._subscribe_to_futures()  # Subscribe to futures immediately\n", "        self.warned_tokens = set()\n", "\n", "    def _load_config(self):\n", "        try:\n", "            with open('config.json', 'r') as config_file:\n", "                return json.load(config_file)\n", "        except FileNotFoundError:\n", "            self.logger.error(\"Config file 'config.json' not found.\")\n", "            raise\n", "        except json.JSONDecodeError:\n", "            self.logger.error(\"Config file 'config.json' is not valid JSON.\")\n", "            raise\n", "        except Exception as e:\n", "            self.logger.error(f\"Error loading config: {e}\")\n", "            raise\n", "\n", "    def _setup_futures(self):\n", "        for index in self.config['indices']:\n", "            df = self._load_csv_data(index['index_name'])\n", "            if df is not None:\n", "                self.future_data[index['token']] = self._get_futures(df, index['token'], index['option_exchange'])\n", "                self.logger.debug(f\"Setup futures for index {index['index_name']} ({index['token']})\")\n", "\n", "    def _load_csv_data(self, index_name):\n", "        # Cache CSVs by index name to avoid repeated loading\n", "        if index_name not in self.csv_cache:\n", "            for idx in self.config['indices']:\n", "                if idx['index_name'] == index_name:\n", "                    try:\n", "                        df = pd.read_csv(idx['trading_symbol_file'])\n", "                        df = df[df['Symbol'] == index_name]\n", "                        self.csv_cache[index_name] = df\n", "                        self.logger.debug(f\"Loaded CSV for {index_name}.\")\n", "                    except Exception as e:\n", "                        self.logger.error(f\"Failed to load CSV for {index_name}: {e}\")\n", "                        self.csv_cache[index_name] = None\n", "        return self.csv_cache.get(index_name)\n", "\n", "    def _get_futures(self, df, index_token, option_exchange):\n", "        # Filter for futures (FUTIDX)\n", "        futures_df = df[df['Instrument'] == 'FUTIDX']\n", "        \n", "        if futures_df.empty:\n", "            self.logger.warning(f\"No futures found for index token {index_token}\")\n", "            return {}\n", "        \n", "        # Sort by expiry to get the nearest ones\n", "        futures_df = futures_df.sort_values(by='Expiry')\n", "        \n", "        # Take the nearest expiry (current month future)\n", "        nearest_expiry = futures_df['Expiry'].iloc[0]\n", "        current_future = futures_df[futures_df['Expiry'] == nearest_expiry]\n", "        \n", "        # If there's a next month future, get that too\n", "        futures = {}\n", "        if len(futures_df['Expiry'].unique()) > 1:\n", "            next_expiry = futures_df['Expiry'].unique()[1]\n", "            next_future = futures_df[futures_df['Expiry'] == next_expiry]\n", "            \n", "            if not next_future.empty:\n", "                row = next_future.iloc[0]\n", "                futures[\"NEXT_MONTH\"] = {\n", "                    'token': str(row['Token']), \n", "                    'symbol': row['TradingSymbol'],\n", "                    'expiry': row['Expiry']\n", "                }\n", "        \n", "        # Add current month future\n", "        if not current_future.empty:\n", "            row = current_future.iloc[0]\n", "            futures[\"CURRENT_MONTH\"] = {\n", "                'token': str(row['Token']), \n", "                'symbol': row['TradingSymbol'],\n", "                'expiry': row['Expiry']\n", "            }\n", "            \n", "        self.logger.debug(f\"Found futures for {index_token}: {futures}\")\n", "        return futures\n", "\n", "    def _subscribe_to_futures(self):\n", "        \"\"\"Subscribe to all futures for each index at initialization.\"\"\"\n", "        for index in self.config['indices']:\n", "            index_token = str(index['token'])\n", "            if index_token in self.future_data:\n", "                for future in self.future_data[index_token].values():\n", "                    subscription = f\"{index['option_exchange']}|{future['token']}\"\n", "                    self.tick_collector.api.subscribe([subscription])\n", "                    self.future_subscriptions.add(future['token'])  # Only numeric part for comparison\n", "                    self.logger.info(f\"Subscribed to future {subscription} for index {index_token}\")\n", "\n", "    async def update_futures(self):\n", "        \"\"\"Periodically check and update futures subscriptions if needed.\"\"\"\n", "        while True:\n", "            for index in self.config['indices']:\n", "                df = self._load_csv_data(index['index_name'])\n", "                if df is not None:\n", "                    new_futures = self._get_futures(df, index['token'], index['option_exchange'])\n", "                    \n", "                    if new_futures:\n", "                        await self._update_future_subscriptions(index, new_futures)\n", "                        # Update future_data\n", "                        self.future_data[index['token']] = new_futures\n", "\n", "            await asyncio.sleep(3600)  # Check for new futures once per hour\n", "\n", "    async def _update_future_subscriptions(self, index, new_futures):\n", "        index_token = str(index['token'])\n", "        for key, new_future in new_futures.items():\n", "            subscription = f\"{index['option_exchange']}|{new_future['token']}\"\n", "            if new_future['token'] not in self.future_subscriptions:\n", "                self.tick_collector.api.subscribe([subscription])\n", "                self.future_subscriptions.add(new_future['token'])\n", "                self.logger.info(f\"Subscribed to new future {subscription} for index {index_token}\")\n", "\n", "    async def handle_future_tick(self, token, tick):\n", "        for index_token, futures in self.future_data.items():\n", "            for future in futures.values():\n", "                if future['token'] == token:\n", "                    if index_token not in self.future_ticks:\n", "                        self.future_ticks[index_token] = {}\n", "                    if token not in self.future_ticks[index_token]:\n", "                        self.future_ticks[index_token][token] = deque(maxlen=200)\n", "                    \n", "                    # Store the comprehensive tick data with all market depth levels\n", "                    self.future_ticks[index_token][token].append(tick)\n", "                    \n", "                    # Optional: Log detailed market depth if debug is enabled\n", "                    if self.logger.isEnabledFor(logging.DEBUG):\n", "                        # Create a summary of the market depth info\n", "                        bid_info = []\n", "                        ask_info = []\n", "                        \n", "                        for i in range(1, 6):\n", "                            if f'bp{i}' in tick and f'bq{i}' in tick:\n", "                                bid_info.append(f\"L{i}:{tick[f'bp{i}']}x{tick[f'bq{i}']}\")\n", "                            if f'sp{i}' in tick and f'sq{i}' in tick:\n", "                                ask_info.append(f\"L{i}:{tick[f'sp{i}']}x{tick[f'sq{i}']}\")\n", "                        \n", "                        depth_info = f\"Bids: {', '.join(bid_info)} | Asks: {', '.join(ask_info)}\"\n", "                        vol_info = f\"Vol: {tick.get('v', 'N/A')}\"\n", "                        \n", "                        self.logger.debug(f\"Updated future tick for {index_token} with token {token}: LTP:{tick['ltp']} {depth_info} {vol_info}\")\n", "                    return\n", "                    \n", "        if token not in self.warned_tokens:\n", "            self.logger.warning(f\"Received tick for unknown future token: {token}\")\n", "            self.warned_tokens.add(token)\n", "            \n", "\n", "class OptionManager:\n", "    def __init__(self, tick_collector):\n", "        self.logger = logging.getLogger(\"OptionManager\")\n", "        self.config = self._load_config()\n", "        self.csv_cache = {}  # Cache loaded CSV data\n", "        self.option_data = {}  # For storing option tokens and symbols by index token\n", "        self.option_ticks = defaultdict(lambda: {})  # Separate storage for option ticks\n", "        self.tick_collector = tick_collector  # Dependency Injection for index ticks\n", "        self.option_subscriptions = set()  # Separate subscription tracking for options\n", "        self._setup_options()        \n", "        self._subscribe_to_options()  # Subscribe to options immediately\n", "        self.warned_tokens = set()\n", "\n", "    def _load_config(self):\n", "        try:\n", "            with open('config.json', 'r') as config_file:\n", "                return json.load(config_file)\n", "        except FileNotFoundError:\n", "            self.logger.error(\"Config file 'config.json' not found.\")\n", "            raise\n", "        except json.JSONDecodeError:\n", "            self.logger.error(\"Config file 'config.json' is not valid JSON.\")\n", "            raise\n", "        except Exception as e:\n", "            self.logger.error(f\"Error loading config: {e}\")\n", "            raise\n", "\n", "    def _setup_options(self):\n", "        for index in self.config['indices']:\n", "            df = self._load_csv_data(index['index_name'])\n", "            if df is not None:\n", "                df = df.sort_values(by='Expiry')\n", "                self.option_data[index['token']] = self._get_options(df, index['token'], index['strike_difference'], index['option_exchange'])\n", "   \n", "    def _load_csv_data(self, index_name):\n", "        # Cache CSVs by index name to avoid repeated loading\n", "        if index_name not in self.csv_cache:\n", "            for idx in self.config['indices']:\n", "                if idx['index_name'] == index_name:\n", "                    try:\n", "                        df = pd.read_csv(idx['trading_symbol_file'])\n", "                        df = df[df['Symbol'] == index_name]\n", "                        self.csv_cache[index_name] = df\n", "                        self.logger.debug(f\"Loaded CSV for {index_name}.\")\n", "                    except Exception as e:\n", "                        self.logger.error(f\"Failed to load CSV for {index_name}: {e}\")\n", "                        self.csv_cache[index_name] = None\n", "        return self.csv_cache.get(index_name)\n", "\n", "    def _get_options(self, df, index_token, strike_difference, option_exchange):\n", "        nearest_expiry = df['Expiry'].iloc[0]\n", "        #\n", "        ltp = self.tick_collector.ring_buffers[str(index_token)][-1]['ltp'] if str(index_token) in self.tick_collector.ring_buffers and self.tick_collector.ring_buffers[str(index_token)] else None\n", "        \n", "        \n", "        if ltp:\n", "            atm_strike = round(ltp / strike_difference) * strike_difference\n", "            self.logger.debug(f\"LTP: {ltp}, ATM Strike: {atm_strike}\")\n", "            options = {}\n", "            self.logger.debug(f\"Generated options for {index_token}: {options}\")\n", "            \n", "            # ATM Options\n", "            atm_data = df[(df['Expiry'] == nearest_expiry) & (df['StrikePrice'] == atm_strike)]\n", "            for _, row in atm_data.iterrows():\n", "                options[f\"ATM_{row['OptionType']}\"] = {'token': str(row['Token']), 'symbol': row['TradingSymbol']}            \n", "            # ITM and OTM Options\n", "            for option_type in ['CE', 'PE']:\n", "                if option_type == 'CE':\n", "                    itm_options = df[(df['Expiry'] == nearest_expiry) & (df['OptionType'] == option_type) & (df['StrikePrice'] < atm_strike)].nlargest(5, 'StrikePrice')\n", "                    otm_options = df[(df['Expiry'] == nearest_expiry) & (df['OptionType'] == option_type) & (df['StrikePrice'] > atm_strike)].nsmallest(5, 'StrikePrice')\n", "                else:  # PE\n", "                    itm_options = df[(df['Expiry'] == nearest_expiry) & (df['OptionType'] == option_type) & (df['StrikePrice'] > atm_strike)].nsmallest(5, 'StrikePrice')\n", "                    otm_options = df[(df['Expiry'] == nearest_expiry) & (df['OptionType'] == option_type) & (df['StrikePrice'] < atm_strike)].nlargest(5, 'StrikePrice')\n", "                \n", "                for i, row in enumerate(itm_options.itertuples(), start=1):\n", "                    options[f\"ITM_{option_type}_{i}\"] = {'token': str(row.Token), 'symbol': row.TradingSymbol}\n", "                for i, row in enumerate(otm_options.itertuples(), start=1):\n", "                    options[f\"OTM_{option_type}_{i}\"] = {'token': str(row.Token), 'symbol': row.TradingSymbol}\n", "            return options\n", "        return {}\n", "\n", "    def _subscribe_to_options(self):\n", "        \"\"\"Subscribe to all options for each index at initialization.\"\"\"\n", "        for index in self.config['indices']:\n", "            index_token = str(index['token'])\n", "            if index_token in self.option_data:\n", "                for option in self.option_data[index_token].values():\n", "                    subscription = f\"{index['option_exchange']}|{option['token']}\"\n", "                    self.tick_collector.api.subscribe([subscription])\n", "                    self.option_subscriptions.add(option['token'])  # Only numeric part for comparison\n", "                    self.logger.info(f\"Subscribed to option {subscription} for index {index_token}\")\n", "\n", "    async def update_options(self):\n", "        while True:\n", "            for index in self.config['indices']:\n", "                df = self._load_csv_data(index['index_name'])\n", "                if df is not None:\n", "                    new_options = self._get_options(df, index['token'], index['strike_difference'], index['option_exchange'])\n", "                    \n", "                    if new_options:\n", "                        await self._update_option_subscriptions(index, new_options)\n", "                        # Update option_data with new designations but retain existing tokens\n", "                        self.option_data[index['token']] = self._merge_options(self.option_data[index['token']], new_options)\n", "\n", "            await asyncio.sleep(10)  # Adjust this interval based on how often you want to check for index movement\n", "\n", "    def _merge_options(self, current_options, new_options):\n", "        merged = current_options.copy()\n", "        for key, new_option in new_options.items():\n", "            # If the token exists in current options, update its designation\n", "            for current_key, current_option in list(merged.items()):\n", "                if current_option['token'] == new_option['token'] and current_key != key:\n", "                    merged[key] = merged.pop(current_key)\n", "                    break\n", "            else:\n", "                # If it's a new token, add it\n", "                merged[key] = new_option\n", "        return merged\n", "\n", "    async def _update_option_subscriptions(self, index, new_options):\n", "        index_token = str(index['token'])\n", "        for key, new_option in new_options.items():\n", "            subscription = f\"{index['option_exchange']}|{new_option['token']}\"\n", "            if new_option['token'] not in self.option_subscriptions:\n", "                self.tick_collector.api.subscribe([subscription])\n", "                self.option_subscriptions.add(new_option['token'])\n", "                self.logger.info(f\"Subscribed to new option {subscription} for index {index_token}\")\n", "\n", "\n", "    ### simple without ask bid -works\n", "    # async def handle_option_tick(self, token, tick):\n", "    #     for index_token, options in self.option_data.items():\n", "    #         for option in options.values():\n", "    #             if option['token'] == token:\n", "    #                 if index_token not in self.option_ticks:\n", "    #                     self.option_ticks[index_token] = {}\n", "    #                 if token not in self.option_ticks[index_token]:\n", "    #                     self.option_ticks[index_token][token] = deque(maxlen=200)\n", "    #                 self.option_ticks[index_token][token].append(tick)\n", "    #                 self.logger.debug(f\"Updated option tick for {index_token} with token {token}: {tick}\")\n", "    #                 return\n", "    #     if token not in self.warned_tokens:\n", "    #         self.logger.warning(f\"Received tick for unknown option token: {token}\")\n", "    #         self.warned_tokens.add(token)\n", "    \n", "    ### simple with ask bid -works\n", "    async def handle_option_tick(self, token, tick):\n", "        for index_token, options in self.option_data.items():\n", "            for option in options.values():\n", "                if option['token'] == token:\n", "                    if index_token not in self.option_ticks:\n", "                        self.option_ticks[index_token] = {}\n", "                    if token not in self.option_ticks[index_token]:\n", "                        self.option_ticks[index_token][token] = deque(maxlen=200)\n", "                    \n", "                    # Store the comprehensive tick data with all market depth levels\n", "                    self.option_ticks[index_token][token].append(tick)\n", "                    \n", "                    # Optional: Log detailed market depth if debug is enabled\n", "                    if self.logger.isEnabledFor(logging.DEBUG):\n", "                        # Create a summary of the market depth info\n", "                        bid_info = []\n", "                        ask_info = []\n", "                        \n", "                        for i in range(1, 6):\n", "                            if f'bp{i}' in tick and f'bq{i}' in tick:\n", "                                bid_info.append(f\"L{i}:{tick[f'bp{i}']}x{tick[f'bq{i}']}\")\n", "                            if f'sp{i}' in tick and f'sq{i}' in tick:\n", "                                ask_info.append(f\"L{i}:{tick[f'sp{i}']}x{tick[f'sq{i}']}\")\n", "                        \n", "                        depth_info = f\"Bids: {', '.join(bid_info)} | Asks: {', '.join(ask_info)}\"\n", "                        vol_info = f\"Vol: {tick.get('v', 'N/A')}\"\n", "                        \n", "                        self.logger.debug(f\"Updated option tick for {index_token} with token {token}: LTP:{tick['ltp']} {depth_info} {vol_info}\")\n", "                    return\n", "                    \n", "        if token not in self.warned_tokens:\n", "            self.logger.warning(f\"Received tick for unknown option token: {token}\")\n", "            self.warned_tokens.add(token)\n", "    \n", "    \n", "\n", "class StrategyManager:\n", "    def __init__(self, tick_collector, trade_manager):       \n", "        self.tick_collector = tick_collector\n", "        self.indicator_manager = tick_collector.resampler.indicator_manager\n", "        self.strategies = {}\n", "        self.monitored_keys = {}\n", "        self.trade_manager = trade_manager\n", "        self.trade_manager.strategy_manager = self\n", "        self.trade_controller = TradeController(self)  # Initialize and attach TradeController\n", "        \n", "        # Add a logger\n", "        self.logger = logging.getLogger(\"StrategyManager\")\n", "        \n", "\n", "    async def add_strategy(self, token, timeframe, strategy_id, indicators, \n", "                         strategy_logic, default_sl=None, default_target=None):\n", "        if token not in self.tick_collector.resampling_enabled:\n", "            raise ValueError(f\"Token {token} is not being resampled.\")\n", "        if timeframe not in self.tick_collector.resampling_enabled[token]:\n", "            raise ValueError(f\"Timeframe {timeframe} is not being resampled for Token {token}.\")\n", "\n", "        for indicator in indicators:\n", "            base_indicator = indicator.rsplit('_', 1)[0]\n", "            if base_indicator not in self.indicator_manager.configs:\n", "                raise ValueError(f\"Indicator {indicator} is not registered.\")\n", "            valid_timeframes = self.indicator_manager.configs[base_indicator].timeframes\n", "            if timeframe not in valid_timeframes:\n", "                raise ValueError(\n", "                    f\"Indicator {indicator} does not support Timeframe {timeframe}. \"\n", "                    f\"Supported timeframes: {valid_timeframes}\"\n", "                )\n", "\n", "        strategy_key = (token, timeframe, strategy_id)\n", "        if strategy_key not in self.strategies:\n", "            self.strategies[strategy_key] = {\n", "                \"indicators\": indicators,\n", "                \"logic\": strategy_logic,\n", "                \"enabled\": True,\n", "                \"task\": None,\n", "                \"default_sl\": default_sl,\n", "                \"default_target\": default_target\n", "            }\n", "\n", "        for indicator in indicators:\n", "            key = (token, timeframe, indicator)\n", "            self.monitored_keys[key] = {\"latest\": None, \"previous\": None}\n", "            if key not in self.indicator_manager.subscriptions:\n", "                await self.indicator_manager.subscribe(token, timeframe, indicator)\n", "\n", "        task = asyncio.create_task(self.monitor_indicators(token, timeframe, indicators, strategy_id))\n", "        self.strategies[strategy_key][\"task\"] = task\n", "        #await broadcast_strategy_updates()  # Broadcast the update\n", "        asyncio.create_task(broadcast_strategy_updates())\n", "        \n", "    \n", "    ### only call logic if candle value change is not candle height\n", "    async def monitor_indicators(self, token, timeframe, indicators, strategy_id):\n", "        keys = [(token, timeframe, indicator) for indicator in indicators]\n", "        while True:\n", "            for key in keys:\n", "                if key in self.indicator_manager.subscriptions:\n", "                    deque_data = self.indicator_manager.subscriptions[key]\n", "                    if deque_data:\n", "                        timestamp, current_value = deque_data[-1]\n", "                        state = self.monitored_keys[key]\n", "                        state[\"previous\"] = state[\"latest\"]\n", "                        if state[\"latest\"] != current_value:\n", "                            state[\"latest\"] = current_value\n", "                            # Check if the changed indicator is not 'candle_height_1'\n", "                            if key[2] != 'candle_height_1':\n", "                                strategy = self.strategies.get((token, timeframe, strategy_id))\n", "                                if strategy and strategy[\"enabled\"]:\n", "                                    await self.evaluate_strategy_logic(token, timeframe, strategy_id, indicators)\n", "            await asyncio.sleep(0.1)\n", "\n", "    async def evaluate_strategy_logic(self, token, timeframe, strategy_id, indicators):\n", "        strategy = self.strategies.get((token, timeframe, strategy_id))\n", "        if not strategy or not strategy[\"enabled\"]:\n", "            return\n", "            \n", "        states = {\n", "            indicator: self.monitored_keys[(token, timeframe, indicator)]\n", "            for indicator in indicators\n", "        }\n", "        await strategy[\"logic\"](token, timeframe, strategy_id, states, \n", "                              self.tick_collector, self.trade_manager)\n", "\n", "    async def enable_strategy(self, token, timeframe, strategy_id):\n", "        if (token, timeframe, strategy_id) in self.strategies:\n", "            self.strategies[(token, timeframe, strategy_id)][\"enabled\"] = True\n", "            # Broadcast the update\n", "            #await broadcast_strategy_updates()  # Broadcast the update\n", "            asyncio.create_task(broadcast_strategy_updates())\n", "            self.logger.info(f\"Strategy {strategy_id} for Token: {token}, Timeframe: {timeframe} enabled.\")\n", "\n", "    async def disable_strategy(self, token, timeframe, strategy_id):\n", "        if (token, timeframe, strategy_id) in self.strategies:\n", "            self.strategies[(token, timeframe, strategy_id)][\"enabled\"] = False\n", "            #await broadcast_strategy_updates()  # Broadcast the update\n", "            asyncio.create_task(broadcast_strategy_updates())\n", "            self.logger.info(f\"Strategy {strategy_id} for Token: {token}, Timeframe: {timeframe} disabled.\")\n", "\n", "    async def stop_strategy(self, token, timeframe, strategy_id):\n", "        if (token, timeframe, strategy_id) in self.strategies:\n", "            strategy = self.strategies[(token, timeframe, strategy_id)]\n", "            if strategy[\"task\"]:\n", "                strategy[\"task\"].cancel()\n", "            del self.strategies[(token, timeframe, strategy_id)]\n", "            #await broadcast_strategy_updates()  # Broadcast the update\n", "            asyncio.create_task(broadcast_strategy_updates())\n", "            \n", "\n", "@dataclass\n", "class Trade:     \n", "    index_token: str\n", "    timeframe: str\n", "    option_token: str\n", "    option_symbol: str\n", "    action: str\n", "    entry_price: float\n", "    entry_time: str\n", "    order_id: str\n", "    default_sl: Optional[float] = None\n", "    default_target: Optional[float] = None\n", "    trail_sl: Optional[float] = None\n", "    trail_sl_trigger: Optional[float] = None\n", "    max_profit: float = 0\n", "    current_pnl: float = 0\n", "    trade_history: List[Dict] = field(default_factory=list)\n", "    exit_reason: Optional[str] = None\n", "    exit_price: Optional[float] = None\n", "    exit_time: Optional[str] = None\n", "    highest_price: float = field(init=False)  # New field\n", "    \n", "\n", "    def __post_init__(self):\n", "        if self.trade_history is None:\n", "            self.trade_history = []\n", "        self.highest_price = self.entry_price  # Initialize with entry price\n", "        self._record_event(\"Trade Opened\", details={\n", "            \"entry_price\": self.entry_price,\n", "            \"option_symbol\": self.option_symbol,\n", "            \"action\": self.action,\n", "            \"timeframe\": self.timeframe\n", "        })\n", "               \n", "    \n", "    @property\n", "    def trade_key(self):\n", "        return f\"{self.order_id}_{self.option_symbol}\"\n", "\n", "    def _record_event(self, event_type, details=None):\n", "        if details is None:\n", "            details = {}\n", "        event = {\n", "            \"timestamp\": datetime.now().isoformat(),\n", "            \"event_type\": event_type,\n", "            **details\n", "        }\n", "        self.trade_history.append(event)\n", "\n", "       \n", "    def update_pnl(self, current_price: float):\n", "        \n", "        if self.exit_price is not None:\n", "            return  # 🚨 Prevent updates on closed trades!\n", "    \n", "        multiplier = 1 if self.action == \"BUY\" else 1  # Fix for SELL trades\n", "        new_pnl = (current_price - self.entry_price) * multiplier\n", "        \n", "        if new_pnl != self.current_pnl:  # Only update if there's a change\n", "            self.current_pnl = new_pnl\n", "            self.max_profit = max(self.max_profit, self.current_pnl)\n", "            \n", "            self._record_event(\"PnL Update\", details={\n", "                \"current_price\": current_price,\n", "                \"current_pnl\": self.current_pnl,\n", "                \"max_profit\": self.max_profit\n", "            })\n", "            \n", "            # Send real-time updates to UI\n", "            asyncio.create_task(broadcast_trade_updates())\n", "\n", "        return self.current_pnl\n", "\n", "\n", "class TradeManager:\n", "    def __init__(self, tick_collector, option_manager):\n", "        self.active_trades: Dict[str, Trade] = {}\n", "        self.closed_trades: List[Trade] = []\n", "        self.tick_collector = tick_collector\n", "        self.option_manager = option_manager\n", "        self.monitoring_tasks = {}\n", "        self.pnl_tasks = {}\n", "        self.strategy_manager = None\n", "        self.total_pnl = 0\n", "        self.daily_pnl = defaultdict(float)\n", "        \n", "        self.trade_lock = asyncio.Lock()\n", "        self.signal_lock = Lock()\n", "        #self.active_trades: Dict[str, Any] = {}\n", "        \n", "        # Add a logger\n", "        self.logger = logging.getLogger(\"TradeManager\")\n", "                \n", "                \n", "    def _generate_unique_order_id(self):\n", "        while True:\n", "            order_id = f\"ORD-{random.randint(10000, 99999)}\"\n", "            if not any(trade.order_id == order_id for trade in self.active_trades.values()):\n", "                return order_id\n", "\n", "    async def execute_trade(self, index_token: str, timeframe: str, option_token: str, \n", "                          option_symbol: str, action: str, price: float, \n", "                          timestamp: str, default_sl: float = None, \n", "                          default_target: float = None) -> Optional[str]:\n", "        index_token = str(index_token)\n", "        if self.check_active_trade(index_token, timeframe):\n", "            self.logger.info(f\"Active trade exists for {index_token} on {timeframe}\")\n", "            return None\n", "\n", "        order_id = self._generate_unique_order_id()\n", "        new_trade = Trade(\n", "            index_token=index_token,\n", "            timeframe=timeframe,\n", "            option_token=option_token,\n", "            option_symbol=option_symbol,\n", "            action=action,\n", "            entry_price=price,\n", "            entry_time=timestamp,\n", "            order_id=order_id,\n", "            default_sl=default_sl,\n", "            default_target=default_target\n", "        )\n", "        \n", "        if default_sl is not None:\n", "            new_trade.trail_sl = default_sl\n", "            new_trade.trail_sl_trigger = new_trade.entry_price - new_trade.trail_sl\n", "            new_trade._record_event(\"Trail SL Set\", details={\"trail_sl_trigger\": new_trade.trail_sl_trigger})\n", "            self.logger.info(f\"Trail SL set for {new_trade.trade_key} at {new_trade.trail_sl_trigger}\")\n", "\n", "        self.active_trades[new_trade.trade_key] = new_trade\n", "        self.logger.info(f\"Trade executed: {action} {option_symbol} ({option_token}) \"\n", "                        f\"Index: {index_token} TF: {timeframe} Order: {order_id} \"\n", "                        f\"Price: {price} Time: {timestamp}\")\n", "        \n", "        self.monitoring_tasks[new_trade.trade_key] = asyncio.create_task(self._monitor_trade(new_trade.trade_key))\n", "        \n", "        self.pnl_tasks[new_trade.trade_key] = asyncio.create_task(\n", "            self._update_trade_pnl(new_trade.trade_key)\n", "        )\n", "               \n", "        asyncio.create_task(broadcast_trade_updates())\n", "        return order_id\n", "    \n", "    \n", "    async def _update_trade_pnl(self, trade_key: str):\n", "        while True:\n", "            async with self.trade_lock:  # 🔒 Ensures safe access\n", "                if trade_key not in self.active_trades:\n", "                    break\n", "                trade = self.active_trades[trade_key]\n", "\n", "            # Fetch the latest price\n", "            option_ticks = self.option_manager.option_ticks.get(\n", "                int(trade.index_token), {}\n", "            ).get(trade.option_token, deque())\n", "            if option_ticks:\n", "                current_price = option_ticks[-1]['ltp']\n", "                trade.update_pnl(current_price)\n", "\n", "            await asyncio.sleep(0.1)\n", "\n", "    async def _monitor_trade(self, trade_key: str):\n", "        while trade_key in self.active_trades:\n", "            trade = self.active_trades[trade_key]\n", "            option_ticks = self.option_manager.option_ticks.get(\n", "                int(trade.index_token), {}\n", "            ).get(trade.option_token, deque())\n", "            \n", "            if option_ticks:\n", "                current_price = option_ticks[-1]['ltp']\n", "                current_tick_time = option_ticks[-1]['tt']\n", "                \n", "                await self._check_sl_target(trade_key, current_price)\n", "                await self._check_trailing_sl(trade_key, current_price)\n", "                # Broadcast changes\n", "                asyncio.create_task(broadcast_trade_updates())\n", "            await asyncio.sleep(.5)\n", "            \n", "    \n", "    async def _check_sl_target(self, trade_key: str, current_price: float):\n", "        trade = self.active_trades.get(trade_key)\n", "        if not trade:\n", "            return\n", "\n", "        if trade.action == \"BUY\":\n", "            if (trade.default_sl and \n", "                current_price <= (trade.entry_price - trade.default_sl)):\n", "                trade.exit_reason = \"SL Hit\"\n", "                await self.close_trade(\n", "                    trade.index_token, trade.timeframe, trade.option_symbol,\n", "                    trade.option_token, trade.order_id, current_price,\n", "                    datetime.now().isoformat()\n", "                )\n", "            elif (trade.default_target and \n", "                  current_price >= (trade.entry_price + trade.default_target)):\n", "                trade.exit_reason = \"Target Hit\"\n", "                await self.close_trade(\n", "                    trade.index_token, trade.timeframe, trade.option_symbol,\n", "                    trade.option_token, trade.order_id, current_price,\n", "                    datetime.now().isoformat()\n", "                )\n", "        else:  # SELL\n", "            if (trade.default_sl and \n", "                current_price <= (trade.entry_price - trade.default_sl)):\n", "                trade.exit_reason = \"SL Hit\"\n", "                await self.close_trade(\n", "                    trade.index_token, trade.timeframe, trade.option_symbol,\n", "                    trade.option_token, trade.order_id, current_price,\n", "                    datetime.now().isoformat()\n", "                )\n", "            elif (trade.default_target and \n", "                  current_price >= (trade.entry_price + trade.default_target)):\n", "                trade.exit_reason = \"Target Hit\"\n", "                await self.close_trade(\n", "                    trade.index_token, trade.timeframe, trade.option_symbol,\n", "                    trade.option_token, trade.order_id, current_price,\n", "                    datetime.now().isoformat()\n", "                )\n", "                \n", "    async def _check_trailing_sl(self, trade_key: str, current_price: float):\n", "        pass\n", "              \n", "        \n", "    def update_sl(self, trade_key: str, new_sl: float):\n", "        trade = self.active_trades.get(trade_key)\n", "        if trade:\n", "            trade.default_sl = new_sl\n", "            trade._record_event(\"SL Updated\", details={\"new_sl\": new_sl})\n", "            self.logger.info(f\"Updated SL for {trade_key} to {new_sl}\")\n", "            # Notify clients of update\n", "            asyncio.create_task(broadcast_trade_updates())\n", "\n", "    def update_target(self, trade_key: str, new_target: float):\n", "        trade = self.active_trades.get(trade_key)\n", "        if trade:\n", "            trade.default_target = new_target\n", "            trade._record_event(\"Target Updated\", details={\"new_target\": new_target})\n", "            self.logger.info(f\"Updated target for {trade_key} to {new_target}\")\n", "            # Notify clients of update\n", "            asyncio.create_task(broadcast_trade_updates())\n", "\n", "    def set_trailing_sl(self, trade_key: str, trail_sl: float):\n", "        trade = self.active_trades.get(trade_key)\n", "        if trade:\n", "            trade.trail_sl = trail_sl\n", "            trade.trail_sl_trigger = None\n", "            trade._record_event(\"Trail SL Set\", details={\"trail_sl\": trail_sl})\n", "            self.logger.info(f\"Set trailing SL for {trade_key} to {trail_sl}\")\n", "            # Notify clients of update\n", "            asyncio.create_task(broadcast_trade_updates())\n", "\n", "    async def exit_all_trades(self):\n", "        trade_keys = list(self.active_trades.keys())\n", "        for trade_key in trade_keys:\n", "            trade = self.active_trades[trade_key]\n", "            option_ticks = self.option_manager.option_ticks.get(\n", "                int(trade.index_token), {}\n", "            ).get(trade.option_token, deque())\n", "            \n", "            if option_ticks:\n", "                current_price = option_ticks[-1]['ltp']\n", "                trade.exit_reason = \"Force Exit\"\n", "                await self.close_trade(\n", "                    trade.index_token, trade.timeframe, trade.option_symbol,\n", "                    trade.option_token, trade.order_id, current_price,\n", "                    datetime.now().isoformat()\n", "                )\n", "        asyncio.create_task(broadcast_trade_updates())\n", "\n", "    async def get_active_positions(self) -> list:\n", "        positions = []\n", "        for trade in self.active_trades.values():\n", "            positions.append({\n", "                \"trade_id\": trade.trade_key,\n", "                \"symbol\": trade.option_symbol,\n", "                \"action\": trade.action,\n", "                \"entry_price\": trade.entry_price,\n", "                \"current_pnl\": trade.current_pnl,\n", "                \"max_profit\": trade.max_profit,\n", "                \"sl\": trade.default_sl,\n", "                \"target\": trade.default_target,\n", "                'trail_sl_trigger': trade.trail_sl_trigger,\n", "                \"trail_sl\": trade.trail_sl\n", "            })\n", "        return positions\n", "\n", "    async def get_trade_history(self, trade_key: str) -> list:\n", "        trade = self.active_trades.get(trade_key)\n", "        if trade:\n", "            return trade.trade_history\n", "        \n", "        for closed_trade in self.closed_trades:\n", "            if closed_trade.trade_key == trade_key:\n", "                return closed_trade.trade_history\n", "        return []\n", "\n", "    async def get_trade_history_display(self):\n", "        all_trades = self.closed_trades + list(self.active_trades.values())\n", "        if not all_trades:\n", "            return \"No trades found\"\n", "            \n", "        trade_history = []\n", "        total_pnl = 0\n", "        \n", "        for trade in all_trades:\n", "            trade_info = {\n", "                \"Trade Key\": trade.trade_key,\n", "                \"Date\": trade.entry_time.split('T')[0],\n", "                \"Time\": trade.entry_time.split('T')[1][:8],\n", "                \"Symbol\": trade.option_symbol,\n", "                \"Signal\": trade.action,\n", "                \"Entry\": f\"{trade.entry_price:.2f}\",\n", "                \"Exit\": f\"{trade.exit_price:.2f}\" if trade.exit_price else \"Active\",\n", "                \"Exit Time\": trade.exit_time.split('T')[1][:8] if trade.exit_time else \"-\",\n", "                \"P&L\": f\"{trade.current_pnl:.2f}\",\n", "                \"Status\": \"Closed\" if trade in self.closed_trades else \"Active\",\n", "                \"Exit Reason\": trade.exit_reason if trade.exit_reason else \"-\"\n", "            }\n", "            trade_history.append(trade_info)\n", "            if trade.exit_price:\n", "                total_pnl += trade.current_pnl\n", "        \n", "        trade_history.sort(key=lambda x: x['Date'] + x['Time'], reverse=True)\n", "        \n", "        headers = [\"Trade Key\", \"Date\", \"Time\", \"Symbol\", \"Signal\", \"Entry\", \"Exit\", \"Exit Time\", \"P&L\", \"Status\", \"Exit Reason\"]        \n", "        output = \"\\nTrade History:\\n\" + \"-\" * 150 + \"\\n\"\n", "        output += \"{:<25} {:<12} {:<10} {:<15} {:<8} {:<10} {:<10} {:<12} {:<12} {:<8} {:<15}\\n\".format(*headers)\n", "        output += \"-\" * 150 + \"\\n\"        \n", "        for trade in trade_history:\n", "            output += \"{Trade Key:<25} {Date:<12} {Time:<10} {Symbol:<15} {Signal:<8} {Entry:<10} {Exit:<10} {Exit Time:<12} {P&L:<12} {Status:<8} {Exit Reason:<15}\\n\".format(**trade)\n", "        \n", "        output += \"-\" * 150 + \"\\n\"\n", "        output += f\"Total P&L: {total_pnl:.2f}\\n\"        \n", "        return output\n", "\n", "        \n", "    async def close_trade(self, index_token: str, timeframe: str, option_symbol: str,\n", "                      option_token: str = None, order_id: str = None,\n", "                      price: float = None, timestamp: str = None):\n", "        async with self.trade_lock:  # 🔒 Ensures safe modification\n", "            trade_key = next(\n", "                (key for key, trade in self.active_trades.items()\n", "                if trade.order_id == order_id and trade.option_symbol == option_symbol),\n", "                None\n", "            )\n", "\n", "            if trade_key and trade_key in self.active_trades:\n", "                trade = self.active_trades[trade_key]\n", "                trade.exit_price = price\n", "                trade.exit_time = timestamp\n", "                trade.exit_reason = \"Normal Exit\" if not trade.exit_reason else trade.exit_reason\n", "\n", "                # Cancel tasks safely\n", "                task_monitor = self.monitoring_tasks.pop(trade_key, None)\n", "                task_pnl = self.pnl_tasks.pop(trade_key, None)\n", "                \n", "                if task_monitor:\n", "                    task_monitor.cancel()\n", "                    try:\n", "                        await task_monitor  # Wait for cancellation\n", "                    except async<PERSON>.CancelledError:\n", "                        pass\n", "                \n", "                if task_pnl:\n", "                    task_pnl.cancel()\n", "                    try:\n", "                        await task_pnl  # Wait for cancellation\n", "                    except async<PERSON>.CancelledError:\n", "                        pass\n", "\n", "                self.closed_trades.append(trade)\n", "                del self.active_trades[trade_key]\n", "\n", "                self.logger.info(f\"Closed trade: {trade_key} at {price}\")\n", "\n", "                await broadcast_trade_updates()\n", "\n", "\n", "    def check_active_trade(self, index_token: str, timeframe: str) -> bool:\n", "        index_token = str(index_token)\n", "        return any(\n", "            str(trade.index_token) == index_token and trade.timeframe == timeframe\n", "            for trade in self.active_trades.values()\n", "        )\n", "\n", "class TradeController:\n", "    def __init__(self, strategy_manager):\n", "        self.strategy_actions = {}  # Stores manual buy/sell permissions for each strategy\n", "        self.strategy_manager = strategy_manager\n", "        self.strategy_manager.trade_controller = self  # Attach to StrategyManager\n", "        print(\"TradeController initialized and attached to StrategyManager.\")\n", "\n", "    def set_manual_control(self, strategy_key, enable_buy, enable_sell):        \n", "    \n", "        # Check if the strategy is enabled in StrategyManager\n", "        strategy = self.strategy_manager.strategies.get(strategy_key)\n", "        if not strategy or not strategy[\"enabled\"]:\n", "            print(f\"Cannot set manual control: Strategy {strategy_key} is not enabled.\")\n", "            return\n", "\n", "        # Set manual control\n", "        if strategy_key not in self.strategy_actions:\n", "            self.strategy_actions[strategy_key] = {\"enable_buy\": True, \"enable_sell\": True}\n", "        self.strategy_actions[strategy_key][\"enable_buy\"] = enable_buy\n", "        self.strategy_actions[strategy_key][\"enable_sell\"] = enable_sell\n", "        print(f\"Manual control updated for {strategy_key}: BUY={enable_buy}, SELL={enable_sell}\")        \n", "        asyncio.create_task(broadcast_strategy_updates())\n", "\n", "    def is_buy_allowed(self, strategy_key):\n", "        strategy = self.strategy_manager.strategies.get(strategy_key)\n", "        if not strategy or not strategy[\"enabled\"]:\n", "            print(f\"Cannot set manual control: Strategy {strategy_key} is not enabled.\")\n", "            return\n", "        \"\"\"Check if BUY is allowed based on manual control.\"\"\"\n", "        return self.strategy_actions.get(strategy_key, {\"enable_buy\": True})[\"enable_buy\"]\n", "\n", "    def is_sell_allowed(self, strategy_key):\n", "        strategy = self.strategy_manager.strategies.get(strategy_key)\n", "        if not strategy or not strategy[\"enabled\"]:\n", "            print(f\"Cannot set manual control: Strategy {strategy_key} is not enabled.\")\n", "            return\n", "        \"\"\"Check if SELL is allowed based on manual control.\"\"\"\n", "        return self.strategy_actions.get(strategy_key, {\"enable_sell\": True})[\"enable_sell\"]\n", "    \n", "\n", "\n", "async def bull_and_bear_trade_logic(token: str, timeframe: str, strategy_id: str, states: Dict,\n", "                                   tick_collector: Any, trade_manager: Any):\n", "    \"\"\"Enhanced trade logic with specific JMA and Supertrend signal combinations\"\"\"\n", "    \n", "    logger = logging.getLogger(\"Logic\")\n", "    \n", "    if not hasattr(bull_and_bear_trade_logic, 'monitoring_status'):\n", "        bull_and_bear_trade_logic.monitoring_status = {}\n", "    monitoring_status = bull_and_bear_trade_logic.monitoring_status\n", "    \n", "    # Get all required states and indicators\n", "    supertrend = states.get(\"supertrend_1\", {\"latest\": None, \"previous\": None})\n", "    jma = states.get(\"jma_1\", {\"latest\": None, \"previous\": None})\n", "    \n", "    # Basic validations\n", "    latest_tick = tick_collector.ring_buffers.get(token, [None])[-1]\n", "    if not latest_tick:\n", "        return\n", "        \n", "    latest_price = latest_tick[\"ltp\"]\n", "    latest_time = latest_tick[\"tt\"]\n", "    \n", "    # Get required managers and data\n", "    option_manager = tick_collector.option_manager\n", "    option_data = option_manager.option_data.get(int(token), {})\n", "    option_ticks = option_manager.option_ticks.get(int(token), {})\n", "    strategy = trade_manager.strategy_manager.strategies.get((token, timeframe, strategy_id))\n", "    \n", "    strategy_key = (token, timeframe, strategy_id)\n", "    trade_controller = trade_manager.strategy_manager.trade_controller\n", "    buy_allowed = trade_controller.is_buy_allowed(strategy_key)\n", "    sell_allowed = trade_controller.is_sell_allowed(strategy_key)\n", "\n", "    monitor_lock = asyncio.Lock()\n", "\n", "    async def execute_option_action(action: str) -> None:\n", "        \"\"\"Execute a new trade with the given action (BUY/SELL)\"\"\"\n", "        try:\n", "            option_type = \"CE\" if action == \"BUY\" else \"PE\"\n", "            atm_option = option_data.get(f\"ATM_{option_type}\")\n", "            \n", "            if not atm_option:\n", "                logger.warning(f\"No ATM option found for {option_type}\")\n", "                return\n", "                \n", "            option_token = atm_option['token']\n", "            option_symbol = atm_option['symbol']\n", "            \n", "            option_ticks_data = option_ticks.get(option_token, [])\n", "            if option_ticks_data and latest_time:\n", "                option_ltp = option_ticks_data[-1]['ltp']\n", "                option_ltp_time = option_ticks_data[-1]['tt']\n", "                await trade_manager.execute_trade(\n", "                    token, timeframe, option_token, option_symbol,\n", "                    action, option_ltp, option_ltp_time,\n", "                    strategy['default_sl'], strategy['default_target']\n", "                )\n", "            else:\n", "                logger.warning(f\"No option ticks data available for {option_symbol}\")\n", "        except Exception as e:\n", "            logger.error(f\"Error in execute_option_action: {e}\")\n", "            import traceback\n", "            logger.error(traceback.format_exc())\n", "\n", "    async def close_option_position(active_trade: Any) -> None:\n", "        \"\"\"Close the given active trade\"\"\"\n", "        try:\n", "            if active_trade and active_trade.option_token in option_ticks:\n", "                ticks_for_option = option_ticks[active_trade.option_token]\n", "                if ticks_for_option:\n", "                    option_ltp = ticks_for_option[-1]['ltp']\n", "                    option_ltp_time = ticks_for_option[-1]['tt']\n", "                    if option_ltp is not None and option_ltp_time is not None:\n", "                        await trade_manager.close_trade(\n", "                            token, timeframe, active_trade.option_symbol,\n", "                            active_trade.option_token, active_trade.order_id,\n", "                            option_ltp, option_ltp_time\n", "                        )\n", "                    else:\n", "                        logger.warning(\"Missing option LTP or latest time for closing trade\")\n", "                else:\n", "                    logger.warning(f\"No ticks available for option {active_trade.option_token}\")\n", "            else:\n", "                logger.warning(\"No active trade or option ticks found for closing\")\n", "        except Exception as e:\n", "            logger.error(f\"Error in close_option_position: {e}\")\n", "            import traceback\n", "            logger.error(traceback.format_exc())\n", "\n", "    async def monitor_retracement(target_price: float, action: str) -> None:\n", "        try:\n", "            async with monitor_lock:\n", "                # Convert ISO timestamp string to Unix timestamp\n", "                def iso_to_timestamp(iso_time: str) -> float:\n", "                    if isinstance(iso_time, (int, float)):\n", "                        return float(iso_time)\n", "                    try:\n", "                        dt = datetime.strptime(iso_time, '%Y-%m-%dT%H:%M:%S')\n", "                        return dt.timestamp()\n", "                    except ValueError as e:\n", "                        logger.error(f\"Failed to parse timestamp: {iso_time}, Error: {e}\")\n", "                        raise\n", "                \n", "                # Convert start_time to Unix timestamp\n", "                start_time = iso_to_timestamp(latest_time)\n", "                \n", "                logger.info(f\"Starting retracement monitoring - Action: {action}, Target: {target_price:.2f}, \"\n", "                        f\"Start time: {latest_time}\")\n", "                \n", "                while True:\n", "                    current_jma = states.get(\"jma_1\", {}).get(\"latest\")\n", "                    \n", "                    current_supertrend = states.get(\"supertrend_1\", {}).get(\"latest\")\n", "                    \n", "                    # Exit monitoring if signal conditions change\n", "                    if action == \"BUY\" and (current_jma == -1): #or current_supertrend == -1):\n", "                        logger.warning(f\"Exiting BUY monitoring - Indicators changed: JMA: {current_jma}, Supertrend: {current_supertrend}\")\n", "                        monitoring_status[strategy_key] = False\n", "                        return\n", "                    elif action == \"SELL\" and (current_jma == 1): #or current_supertrend == 1):\n", "                        logger.warning(f\"Exiting SELL monitoring - Indicators changed: JMA: {current_jma}, Supertrend: {current_supertrend}\")\n", "                        monitoring_status[strategy_key] = False\n", "                        return\n", "\n", "                    latest_tick = tick_collector.ring_buffers.get(token, [None])[-1]\n", "                    if not latest_tick:\n", "                        logger.warning(\"Exiting monitoring - No tick data available\")\n", "                        monitoring_status[strategy_key] = False\n", "                        return\n", "                    \n", "                    try:\n", "                        current_price = float(latest_tick[\"ltp\"])\n", "                    except (ValueErro<PERSON>, TypeError):\n", "                        logger.error(f\"Invalid current_price: {latest_tick['ltp']}\")\n", "                        return\n", "\n", "                    try:\n", "                        target_price = float(target_price)\n", "                    except (ValueErro<PERSON>, TypeError):\n", "                        logger.error(f\"Invalid target_price: {target_price}\")\n", "                        return\n", "                    \n", "                    if (action == \"BUY\" and current_price <= target_price) or \\\n", "                    (action == \"SELL\" and current_price >= target_price):\n", "                        if monitoring_status.get(strategy_key, False):\n", "                            logger.info(f\"Retracement target met - Price: {current_price:.2f}, Target: {target_price:.2f}\")\n", "                            await execute_option_action(action)\n", "                            monitoring_status[strategy_key] = False\n", "                        return\n", "                    \n", "                    # Convert current timestamp and check timeout\n", "                    current_time = iso_to_timestamp(latest_tick[\"tt\"])\n", "                    time_elapsed = current_time - start_time\n", "                    \n", "                    if time_elapsed > 300:  # 5 minutes timeout\n", "                        logger.warning(f\"Monitoring timeout after {time_elapsed:.1f}s - Final Price: {current_price:.2f}, Target: {target_price:.2f}\")\n", "                        monitoring_status[strategy_key] = False\n", "                        return\n", "                        \n", "                    await asyncio.sleep(0.1)\n", "                    \n", "        except Exception as e:\n", "            logger.error(f\"Error in retracement monitoring: {str(e)}\")\n", "            logger.error(traceback.format_exc())\n", "            monitoring_status[strategy_key] = False\n", "        finally:\n", "            monitoring_status[strategy_key] = False\n", "\n", "    try:\n", "        # First check for active trades and process exits\n", "        active_trade = None\n", "        for trade in trade_manager.active_trades.values():\n", "            if trade.index_token == token and trade.timeframe == timeframe:\n", "                active_trade = trade\n", "                break\n", "\n", "        # Process exit signals with updated conditions\n", "        if active_trade:\n", "            if active_trade.action == \"BUY\":\n", "                # Exit long if JMA changes to -1 OR Supertrend changes to -1\n", "                jma_exit = jma[\"latest\"] == -1 and jma[\"previous\"] != -1\n", "                sup_exit = supertrend[\"latest\"] == -1 and supertrend[\"previous\"] != -1\n", "                \n", "                if jma_exit or sup_exit:\n", "                    await close_option_position(active_trade)\n", "                    monitoring_status[strategy_key] = False\n", "                    \n", "            elif active_trade.action == \"SELL\":\n", "                # Exit short if JMA changes to 1 OR Supertrend changes to 1\n", "                jma_exit = jma[\"latest\"] == 1 and jma[\"previous\"] != 1\n", "                sup_exit = supertrend[\"latest\"] == 1 and supertrend[\"previous\"] != 1\n", "                \n", "                if jma_exit or sup_exit:\n", "                    await close_option_position(active_trade)\n", "                    monitoring_status[strategy_key] = False\n", "\n", "        # Check for new entry signals with updated conditions\n", "        action = None\n", "        \n", "        # Buy signal: JMA changes to 1 from any state AND Supertrend is 1\n", "        if jma[\"latest\"] == 1 and jma[\"previous\"] != 1 : #and supertrend[\"latest\"] == 1:\n", "            action = \"BUY\"\n", "            \n", "        # Sell signal: JMA changes to -1 from any state AND Supertrend is -1\n", "        elif jma[\"latest\"] == -1 and jma[\"previous\"] != -1 : #and supertrend[\"latest\"] == -1:\n", "            action = \"SELL\"\n", "\n", "        # Process entry signals\n", "        if action:\n", "            # Cancel any existing retracement monitoring\n", "            monitoring_status[strategy_key] = False\n", "            \n", "            last_candle = tick_collector.resampler.indicator_manager.get_latest_candle_with_indicators(\n", "                token, timeframe)\n", "                \n", "            if last_candle:\n", "                candle_ratio = last_candle.indicators['candle_height_1']\n", "                \n", "                if candle_ratio > 2 and not monitoring_status.get(strategy_key, False):\n", "                    high_price = last_candle.candle.high\n", "                    low_price = last_candle.candle.low\n", "                    \n", "                    if action == \"BUY\" and buy_allowed:\n", "                        target_price = low_price + (high_price - low_price) * 0.50\n", "                        monitoring_status[strategy_key] = True\n", "                        logger.info(f\"Monitoring retracement for buy at {target_price}\")\n", "                        asyncio.create_task(monitor_retracement(target_price, action))\n", "                    elif action == \"SELL\" and sell_allowed:\n", "                        target_price = high_price - (high_price - low_price) * 0.50\n", "                        monitoring_status[strategy_key] = True\n", "                        logger.info(f\"Monitoring retracement for sell at {target_price}\")\n", "                        asyncio.create_task(monitor_retracement(target_price, action))\n", "                else:\n", "                    if action == \"BUY\" and buy_allowed:\n", "                        await execute_option_action(action)\n", "                    elif action == \"SELL\" and sell_allowed:\n", "                        await execute_option_action(action)\n", "\n", "    except Exception as e:\n", "        print(f\"Error in trade logic for strategy {strategy_id}: {e}\")\n", "        import traceback\n", "        print(traceback.format_exc())\n", "        monitoring_status[strategy_key] = False\n", "\n", "        \n", "async def setup_strategies(tick_collector, trade_manager):\n", "    \"\"\"Set up strategies using the TickCollector and TradeManager instances.\"\"\"\n", "    strategy_manager = StrategyManager(tick_collector, trade_manager)\n", "\n", "    # await strategy_manager.add_strategy(\n", "    #     token=\"438577\",\n", "    #     timeframe=\"5s\",\n", "    #     strategy_id=\"5s_logic\",  \n", "    #     indicators=[\"supertrend_1\", \"jma_1\", \"candle_height_1\"],\n", "    #     strategy_logic=bull_and_bear_trade_logic,\n", "    #     default_sl=20,\n", "    #     default_target=40\n", "    # )   \n", "    \n", "    await strategy_manager.add_strategy(\n", "        token=\"26000\",\n", "        timeframe=\"15s\",\n", "        strategy_id=\"5s_logic\",  \n", "        indicators=[\"supertrend_1\", \"jma_1\", \"candle_height_1\"],\n", "        strategy_logic=bull_and_bear_trade_logic,\n", "        default_sl=5,\n", "        default_target=10\n", "    ) \n", "    \n", "    await strategy_manager.add_strategy(\n", "        token=\"26009\",\n", "        timeframe=\"15s\",\n", "        strategy_id=\"5s_logic\",  \n", "        indicators=[\"supertrend_1\", \"jma_1\", \"candle_height_1\"],\n", "        strategy_logic=bull_and_bear_trade_logic,\n", "        default_sl=20,\n", "        default_target=40\n", "    ) \n", "    \n", "    await strategy_manager.add_strategy(\n", "        token=\"26009\",\n", "        timeframe=\"5s\",\n", "        strategy_id=\"5s_logic\",  \n", "        indicators=[\"supertrend_1\", \"jma_1\", \"candle_height_1\"],\n", "        strategy_logic=bull_and_bear_trade_logic,\n", "        default_sl=15,\n", "        default_target=30\n", "    ) \n", "    return strategy_manager\n", "\n", "async def main():\n", "    \n", "    # Configure the root logger at the start of main()\n", "    logging.basicConfig(\n", "        level=logging.INFO,  # Set the logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)\n", "        format=\"%(asctime)s - %(name)s - %(levelname)s - %(message)s\",  # Log format\n", "        handlers=[\n", "            #logging.StreamHandler(),  # Log to console\n", "            logging.FileHandler(\"app.log\")  # Log to a file\n", "        ]\n", "    )    \n", "    \n", "    collector = None\n", "    try:\n", "        # Initialize AdvancedResampler outside of TickCollector\n", "        resampler = AdvancedResampler(valid_timeframes=TickCollector.VALID_TIMEFRAMES) \n", "        collector = TickCollector(resampler=resampler)\n", "        global global_tick_collector\n", "        global_tick_collector = collector\n", "\n", "        option_manager = OptionManager(collector)\n", "        future_manager = Future<PERSON>anager(collector)\n", "        \n", "        collector.future_manager = future_manager\n", "        collector.option_manager = option_manager\n", "        trade_manager = TradeManager(collector, option_manager)\n", "        collector.trade_manager = trade_manager  \n", "\n", "        # Add delayed strategy manager setup method to TickCollector\n", "        async def delayed_setup():\n", "            await asyncio.sleep(20)\n", "            collector.strategy_manager = await setup_strategies(collector, trade_manager)\n", "            # Start the option update task\n", "            asyncio.create_task(option_manager.update_options()) \n", "            asyncio.create_task(future_manager.update_futures())\n", "            asyncio.create_task(broadcast_strategy_updates())        \n", "\n", "        # Start essential tasks\n", "        tasks = [\n", "            asyncio.create_task(collector.connect_and_subscribe()),\n", "            asyncio.create_task(collector.process_tick_queue()),\n", "            asyncio.create_task(delayed_setup())            \n", "        ]  \n", "        \n", "        tasks.append(asyncio.create_task(start_server()))\n", "        # Wait for all tasks to complete\n", "        await asyncio.gather(*tasks)\n", "        \n", "    except Exception as e:\n", "        logging.error(f\"Error in main: {e}\")\n", "        raise\n", "    finally:\n", "        if collector and collector.api:\n", "            # Implement cleanup logic here\n", "            for subscription in collector.active_subscriptions.copy():\n", "                await collector.manage_subscriptions('remove', subscription)\n", "            collector.feed_opened = False\n", "            # Add server shutdown logic here if possible\n", "            \n", "# Ensure we're not blocking the IPython environment\n", "loop = asyncio.get_event_loop()\n", "loop.set_debug(True)\n", "if loop.is_running():\n", "    nest_asyncio.apply()\n", "# Start the main function as an asyncio task\n", "asyncio.create_task(main())\n", "# If not running, keep the loop alive\n", "if not loop.is_running():\n", "    \n", "    try:\n", "        #loop.run_forever()\n", "        asyncio.create_task(main())\n", "    except KeyboardInterrupt:\n", "        logging.info(\"Received exit signal. Cleaning up...\")\n", "    finally:\n", "        loop.close()\n", "        logging.info(\"Event loop closed. Exiting.\")\n", "        #trade_logger.shutdown()\n", "#################################\n", "#################################\n", "\n", "app = FastAPI()\n", "\n", "# Store connected clients\n", "app.add_middleware(\n", "    CORSMiddleware,\n", "    allow_origins=[\"http://localhost:5173\"],  # Adjust based on your frontend port\n", "    allow_methods=[\"*\"],\n", "    allow_headers=[\"*\"],\n", ")\n", "clients: Set[WebSocket] = set()\n", "global_tick_collector = None\n", "\n", "class TradeActionRequest(BaseModel):\n", "    action: str\n", "    trade_key: Optional[str] = None\n", "    new_sl: Optional[float] = None\n", "    new_target: Optional[float] = None\n", "    trail_sl: Optional[float] = None\n", "    \n", "class CustomJSONEncoder(json.JSONEncoder):\n", "    def default(self, obj):\n", "        if isinstance(obj, datetime):\n", "            return obj.isoformat()\n", "        return super().default(obj)\n", "\n", "last_broadcast_trades = defaultdict(lambda: None)\n", "last_broadcast_strategies = defaultdict(lambda: None)\n", "\n", "def delta_encode(current_state, last_state):\n", "    delta = {}\n", "    for key in current_state:\n", "        if current_state[key] != last_state.get(key):\n", "            delta[key] = current_state[key]\n", "    return delta\n", "\n", "async def broadcast_trade_updates():\n", "    \"\"\"Broadcasts updates to all connected WebSocket clients with delta encoding.\"\"\"\n", "    try:\n", "        if not clients:\n", "            return\n", "\n", "        if global_tick_collector is None or global_tick_collector.trade_manager is None:\n", "            print(\"TradeManager not initialized for broadcast.\")\n", "            return\n", "\n", "        trade_manager = global_tick_collector.trade_manager\n", "        current_state = {\n", "            \"active_trades\": await trade_manager.get_active_positions(),\n", "            \"trade_history\": await trade_manager.get_trade_history_display()\n", "        }\n", "        delta = delta_encode(current_state, last_broadcast_trades)\n", "\n", "        if delta:  # Only broadcast if there's an actual change\n", "            message = {\n", "                \"type\": \"update\",\n", "                **delta\n", "            }\n", "            last_broadcast_trades.update(current_state)  # Update for next comparison\n", "            json_message = json.dumps(message, cls=CustomJSONEncoder)\n", "            coroutines = [client.send_text(json_message) for client in set(clients)]\n", "            results = await asyncio.gather(*coroutines, return_exceptions=True)\n", "            for result in results:\n", "                if isinstance(result, Exception):\n", "                    print(f\"Error broadcasting to client: {result}\")\n", "                    clients.discard(client)\n", "    except Exception as e:\n", "        print(f\"Error during broadcast: {e}\")\n", "        \n", "# Pydantic model for strategy control requests\n", "class StrategyControlRequest(BaseModel):\n", "    token: str\n", "    timeframe: str\n", "    strategy_id: str\n", "\n", "class TradeControlRequest(BaseModel):\n", "    token: str\n", "    timeframe: str\n", "    strategy_id: str\n", "    enable_buy: bool\n", "    enable_sell: bool\n", "\n", "async def broadcast_strategy_updates():\n", "    \"\"\"Broadcasts strategy updates to all connected WebSocket clients with delta encoding.\"\"\"\n", "    try:\n", "        if not clients:\n", "            return\n", "\n", "        strategy_manager = global_tick_collector.strategy_manager\n", "        if not strategy_manager:\n", "            print(\"StrategyManager not initialized for broadcast.\")\n", "            return\n", "\n", "        current_state = []\n", "        for (token, timeframe, strategy_id), strategy in strategy_manager.strategies.items():\n", "            trade_controller = strategy_manager.trade_controller\n", "            trade_control = trade_controller.strategy_actions.get(\n", "                (token, timeframe, strategy_id), {\"enable_buy\": True, \"enable_sell\": True}\n", "            )\n", "            current_state.append({\n", "                \"token\": token,\n", "                \"timeframe\": timeframe,\n", "                \"strategy_id\": strategy_id,\n", "                \"enabled\": strategy[\"enabled\"],\n", "                \"enable_buy\": trade_control[\"enable_buy\"],\n", "                \"enable_sell\": trade_control[\"enable_sell\"]\n", "            })\n", "\n", "        delta = delta_encode({\"strategy_states\": current_state}, last_broadcast_strategies)\n", "\n", "        if delta:  # Only broadcast if there's an actual change\n", "            message = {\n", "                \"type\": \"strategy_update\",\n", "                **delta\n", "            }\n", "            last_broadcast_strategies.update({\"strategy_states\": current_state})  # Update for next comparison\n", "            json_message = json.dumps(message, cls=CustomJSONEncoder)\n", "            coroutines = [client.send_text(json_message) for client in set(clients)]\n", "            results = await asyncio.gather(*coroutines, return_exceptions=True)\n", "            \n", "            \n", "            for client, result in zip(set(clients), results):\n", "                if isinstance(result, Exception):\n", "                    print(f\"Error broadcasting to client: {result}\")\n", "                    clients.discard(client)\n", "        \n", "    except Exception as e:\n", "        print(f\"Error during strategy broadcast: {e}\")\n", "        \n", "# Endpoint to enable a strategy\n", "@app.post(\"/enable_strategy\")\n", "async def enable_strategy(request: StrategyControlRequest):\n", "    try:\n", "        await global_tick_collector.strategy_manager.enable_strategy(\n", "            request.token, request.timeframe, request.strategy_id\n", "        )\n", "        await broadcast_strategy_updates()  # Broadcast the update\n", "        return {\"status\": \"success\", \"message\": f\"Strategy {request.strategy_id} enabled.\"}\n", "    except Exception as e:\n", "        raise HTTPException(status_code=400, detail=str(e))\n", "\n", "# Endpoint to disable a strategy\n", "@app.post(\"/disable_strategy\")\n", "async def disable_strategy(request: StrategyControlRequest):\n", "    try:\n", "        await global_tick_collector.strategy_manager.disable_strategy(\n", "            request.token, request.timeframe, request.strategy_id\n", "        )\n", "        await broadcast_strategy_updates()  # Broadcast the update\n", "        return {\"status\": \"success\", \"message\": f\"Strategy {request.strategy_id} disabled.\"}\n", "    except Exception as e:\n", "        raise HTTPException(status_code=400, detail=str(e))\n", "\n", "# Endpoint to toggle buy/sell for a strategy\n", "@app.post(\"/set_trade_control\")\n", "async def set_trade_control(request: TradeControlRequest):\n", "    try:\n", "        strategy_key = (request.token, request.timeframe, request.strategy_id)\n", "        strategy = global_tick_collector.strategy_manager.strategies.get(strategy_key)\n", "        \n", "        # Check if the strategy exists and is enabled\n", "        \n", "        if not strategy or not strategy[\"enabled\"]:\n", "            raise HTTPException(\n", "                status_code=400,\n", "                detail=f\"Cannot set trade control: Strategy {request.strategy_id} is not enabled.\"\n", "            )\n", "        \n", "        # Set manual control for the strategy\n", "        global_tick_collector.strategy_manager.trade_controller.set_manual_control(\n", "            strategy_key, request.enable_buy, request.enable_sell\n", "        )\n", "        await broadcast_strategy_updates()  # Broadcast the update\n", "        \n", "        return {\n", "            \"status\": \"success\",\n", "            \"message\": f\"Trade control updated for {request.strategy_id}.\"\n", "        }\n", "    except Exception as e:\n", "        raise HTTPException(status_code=400, detail=str(e))\n", "\n", "# Endpoint to get the current state of a strategy\n", "@app.get(\"/get_strategy_state\")\n", "async def get_strategy_state(token: str, timeframe: str, strategy_id: str):\n", "    try:\n", "        strategy_key = (token, timeframe, strategy_id)\n", "        strategy = global_tick_collector.strategy_manager.strategies.get(strategy_key)\n", "        if not strategy:\n", "            raise HTTPException(status_code=404, detail=\"Strategy not found.\")\n", "        \n", "        trade_controller = global_tick_collector.strategy_manager.trade_controller\n", "        trade_control = trade_controller.strategy_actions.get(strategy_key, {\"enable_buy\": True, \"enable_sell\": True})\n", "        \n", "        return {\n", "            \"status\": \"success\",\n", "            \"strategy_id\": strategy_id,\n", "            \"enabled\": strategy[\"enabled\"],\n", "            \"enable_buy\": trade_control[\"enable_buy\"],\n", "            \"enable_sell\": trade_control[\"enable_sell\"]\n", "        }\n", "    except Exception as e:\n", "        raise HTTPException(status_code=400, detail=str(e))\n", "    \n", "@app.get(\"/get_all_strategies\")\n", "async def get_all_strategies():\n", "    if not global_tick_collector or not global_tick_collector.strategy_manager:\n", "        raise HTTPException(status_code=500, detail=\"StrategyManager not initialized\")\n", "    \n", "    strategy_manager = global_tick_collector.strategy_manager\n", "    strategies = []\n", "    \n", "    for (token, timeframe, strategy_id), strategy in strategy_manager.strategies.items():\n", "        trade_controller = strategy_manager.trade_controller\n", "        trade_control = trade_controller.strategy_actions.get(\n", "            (token, timeframe, strategy_id), {\"enable_buy\": True, \"enable_sell\": True}\n", "        )\n", "        strategies.append({\n", "            \"token\": token,\n", "            \"timeframe\": timeframe,\n", "            \"strategy_id\": strategy_id,\n", "            \"enabled\": strategy[\"enabled\"],\n", "            \"enable_buy\": trade_control[\"enable_buy\"],\n", "            \"enable_sell\": trade_control[\"enable_sell\"]\n", "        })\n", "    \n", "    return strategies\n", "        \n", "@app.websocket(\"/ws/trades\")\n", "async def websocket_endpoint(websocket: WebSocket):\n", "    await websocket.accept()\n", "    clients.add(websocket)\n", "    await broadcast_strategy_updates() \n", "    \n", "    try:\n", "        while True:\n", "            data = await websocket.receive_text()\n", "            try:\n", "                #command = TradeActionRequest.parse_raw(data)\n", "                command = TradeActionRequest.model_validate(json.loads(data))  \n", "                if not global_tick_collector or not global_tick_collector.trade_manager:\n", "                    await websocket.send_text(\"TradeManager not initialized\")\n", "                    continue\n", "                \n", "                trade_manager = global_tick_collector.trade_manager\n", "                \n", "                if command.action == \"update_sl\" and command.trade_key and command.new_sl is not None:\n", "                    # Find trade by symbol instead of trade_key\n", "                    trade_key = next((key for key, trade in trade_manager.active_trades.items() \n", "                                    if trade.option_symbol == command.trade_key), None)\n", "                    if trade_key:\n", "                        trade_manager.update_sl(trade_key, float(command.new_sl))\n", "                        \n", "                elif command.action == \"update_target\" and command.trade_key and command.new_target is not None:\n", "                    trade_key = next((key for key, trade in trade_manager.active_trades.items() \n", "                                    if trade.option_symbol == command.trade_key), None)\n", "                    if trade_key:\n", "                        trade_manager.update_target(trade_key, float(command.new_target))\n", "                        \n", "                elif command.action == \"set_trailing_sl\" and command.trade_key and command.trail_sl is not None:\n", "                    trade_key = next((key for key, trade in trade_manager.active_trades.items() \n", "                                    if trade.option_symbol == command.trade_key), None)\n", "                    if trade_key:\n", "                        trade_manager.set_trailing_sl(trade_key, float(command.trail_sl))\n", "                        \n", "                elif command.action == \"close_trade\" and command.trade_key:\n", "                    trade_key = next((key for key, trade in trade_manager.active_trades.items() \n", "                                    if trade.option_symbol == command.trade_key), None)\n", "                    if trade_key:\n", "                        trade = trade_manager.active_trades[trade_key]\n", "                        option_ticks = trade_manager.option_manager.option_ticks.get(\n", "                            int(trade.index_token), {}).get(trade.option_token, deque())\n", "                        current_price = option_ticks[-1]['ltp'] if option_ticks else trade.entry_price\n", "                        \n", "                        # Set exit reason for manual close\n", "                        trade.exit_reason = \"Manual Close\"\n", "                        \n", "                        await trade_manager.close_trade(\n", "                            index_token=trade.index_token,\n", "                            timeframe=trade.timeframe,\n", "                            option_symbol=trade.option_symbol,\n", "                            option_token=trade.option_token,\n", "                            order_id=trade.order_id,\n", "                            price=current_price,\n", "                            timestamp=datetime.now().isoformat()                            \n", "                        )\n", "                \n", "                elif command.action == \"exit_all_trades\":\n", "                    await trade_manager.exit_all_trades()                \n", "                await broadcast_trade_updates()                \n", "            except Exception as e:\n", "                \n", "                print(f\"Error processing command: {e}\")\n", "                await websocket.send_text(f\"Error: {str(e)}\")                \n", "    except WebSocketDisconnect:\n", "        clients.discard(websocket)\n", "    finally:\n", "        clients.discard(websocket)\n", "        \n", "#templates = Jinja2Templates(directory=\"templates\")\n", "@app.get(\"/\")\n", "async def root():\n", "    return {\"message\": \"Trade WebSocket server running\"}\n", "\n", "# Analytics Endpoints\n", "@app.get(\"/analytics/performance\")\n", "async def get_performance_analytics():\n", "    \"\"\"Get performance metrics by index and timeframe\"\"\"\n", "    if not hasattr(global_tick_collector, 'trade_manager'):\n", "        return {\"error\": \"Trade manager not initialized\"}\n", "    \n", "    class SimpleAnalyzer:\n", "        def __init__(self, trade_manager):\n", "            self.trade_manager = trade_manager\n", "            self.index_map = self._load_index_map()\n", "            \n", "        def _load_index_map(self):\n", "            try:\n", "                with open('config.json') as f:\n", "                    return {str(i['token']): i['index_name'] for i in json.load(f)['indices']}\n", "            except:\n", "                return {}\n", "\n", "        def _get_index_from_symbol(self, symbol: str) -> str:\n", "            for index_name in set(self.index_map.values()):\n", "                if symbol.startswith(index_name):\n", "                    return index_name\n", "            return \"Unknown\"\n", "\n", "        async def analyze(self):\n", "            all_trades = self.trade_manager.closed_trades + list(self.trade_manager.active_trades.values())\n", "            metrics = defaultdict(lambda: {\n", "                'total_pnl': 0,\n", "                'timeframes': defaultdict(lambda: {'pnl': 0, 'trades': 0}),\n", "                'expiries': defaultdict(lambda: {'pnl': 0, 'trades': 0})\n", "            })\n", "\n", "            for trade in all_trades:\n", "                index_name = self._get_index_from_symbol(trade.option_symbol)\n", "                expiry = trade.option_symbol.replace(index_name, \"\")[:6]  # Simple expiry extract\n", "                pnl = trade.current_pnl\n", "                \n", "                metrics[index_name]['total_pnl'] += pnl\n", "                metrics[index_name]['timeframes'][trade.timeframe]['pnl'] += pnl\n", "                metrics[index_name]['timeframes'][trade.timeframe]['trades'] += 1\n", "                metrics[index_name]['expiries'][expiry]['pnl'] += pnl\n", "                metrics[index_name]['expiries'][expiry]['trades'] += 1\n", "\n", "            # Convert to serializable format\n", "            return {\n", "                index: {\n", "                    'total_pnl': round(data['total_pnl'], 2),\n", "                    'timeframes': dict(data['timeframes']),\n", "                    'expiries': dict(data['expiries'])\n", "                }\n", "                for index, data in metrics.items()\n", "            }\n", "    \n", "    analyzer = SimpleAnalyzer(global_tick_collector.trade_manager)\n", "    return await analyzer.analyze()\n", "\n", "@app.get(\"/analytics/symbol-breakdown\")\n", "async def get_symbol_breakdown():\n", "    \"\"\"Get trade distribution by symbol\"\"\"\n", "    if not hasattr(global_tick_collector, 'trade_manager'):\n", "        return {\"error\": \"Trade manager not initialized\"}\n", "    \n", "    tm = global_tick_collector.trade_manager\n", "    symbols = defaultdict(int)\n", "    \n", "    for trade in tm.closed_trades + list(tm.active_trades.values()):\n", "        symbols[trade.option_symbol] += 1\n", "    \n", "    return dict(symbols)\n", "# -------------------------------------------------------------------\n", "\n", "async def start_server():\n", "    config = uvicorn.Config(app, host=\"0.0.0.0\", port=8000)\n", "    server = uvicorn.Server(config)\n", "    \n", "    # Wait for setup to complete\n", "    while global_tick_collector is None or global_tick_collector.strategy_manager is None:\n", "        await asyncio.sleep(1)        \n", "    await server.serve()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["token = \"26009\"  # or full token \"MCX|434272\" depending on how you stored it\n", "timeframe = \"5s\"\n", "# Later, to get the latest candle with its indicators:\n", "# To get last N candles with their indicators:\n", "last_10_candles = global_tick_collector.resampler.indicator_manager.get_candles_with_indicators(token, timeframe, n_candles=5\n", "                                                                                                )\n", "for candle_data in last_10_candles:\n", "    print(f\"Time: {candle_data.candle.timestamp}, Close: {candle_data.candle.close}, jma-1: {candle_data.indicators['jma_1']}, jma: {candle_data.indicators['jma_0']}, sup 1: : {candle_data.indicators['supertrend_1']},sup: {candle_data.indicators['supertrend_0']}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get complete trade history\n", "history = await global_tick_collector.trade_manager.get_trade_history_display()\n", "print(history)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["await global_tick_collector.strategy_manager.enable_strategy(\"437800\", \"15s\", \"5s_logic\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["strategy_key = (\"437800\", \"5\", \"5s_logic\")\n", "global_tick_collector.strategy_manager.trade_controller.set_manual_control(strategy_key, enable_buy=False, enable_sell=False)\n", "global_tick_collector.strategy_manager.trade_controller.is_buy_allowed(strategy_key)\n", "global_tick_collector.strategy_manager.trade_controller.strategy_actions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["global_tick_collector.future_manager.future_ticks"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["last_candle = global_tick_collector.resampler.indicator_manager.get_latest_candle_with_indicators('437800', \"15s\")\n", "print(last_candle.indicators['candle_height_1'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["await global_tick_collector.strategy_manager.add_strategy(\n", "        token=\"438894\",\n", "        timeframe=\"15s\",\n", "        strategy_id=\"5s_logic\",  \n", "        indicators=[\"supertrend_1\", \"jma_1\", \"candle_height_1\"],\n", "        strategy_logic=bull_and_bear_trade_logic,\n", "        default_sl=10,\n", "        default_target=30\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["await global_tick_collector.strategy_manager.disable_strategy('26009' , \"15s\", \"5s_logic\" )"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["await global_tick_collector.manage_subscriptions(\"add\",'NFO|35013')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["global_tick_collector.option_manager.option_ticks"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["global_tick_collector.option_manager.option_subscriptions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["global_tick_collector.ring_buffers"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pip install pydantic_ai"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pip install scikit-fuzzy"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.2"}}, "nbformat": 4, "nbformat_minor": 2}