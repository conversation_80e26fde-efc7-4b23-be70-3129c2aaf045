#!/usr/bin/env python3
import os
import subprocess
import sys

# Add the project root directory to Python path
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, script_dir)

# Set the Fyers access token
# Replace this with your actual token from fyers.ipynb
FYERS_ACCESS_TOKEN = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJhcGkuZnllcnMuaW4iLCJpYXQiOjE3NDI0NjA0NjUsImV4cCI6MTc0MjUxNzA0NSwibmJmIjoxNzQyNDYwNDY1LCJhdWQiOlsieDowIiwieDoxIiwieDoyIiwiZDoxIiwiZDoyIiwieDoxIiwieDowIiwieDoxIiwieDowIiwieDoxIiwieDowIiwieDoxIiwieDowIiwieDoxIiwieDowIiwieDoxIiwieDowIiwieDoxIiwieDowIl0sInN1YiI6ImFjY2Vzc190b2tlbiIsImF0X2hhc2giOiJnQUFBQUFCbjI5WXgyRmRNYVlGWExDLVBMYmJUeEJwdm5aZWtmWjI4NzZJa2x4dGtxTWN5eUFiQUIxcG5pR0VEbmFlU3FMRVVkSjQ1d2hJR3J0MnBCdGVfQzhxVWFud29udEhVZ1V5UzdWLVdGVGNGZWRCUWlfND0iLCJkaXNwbGF5X25hbWUiOiJERUVQIEpZT1RJIEJPUkEiLCJvbXMiOiJLMSIsImhzbV9rZXkiOiI1NzZjNjYyZDhkZGVhNmQ0ZWRjOWVmYzgyNGUwYjZhMTE0NjA5ZWMwZDlhMTBlNzA0MTYyMjZlOSIsImlzRGRwaUVuYWJsZWQiOiJOIiwiaXNNdGZFbmFibGVkIjoiTiIsImZ5X2lkIjoiWUQxNTU2OSIsImFwcFR5cGUiOjEwMCwicG9hX2ZsYWciOiJOIn0.DJpKXVMhZqkoRCojLCVlFUyiLO_wbZYjO86jeT8F8kw'

# Set the environment variable
os.environ["FYERS_ACCESS_TOKEN"] = FYERS_ACCESS_TOKEN

# Run the main.py script
try:
    # Path to the main.py file
    main_script = os.path.join(script_dir, "market_data", "main.py")
    
    # Check if the file exists
    if not os.path.exists(main_script):
        print(f"Error: Could not find {main_script}")
        sys.exit(1)
    
    # Run the main script
    print(f"Starting market data service with Fyers access token...")
    # Use the same Python interpreter with the modified sys.path
    subprocess.run([sys.executable, "-c", 
                   f"import sys; sys.path.insert(0, '{script_dir}'); " +
                   f"exec(open('{main_script}').read())"])
    
except KeyboardInterrupt:
    print("Script terminated by user")
except Exception as e:
    print(f"Error running script: {e}")
