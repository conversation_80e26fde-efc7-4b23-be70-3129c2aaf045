altair==5.3.0
amqp==5.2.0
aniso8601==9.0.1
annotated-types==0.7.0
anyio==4.4.0
apispec==6.4.0
apispec-webframeworks==1.0.0
arrow==1.3.0
asarPy==1.0.1
asttokens==2.4.1
asyncio==3.4.3
attrs==23.2.0
Automat==22.10.0
bidict==0.23.1
billiard==4.2.0
binaryornot==0.4.4
blinker==1.8.2
boto3==1.34.34
botocore==1.34.148
celery==5.4.0
certifi==2024.6.2
chardet==5.2.0
charset-normalizer==3.3.2
click==8.1.7
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
comm==0.2.2
constantly==23.10.4
contourpy==1.2.1
cycler==0.12.1
Cython==0.29.37
debugpy==1.8.1
decorator==5.1.1
deepdiff==6.7.1
dhanhq==1.3
dill==0.3.8
dnspython==2.6.1
email_validator==2.2.0
et-xmlfile==1.1.0
executing==2.0.1
fastapi==0.111.0
fastapi-cli==0.0.4
fastjsonschema==2.20.0
Flask==3.0.2
Flask-Cors==4.0.0
Flask-RESTful==0.3.10
Flask-SocketIO==5.3.6
fonttools==4.53.0
gevent==23.9.1
gevent-websocket==0.10.1
gitignore_parser==0.1.11
greenlet==3.0.3
h11==0.14.0
httpcore==1.0.5
httptools==0.6.1
httpx==0.27.0
hyperlink==21.0.0
idna==3.7
incremental==22.10.0
ipykernel==6.29.4
ipython==8.25.0
itsdangerous==2.2.0
java-utilities==0.3.0
jedi==0.18.2
Jinja2==3.1.4
jmespath==1.0.1
joblib==1.4.2
jpy==0.17.0
jsonschema==4.22.0
jsonschema-specifications==2023.12.1
jupyter_client==8.6.2
jupyter_core==5.7.2
kiwisolver==1.4.5
kombu==5.3.7
kthread==0.2.3
llvmlite==0.43.0
lxml==5.2.2
Mako==1.3.5
Markdown==3.5.2
markdown-it-py==3.0.0
MarkupSafe==2.1.5
marshmallow==3.20.2
matplotlib==3.9.0
matplotlib-inline==0.1.7
mdurl==0.1.2
meson==1.3.2
mplfinance==0.12.10b0
nbformat==5.10.4
nest-asyncio==1.6.0
networkx==3.2.1
NorenRestApi @ file:///home/<USER>/Desktop/new/noren_file/NorenRestApi-0.0.30-py2.py3-none-any.whl
numba==0.60.0
numpy==1.26.4
oauthlib==3.2.2
openpyxl==3.1.2
ordered-set==4.1.0
orjson==3.10.6
outcome==1.3.0.post0
packaging==23.2
pandarallel==1.6.5
pandas==2.2.0
pandas-datareader==0.10.0
pandas_ta @ git+https://github.com/twopirllc/pandas-ta.git@23bca1aff2efde5e4fd1215b20416fda616750f7
parso==0.8.4
passlib==1.7.4
pexpect==4.9.0
pillow==10.3.0
platformdirs==4.2.2     
plotly==5.22.0
prompt_toolkit==3.0.47
psutil==5.9.8
ptyprocess==0.7.0
pure-eval==0.2.2
pyarrow==15.0.0
pydantic==2.8.0
pydantic_core==2.20.0
Pygments==2.18.0
pymongo==4.6.1
pyotp==2.9.0
pyparsing==3.1.2
pypng==0.20220715.0
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-engineio==4.9.1
python-multipart==0.0.9
python-slugify==8.0.4
python-socketio==5.11.3
pytz==2023.3.post1
pyzmq==26.0.3
qrcode==7.4.2
redis==5.0.6
referencing==0.35.1
repath==0.9.0
requests==2.28.2
rich==13.7.1
rpds-py==0.18.1
s3transfer==0.10.2
scikit-learn==1.5.0
scipy==1.13.1
shellingham==1.5.4
simple-websocket==1.0.0
six==1.16.0
sniffio==1.3.1
sortedcontainers==2.4.0
SQLAlchemy==2.0.25
stack-data==0.6.3
starlette==0.37.2
ta==0.11.0
talipp==2.3.1
tenacity==8.4.2
text-unidecode==1.3
threadpoolctl==3.5.0
toml==0.10.2
toolz==0.12.1
tornado==6.4.1
traitlets==5.14.3
trio==0.25.1
Twisted==23.10.0
typer==0.12.3
types-python-dateutil==2.9.0.20240316
typing_extensions==4.12.2
tzdata==2024.1
tzlocal==5.2
ujson==5.10.0
urllib3==1.26.18
uvicorn==0.30.1
uvloop==0.19.0
vine==5.1.0
watchdog==4.0.1
watchfiles==0.22.0
wcwidth==0.2.13
websocket-client==1.5.1
websockets==12.0
Werkzeug==3.0.3
wsproto==1.2.0
xarray==2024.6.0
XlsxWriter==3.2.0
zope.event==5.0
zope.interface==6.4.post2
