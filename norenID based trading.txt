## noren id added

@dataclass
class Trade:
    index_token: str
    timeframe: str
    option_token: str
    option_symbol: str
    action: str
    entry_price: float
    entry_time: str
    noren_id: Optional[str] = None  # Will come from API response
    default_sl: Optional[float] = None
    default_target: Optional[float] = None

    @property
    def trade_key(self):
        return f"{self.noren_id}_{self.option_symbol}"

class TradeManager:
    def __init__(self, tick_collector, option_manager):
        self.active_trades = {}  # key: trade_key, value: Trade
        self.tick_collector = tick_collector
        self.option_manager = option_manager
        self.monitoring_tasks = {}
        self.strategy_manager = None

    async def execute_trade(self, index_token: str, timeframe: str, option_token: str, 
                          option_symbol: str, action: str, price: float, 
                          timestamp: str, default_sl: float = None, 
                          default_target: float = None) -> Optional[str]:
        
        if any(trade.index_token == index_token and trade.timeframe == timeframe 
               for trade in self.active_trades.values()):
            print(f"Active trade exists for {index_token} on {timeframe}")
            return None

        # Here you would make the API call to place order
        # noren_id = await api.place_order(...)
        
        # For now, using placeholder
        noren_id = None  # Will come from API response
        
        new_trade = Trade(
            index_token=index_token,
            timeframe=timeframe,
            option_token=option_token,
            option_symbol=option_symbol,
            action=action,
            entry_price=price,
            entry_time=timestamp,
            noren_id=noren_id,
            default_sl=default_sl,
            default_target=default_target
        )
        
        # Only add to active trades if order was successful (noren_id received)
        if noren_id:
            self.active_trades[new_trade.trade_key] = new_trade
            print(f"Trade executed: {action} {option_symbol} ({option_token}) "
                  f"Index: {index_token} TF: {timeframe} "
                  f"Noren ID: {noren_id} Price: {price} Time: {timestamp}")
            
            self.monitoring_tasks[new_trade.trade_key] = asyncio.create_task(
                self._monitor_trade(new_trade.trade_key)
            )
            return noren_id
        return None

    def close_trade(self, index_token: str, timeframe: str, option_symbol: str,
                   option_token: str = None, noren_id: str = None,
                   price: float = None, timestamp: str = None):
        
        trade_key = next(
            (key for key, trade in self.active_trades.items()
             if trade.noren_id == noren_id and trade.option_symbol == option_symbol),
            None
        )
        
        if trade_key and trade_key in self.active_trades:
            trade = self.active_trades[trade_key]
            
            # Here you would make API call to close order using noren_id
            # success = await api.close_order(trade.noren_id)
            
            if trade_key in self.monitoring_tasks:
                self.monitoring_tasks[trade_key].cancel()
                del self.monitoring_tasks[trade_key]
            
            del self.active_trades[trade_key]
            print(f"Position closed: {trade.action} {option_symbol} "
                  f"({option_token}) Index: {index_token} TF: {timeframe} "
                  f"Noren ID: {noren_id} Price: {price} Time: {timestamp}")
        else:
            print(f"No matching trade found: Symbol {option_symbol} "
                  f"Noren ID {noren_id}")