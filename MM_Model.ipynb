{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'async<PERSON>' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 427\u001b[0m\n\u001b[1;32m    424\u001b[0m     \u001b[38;5;28;<PERSON><PERSON><PERSON><PERSON>\u001b[39;00m asyncio\u001b[38;5;241m.\u001b[39msleep(\u001b[38;5;241m3600\u001b[39m)\n\u001b[1;32m    426\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;18m__name__\u001b[39m \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m__main__\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[0;32m--> 427\u001b[0m     \u001b[43masyncio\u001b[49m\u001b[38;5;241m.\u001b[39mrun(main())\n", "\u001b[0;31mNameError\u001b[0m: name 'asyncio' is not defined"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "from collections import deque, defaultdict\n", "import logging\n", "import os\n", "from numba import njit\n", "from scipy import stats\n", "\n", "# Set up logging\n", "logging.basicConfig(level=logging.INFO)\n", "logger = logging.getLogger(__name__)\n", "\n", "@njit\n", "def milstein_variance_step(V_prev, kappa, theta, sigma, dt, rand_noise, V_min, V_max):\n", "    \"\"\"<PERSON><PERSON><PERSON> discretization for Heston variance update.\"\"\"\n", "    V_prev = max(V_prev, V_min)\n", "    drift = kappa * (theta - V_prev) * dt\n", "    vol = sigma * np.sqrt(V_prev)\n", "    diffusion = vol * np.sqrt(dt) * rand_noise\n", "    correction = 0.5 * sigma * vol * dt\n", "    V_new = V_prev + drift + diffusion + correction\n", "    return np.clip(V_new, V_min, V_max)\n", "\n", "@njit\n", "def compute_gamma_njit(S, K, T, r, V_t, rho, beta, V_min, vol_level):\n", "    \"\"\"Compute gamma with SABR-like skew.\"\"\"\n", "    sigma = np.sqrt(max(V_t, V_min))\n", "    T = max(T, 1e-6)\n", "    skew_factor = 1 + rho * ((S / K) ** (1 - beta) - 1) / sigma * vol_level\n", "    d1 = (np.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))\n", "    gamma = (np.exp(-r * T) / (S * sigma * np.sqrt(T))) * (1 / np.sqrt(2 * np.pi)) * np.exp(-d1**2 / 2)\n", "    gamma *= skew_factor\n", "    return max(gamma, 0)\n", "\n", "class RobustRealTimeSVGamma:\n", "    def __init__(self, tick_collector, index_token='26000', window_size=100, short_window=10, \n", "                 risk_free_rate=0.05, seed=42, learning_rate_boost=False):\n", "        \"\"\"SV Gamma module with futures and symbol file expiry.\"\"\"\n", "        self.tick_collector = tick_collector\n", "        self.index_token = index_token\n", "        self.window_size = window_size\n", "        self.short_window_base = short_window\n", "        self.r = risk_free_rate\n", "        self.seed = seed\n", "        self.learning_rate_boost = learning_rate_boost\n", "        \n", "        # Heston parameters\n", "        self.kappa = 2.0\n", "        self.theta = 0.04\n", "        self.sigma = 0.3\n", "        self.rho = -0.7\n", "        self.V_t = 0.04\n", "        self.V_prev = self.V_t\n", "        self.V_min = 1e-6\n", "        self.V_max = 1.0\n", "        self.last_timestamp = None\n", "        \n", "        # Buffers\n", "        self.option_buffer = defaultdict(lambda: deque(maxlen=window_size))\n", "        self.short_buffer = deque(maxlen=short_window)\n", "        self.squared_returns = deque(maxlen=window_size)\n", "        self.ewma_variance = self.theta\n", "        self.ewma_short = 0.0\n", "        self.alpha_long = 0.01\n", "        self.alpha_short = 0.2\n", "        np.random.seed(seed)\n", "\n", "        # Adaptive tuning\n", "        self.trade_log = deque(maxlen=50)\n", "        self.completed_trades = deque(maxlen=50)\n", "        self.thresholds = {'V_t': 0.25, 'gamma': 0.05, 'profit_drop': -20}\n", "        self.grid_ranges = {'V_t': (0.23, 0.28, 0.01), 'gamma': (0.04, 0.07, 0.01), 'profit_drop': (-30, -10, 5)}\n", "\n", "        # Futures token\n", "        self.futures_token = self.tick_collector.future_manager.future_data.get(self.index_token, {}).get('CURRENT_MONTH', {}).get('token', None)\n", "\n", "        # Cache trading symbol file and expiry lookup\n", "        self.symbol_file = self.get_symbol_file()\n", "        self.expiry_cache = self.load_expiry_cache()\n", "\n", "    def get_symbol_file(self):\n", "        \"\"\"Get trading symbol file from config.json in same directory.\"\"\"\n", "        config_path = os.path.join(os.path.dirname(__file__), 'config.json')\n", "        try:\n", "            with open(config_path, 'r') as f:\n", "                config = json.load(f)\n", "            for index in config['indices']:\n", "                if str(index['token']) == self.index_token:\n", "                    return index['trading_symbol_file']\n", "            logger.warning(f\"No symbol file found for index token {self.index_token}, defaulting to 'NFO_symbols.csv'.\")\n", "            return 'NFO_symbols.csv'\n", "        except (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, json.JSONDecodeError):\n", "            logger.warning(f\"Failed to load config.json, using default 'NFO_symbols.csv'.\")\n", "            return 'NFO_symbols.csv'\n", "\n", "    def load_expiry_cache(self):\n", "        \"\"\"Load expiry cache from trading symbol file.\"\"\"\n", "        symbol_path = os.path.join(os.path.dirname(__file__), self.symbol_file)\n", "        expiry_cache = {}\n", "        try:\n", "            df = pd.read_csv(symbol_path)\n", "            for _, row in df.iterrows():\n", "                token = str(row['Token'])\n", "                expiry = row['Expiry']\n", "                expiry_cache[token] = expiry\n", "        except (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, pd.errors.EmptyDataError):\n", "            logger.warning(f\"Failed to load {self.symbol_file}, expiry cache empty.\")\n", "        return expiry_cache\n", "\n", "    def get_expiry(self, token):\n", "        \"\"\"Get expiry for an option token, fallback to futures expiry.\"\"\"\n", "        expiry_str = self.expiry_cache.get(token)\n", "        if expiry_str:\n", "            try:\n", "                expiry_date = pd.to_datetime(expiry_str)\n", "                days_left = (expiry_date - pd.Timestamp.now()).days\n", "                return max(days_left / 252, 1e-6)\n", "            except (ValueErro<PERSON>, TypeError):\n", "                pass\n", "        \n", "        # Fallback to futures expiry\n", "        future_info = self.tick_collector.future_manager.future_data.get(self.index_token, {}).get('CURRENT_MONTH', {})\n", "        expiry_str = future_info.get('expiry', '2025-03-27')\n", "        try:\n", "            expiry_date = pd.to_datetime(expiry_str)\n", "            days_left = (expiry_date - pd.Timestamp.now()).days\n", "            return max(days_left / 252, 1e-6)\n", "        except (ValueErro<PERSON>, TypeError):\n", "            logger.warning(f\"Failed to parse expiry for token {token}, using default 1/252.\")\n", "            return 1/252\n", "\n", "    def black_scholes_price(self, S, K, T, r, sigma, option_type='call'):\n", "        \"\"\"Black-Scholes option price.\"\"\"\n", "        d1 = (np.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))\n", "        d2 = d1 - sigma * np.sqrt(T)\n", "        if option_type == 'call':\n", "            price = S * stats.norm.cdf(d1) - K * np.exp(-r * T) * stats.norm.cdf(d2)\n", "        else:\n", "            price = K * np.exp(-r * T) * stats.norm.cdf(-d2) - S * stats.norm.cdf(-d1)\n", "        return price\n", "\n", "    def implied_vol_newton(self, S, K, T, r, option_price, option_type='call', tol=1e-6, max_iter=100):\n", "        \"\"\"Newton-<PERSON><PERSON>on IV solver.\"\"\"\n", "        sigma = 0.2\n", "        for _ in range(max_iter):\n", "            price = self.black_scholes_price(S, K, T, r, sigma, option_type)\n", "            vega = S * np.sqrt(T) * stats.norm.pdf((np.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T)))\n", "            diff = price - option_price\n", "            if abs(diff) < tol:\n", "                return sigma\n", "            if vega == 0:\n", "                return sigma\n", "            sigma -= diff / vega\n", "            if sigma <= 0:\n", "                sigma = 0.01\n", "        return sigma\n", "\n", "    def update_volatility(self, option_prices, dt):\n", "        \"\"\"Update variance with futures and symbol file expiry.\"\"\"\n", "        atm_strike = min(option_prices.keys(), key=lambda x: abs(x - self.get_nifty_spot()))\n", "        call_price, put_price = option_prices[atm_strike]\n", "        S = atm_strike + call_price - put_price\n", "        \n", "        iv_sum = 0\n", "        count = 0\n", "        for strike, (call_price, put_price) in option_prices.items():\n", "            for option_type, price in [('call', call_price), ('put', put_price)]:\n", "                if price > 0:\n", "                    option_key = next((k for k, v in self.tick_collector.option_manager.option_data[self.index_token].items() \n", "                                      if v['strike'] == strike and option_type[0].upper() in k), None)\n", "                    if option_key:\n", "                        token = self.tick_collector.option_manager.option_data[self.index_token][option_key]['token']\n", "                        T = self.get_expiry(token)\n", "                        iv = self.implied_vol_newton(S, strike, T, self.r, price, option_type)\n", "                        iv_sum += iv\n", "                        count += 1\n", "        if count > 0:\n", "            self.V_t = (iv_sum / count) ** 2\n", "        \n", "        rand_noise = np.random.normal()\n", "        self.V_prev = self.V_t\n", "        self.V_t = milstein_variance_step(self.V_t, self.kappa, self.theta, self.sigma, dt, \n", "                                         rand_noise, self.V_min, self.V_max)\n", "        self.update_parameters(option_prices, S)\n", "\n", "    def update_parameters(self, option_prices, S):\n", "        \"\"\"Update Heston parameters.\"\"\"\n", "        squared_returns = []\n", "        for strike, (call_price, put_price) in option_prices.items():\n", "            if len(self.option_buffer[strike]) > 0:\n", "                last_call, last_put = self.option_buffer[strike][-1]\n", "                call_ret = (call_price - last_call) / last_call if last_call else 0\n", "                put_ret = (put_price - last_put) / last_put if last_put else 0\n", "                squared_returns.append((call_ret**2 + put_ret**2) / 2)\n", "        \n", "        if squared_returns:\n", "            self.short_buffer.append(np.mean(squared_returns))\n", "            self.ewma_short = (1 - self.alpha_short) * self.ewma_short + self.alpha_short * np.mean(squared_returns)\n", "            self.squared_returns.append(self.ewma_short)\n", "            current_variance = np.mean(self.squared_returns)\n", "            self.ewma_variance = (1 - self.alpha_long) * self.ewma_variance + self.alpha_long * current_variance\n", "            pred_error = self.ewma_variance - self.V_t\n", "            alpha = self.alpha_long if not self.learning_rate_boost else self.alpha_long * min(self.V_t / self.theta, 2.0)\n", "            self.theta = np.clip(self.theta + alpha * pred_error, self.V_min, self.V_max)\n", "            self.sigma = np.clip(self.sigma + alpha * (np.std(self.squared_returns) - self.sigma), 0.1, 0.5)\n", "\n", "    def get_nifty_spot(self):\n", "        \"\"\"Get NIFTY spot from futures ticks, fallback to ring_buffers.\"\"\"\n", "        if self.futures_token and self.futures_token in self.tick_collector.future_manager.future_ticks.get(self.index_token, {}):\n", "            tick = self.tick_collector.future_manager.future_ticks[self.index_token][self.futures_token][0]\n", "            if 'ltp' in tick:\n", "                return tick['ltp']\n", "        if self.index_token in self.tick_collector.ring_buffers:\n", "            return self.tick_collector.ring_buffers[self.index_token]['ltp']\n", "        logger.warning(f\"No NIFTY spot available for {self.index_token}, using default.\")\n", "        return 24500\n", "\n", "    def get_current_price(self, strike, option_type):\n", "        \"\"\"Get latest LTP for a strike and option type.\"\"\"\n", "        for key, opt in self.tick_collector.option_manager.option_data[self.index_token].items():\n", "            opt_strike = float(key.split('_')[-1]) if 'ATM' not in key else float(key.split('_')[1])\n", "            if opt_strike == strike and option_type in key:\n", "                token = opt['token']\n", "                if token in self.tick_collector.option_ticks[self.index_token]:\n", "                    tick = self.tick_collector.option_ticks[self.index_token][token][0]\n", "                    if 'ltp' in tick:\n", "                        return tick['ltp']\n", "        return 0\n", "\n", "    def analyze_signals(self, strike_data, timestamp):\n", "        \"\"\"Analyze SV signals.\"\"\"\n", "        if not strike_data:\n", "            return f\"SV Analysis at {timestamp}: No data yet—wait for ticks.\"\n", "        \n", "        V_t = list(strike_data.values())[0][0]\n", "        blast_potential = V_t > self.thresholds['V_t']\n", "        \n", "        calls = {strike: data for strike, data in strike_data.items() \n", "                 if strike in [float(k.split('_')[-1]) if 'ATM' not in k else float(k.split('_')[1]) \n", "                               for k in self.tick_collector.option_manager.option_data[self.index_token] if 'CE' in k]}\n", "        puts = {strike: data for strike, data in strike_data.items() \n", "                if strike in [float(k.split('_')[-1]) if 'ATM' not in k else float(k.split('_')[1]) \n", "                              for k in self.tick_collector.option_manager.option_data[self.index_token] if 'PE' in k]}\n", "\n", "        call_speed = sorted(calls.items(), key=lambda x: x[1][1], reverse=True)\n", "        put_speed = sorted(puts.items(), key=lambda x: x[1][1], reverse=True)\n", "\n", "        call_roi = [(strike, (gamma * 50 + V_t * 10) / self.get_current_price(strike, 'CE') * 100) \n", "                    for strike, (_, gamma) in call_speed if self.get_current_price(strike, 'CE') > 0]\n", "        put_roi = [(strike, (gamma * 50 + V_t * 10) / self.get_current_price(strike, 'PE') * 100) \n", "                   for strike, (_, gamma) in put_speed if self.get_current_price(strike, 'PE') > 0]\n", "        call_roi = sorted(call_roi, key=lambda x: x[1], reverse=True)\n", "        put_roi = sorted(put_roi, key=lambda x: x[1], reverse=True)\n", "\n", "        analysis = [f\"SV Analysis at {timestamp}:\"]\n", "        if blast_potential:\n", "            analysis.append(f\"Market heating up—\\( V_t \\) {V_t:.2f} signals a 50-100 point blast in 5-15 minutes.\")\n", "            if call_speed:\n", "                fastest_call = call_speed[0]\n", "                top_roi_call = call_roi[0] if call_roi else (fastest_call[0], 0)\n", "                analysis.append(f\"Calls: Fastest mover {int(fastest_call[0])} CE (gamma {fastest_call[1][1]:.3f}). \"\n", "                                f\"Highest ROI {int(top_roi_call[0])} CE ({top_roi_call[1]:.0f}%).\")\n", "            if put_speed:\n", "                fastest_put = put_speed[0]\n", "                top_roi_put = put_roi[0] if put_roi else (fastest_put[0], 0)\n", "                analysis.append(f\"Puts: Fastest mover {int(fastest_put[0])} PE (gamma {fastest_put[1][1]:.3f}). \"\n", "                                f\"Highest ROI {int(top_roi_put[0])} PE ({top_roi_put[1]:.0f}%).\")\n", "        else:\n", "            analysis.append(f\"Wait—\\( V_t \\) {V_t:.2f} is below threshold {self.thresholds['V_t']:.2f}.\")\n", "        \n", "        return \" \".join(analysis)\n", "\n", "    def process_tick(self, timestamp):\n", "        \"\"\"Process ticks with futures and symbol file integration.\"\"\"\n", "        if self.last_timestamp is None:\n", "            dt = 1.0\n", "        else:\n", "            dt = (timestamp - self.last_timestamp).total_seconds() if isinstance(timestamp, pd.Timestamp) else (timestamp - self.last_timestamp)\n", "            dt = max(dt, 0.001)\n", "        \n", "        option_prices = {}\n", "        option_data = self.tick_collector.option_manager.option_data[self.index_token]\n", "        target_keys = ['ATM_CE', 'ATM_PE', 'ITM_CE_1', 'ITM_CE_2', 'ITM_PE_1', 'ITM_PE_2', \n", "                       'OTM_CE_1', 'OTM_CE_2', 'OTM_PE_1', 'OTM_PE_2']\n", "        for option_key in target_keys:\n", "            if option_key in option_data:\n", "                token = option_data[option_key]['token']\n", "                if token in self.tick_collector.option_ticks[self.index_token]:\n", "                    latest_tick = self.tick_collector.option_ticks[self.index_token][token][0]\n", "                    if 'ltp' in latest_tick:\n", "                        strike = float(option_key.split('_')[-1]) if 'ATM' not in option_key else float(option_key.split('_')[1])\n", "                        if 'CE' in option_key:\n", "                            option_prices[strike] = (latest_tick['ltp'], option_prices.get(strike, (0, 0))[1])\n", "                        elif 'PE' in option_key:\n", "                            option_prices[strike] = (option_prices.get(strike, (0, 0))[0], latest_tick['ltp'])\n", "                        self.option_buffer[strike].append((latest_tick['ltp'], 0) if 'CE' in option_key else (0, latest_tick['ltp']))\n", "\n", "        if len(option_prices) >= 4:\n", "            self.update_volatility(option_prices, dt)\n", "            S = self.get_nifty_spot()\n", "            vol_level = min(self.V_t / self.theta, 2.0)\n", "            results = {}\n", "            for strike in option_prices:\n", "                option_key = next(k for k, v in option_data.items() if v['strike'] == strike and ('CE' in k or 'PE' in k))\n", "                token = option_data[option_key]['token']\n", "                T = self.get_expiry(token)\n", "                gamma = compute_gamma_njit(S, strike, T, self.r, self.V_t, self.rho, 0.5, self.V_min, vol_level)\n", "                results[strike] = (np.sqrt(self.V_t), gamma)\n", "            self.last_timestamp = timestamp\n", "            \n", "            self.simulate_trade(timestamp, S, results)\n", "            return self.analyze_signals(results, timestamp)\n", "        return f\"SV Analysis at {timestamp}: Insufficient data—waiting for ticks.\"\n", "\n", "    def simulate_trade(self, timestamp, S, strike_data):\n", "        \"\"\"Simulate trades with adaptive exits.\"\"\"\n", "        calls = {strike: data for strike, data in strike_data.items() \n", "                 if strike in [float(k.split('_')[-1]) if 'ATM' not in k else float(k.split('_')[1]) \n", "                               for k in self.tick_collector.option_manager.option_data[self.index_token] if 'CE' in k]}\n", "        puts = {strike: data for strike, data in strike_data.items() \n", "                if strike in [float(k.split('_')[-1]) if 'ATM' not in k else float(k.split('_')[1]) \n", "                              for k in self.tick_collector.option_manager.option_data[self.index_token] if 'PE' in k]}\n", "\n", "        for trade in list(self.trade_log):\n", "            current_ltp = self.get_current_price(trade['strike'], trade['type'])\n", "            if current_ltp > 0:\n", "                profit = (current_ltp - trade['entry_price']) / trade['entry_price'] * 100\n", "                trade['peak_profit'] = max(trade.get('peak_profit', profit), profit)\n", "                profit_drop = (profit - trade['peak_profit']) if 'peak_profit' in trade else 0\n", "                \n", "                if self.V_t < self.thresholds['V_t']:\n", "                    trade['exit_price'] = current_ltp\n", "                    trade['exit_time'] = timestamp\n", "                    trade['exit_reason'] = 'V_t dropped'\n", "                    self.completed_trades.append(trade)\n", "                    self.trade_log.remove(trade)\n", "                elif profit_drop <= self.thresholds['profit_drop']:\n", "                    trade['exit_price'] = current_ltp\n", "                    trade['exit_time'] = timestamp\n", "                    trade['exit_reason'] = 'Profit drop'\n", "                    self.completed_trades.append(trade)\n", "                    self.trade_log.remove(trade)\n", "                elif (timestamp - trade['timestamp']).total_seconds() / 60 >= 15:\n", "                    trade['exit_price'] = current_ltp\n", "                    trade['exit_time'] = timestamp\n", "                    trade['exit_reason'] = 'Timeout'\n", "                    self.completed_trades.append(trade)\n", "                    self.trade_log.remove(trade)\n", "\n", "        if self.V_t > self.thresholds['V_t']:\n", "            if calls:\n", "                fastest_call = max(calls.items(), key=lambda x: x[1][1])\n", "                entry_price = self.get_current_price(fastest_call[0], 'CE')\n", "                if entry_price > 0:\n", "                    self.trade_log.append({\n", "                        'timestamp': timestamp,\n", "                        'strike': fastest_call[0],\n", "                        'type': 'CE',\n", "                        'entry_price': entry_price,\n", "                        'entry_S': S,\n", "                        'V_t': self.V_t,\n", "                        'gamma': fastest_call[1][1],\n", "                        'peak_profit': 0\n", "                    })\n", "            if puts:\n", "                fastest_put = max(puts.items(), key=lambda x: x[1][1])\n", "                entry_price = self.get_current_price(fastest_put[0], 'PE')\n", "                if entry_price > 0:\n", "                    self.trade_log.append({\n", "                        'timestamp': timestamp,\n", "                        'strike': fastest_put[0],\n", "                        'type': 'PE',\n", "                        'entry_price': entry_price,\n", "                        'entry_S': S,\n", "                        'V_t': self.V_t,\n", "                        'gamma': fastest_put[1][1],\n", "                        'peak_profit': 0\n", "                    })\n", "\n", "        if len(self.completed_trades) >= 50:\n", "            self.update_thresholds()\n", "\n", "    def update_thresholds(self):\n", "        \"\"\"Update thresholds based on completed trades.\"\"\"\n", "        wins = 0\n", "        total_profit = 0\n", "        \n", "        for trade in self.completed_trades:\n", "            profit = (trade['exit_price'] - trade['entry_price']) / trade['entry_price'] * 100\n", "            if profit >= 50:\n", "                wins += 1\n", "            total_profit += profit\n", "        \n", "        win_rate = wins / len(self.completed_trades)\n", "        avg_profit = total_profit / len(self.completed_trades)\n", "        \n", "        if win_rate < 0.8:\n", "            for param, (min_val, max_val, step) in self.grid_ranges.items():\n", "                current = self.thresholds[param]\n", "                if param == 'profit_drop':\n", "                    if win_rate < 0.7 and current < max_val - step:\n", "                        self.thresholds[param] += step\n", "                    elif win_rate > 0.85 and current > min_val + step:\n", "                        self.thresholds[param] -= step\n", "                else:\n", "                    if win_rate < 0.7 and current < max_val - step:\n", "                        self.thresholds[param] += step\n", "                    elif win_rate > 0.85 and current > min_val + step:\n", "                        self.thresholds[param] -= step\n", "                logger.info(f\"SV Updated {param}: {self.thresholds[param]:.2f}, Win Rate: {win_rate:.2f}, Avg Profit: {avg_profit:.0f}%\")\n", "\n", "# Example integration\n", "async def main():\n", "    resampler = AdvancedResampler(valid_timeframes=['5s', '15s', '60s', '120s', '300s'])\n", "    collector = TickCollector(resampler)\n", "    option_manager = OptionManager(collector)\n", "    future_manager = Future<PERSON>anager(collector)\n", "    collector.set_option_manager(option_manager)\n", "    collector.set_future_manager(future_manager)\n", "    \n", "    sv_module = RobustRealTimeSVGamma(collector, index_token='26000')\n", "    \n", "    await collector.connect_and_subscribe()\n", "    await asyncio.sleep(3600)\n", "\n", "if __name__ == \"__main__\":\n", "    asyncio.run(main())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import asyncio\n", "import numpy as np\n", "from collections import deque\n", "import logging\n", "from numba import njit\n", "\n", "# Set up logging\n", "logging.basicConfig(level=logging.INFO)\n", "logger = logging.getLogger(__name__)\n", "\n", "@njit\n", "def calculate_ofi(bid_vols, ask_vols, prev_bid_vols, prev_ask_vols):\n", "    \"\"\"Calculate Order Flow Imbalance.\"\"\"\n", "    bid_hit = np.sum(np.maximum(bid_vols - prev_bid_vols, 0))\n", "    ask_hit = np.sum(np.maximum(ask_vols - prev_ask_vols, 0))\n", "    total = max(bid_hit + ask_hit, 1)\n", "    return (bid_hit - ask_hit) / total\n", "\n", "@njit\n", "def calculate_dwi(bid_prices, bid_vols, ask_prices, ask_vols):\n", "    \"\"\"Calculate Depth Weighted Imbalance with available levels.\"\"\"\n", "    bid_weight = np.sum(bid_prices * bid_vols)\n", "    ask_weight = np.sum(ask_prices * ask_vols)\n", "    total_weight = max(bid_weight + ask_weight, 1)\n", "    return (bid_weight / total_weight) - (ask_weight / total_weight)\n", "\n", "class AdvancedMicrostructureAnalyzer:\n", "    def __init__(self, tick_collector, index_token='26000', window_size=100, short_window=10):\n", "        \"\"\"Microstructure analyzer with futures integration.\"\"\"\n", "        self.tick_collector = tick_collector\n", "        self.index_token = index_token\n", "        self.window_size = window_size\n", "        self.short_window = short_window\n", "        \n", "        self.option_depth = defaultdict(lambda: deque(maxlen=window_size))\n", "        self.futures_volume = deque(maxlen=window_size)\n", "        self.index_velocity = deque(maxlen=short_window)\n", "        self.last_timestamp = None\n", "        self.last_volumes = defaultdict(lambda: {'bv': 0, 'av': 0})\n", "        \n", "        self.lock = asyncio.Lock()\n", "        \n", "        self.signals = {\n", "            'OFI': 0.0,\n", "            'DWI': 0.0,\n", "            'FuturesSpike': 1.0,\n", "            'Velocity': 0.0,\n", "            'Acceleration': 0.0,\n", "            'CancellationRate': 0.0\n", "        }\n", "        \n", "        self.trade_log = deque(maxlen=50)\n", "        self.completed_trades = deque(maxlen=50)\n", "        self.thresholds = {\n", "            'OFI': 0.5,\n", "            'DWI': 0.3,\n", "            'FuturesSpike': 2.0,\n", "            'Velocity': 10.0,\n", "            'CancellationRate': 0.6,\n", "            'nifty_drop': -25\n", "        }\n", "        self.grid_ranges = {\n", "            'OFI': (0.4, 0.7, 0.05),\n", "            'DWI': (0.2, 0.5, 0.05),\n", "            'FuturesSpike': (1.8, 2.5, 0.1),\n", "            'Velocity': (8.0, 12.0, 1.0),\n", "            'CancellationRate': (0.5, 0.7, 0.05),\n", "            'nifty_drop': (-35, -15, 5)\n", "        }\n", "\n", "        self.futures_token = self.tick_collector.future_manager.future_data.get(self.index_token, {}).get('CURRENT_MONTH', {}).get('token', None)\n", "\n", "    async def process_tick(self, timestamp, tick_data):\n", "        \"\"\"Process tick with futures.\"\"\"\n", "        async with self.lock:\n", "            dt = 1.0 if self.last_timestamp is None else max(\n", "                (timestamp - self.last_timestamp).total_seconds() if isinstance(timestamp, pd.Timestamp) else (timestamp - self.last_timestamp), \n", "                0.001\n", "            )\n", "            self.last_timestamp = timestamp\n", "            \n", "            token = tick_data.get('tk')\n", "            if token in [opt['token'] for opt in self.tick_collector.option_manager.option_data[self.index_token].values()]:\n", "                await self.update_option_signals(tick_data)\n", "            elif token == self.futures_token:\n", "                await self.update_futures_volume(tick_data)\n", "            elif token == self.index_token:\n", "                await self.update_velocity_acceleration(tick_data, dt)\n", "            \n", "            analysis = await self.analyze_signals(self.signals, timestamp, tick_data)\n", "            await self.simulate_trade(timestamp, tick_data)\n", "            return analysis\n", "\n", "    async def update_option_signals(self, tick_data):\n", "        \"\"\"Update signals with available depth.\"\"\"\n", "        token = tick_data['tk']\n", "        bid_price = float(tick_data.get('bp', 0))\n", "        ask_price = float(tick_data.get('ap', 0))\n", "        bid_vol = float(tick_data.get('bv', 0))\n", "        ask_vol = float(tick_data.get('av', 0))\n", "        \n", "        # Validate and use available depth (up to 5 levels)\n", "        bid_prices = [float(tick_data.get(f'bp{i}', 0)) for i in range(1, 6)]\n", "        bid_vols = [float(tick_data.get(f'bq{i}', 0)) for i in range(1, 6)]\n", "        ask_prices = [float(tick_data.get(f'sp{i}', 0)) for i in range(1, 6)]\n", "        ask_vols = [float(tick_data.get(f'sq{i}', 0)) for i in range(1, 6)]\n", "        \n", "        valid_bids = [(p, v) for p, v in zip(bid_prices, bid_vols) if p > 0 and v > 0]\n", "        valid_asks = [(p, v) for p, v in zip(ask_prices, ask_vols) if p > 0 and v > 0]\n", "        bid_prices = np.array([x[0] for x in valid_bids]) if valid_bids else np.array([bid_price])\n", "        bid_vols = np.array([x[1] for x in valid_bids]) if valid_bids else np.array([bid_vol])\n", "        ask_prices = np.array([x[0] for x in valid_asks]) if valid_asks else np.array([ask_price])\n", "        ask_vols = np.array([x[1] for x in valid_asks]) if valid_asks else np.array([ask_vol])\n", "        \n", "        self.option_depth[token].append((bid_price, ask_price, bid_vol, ask_vol))\n", "        \n", "        all_bid_vols = []\n", "        all_ask_vols = []\n", "        all_prev_bid_vols = []\n", "        all_prev_ask_vols = []\n", "        for opt in self.tick_collector.option_manager.option_data[self.index_token].values():\n", "            opt_token = opt['token']\n", "            if opt_token in self.option_depth and self.option_depth[opt_token]:\n", "                latest = self.option_depth[opt_token][-1]\n", "                all_bid_vols.append(latest[2])\n", "                all_ask_vols.append(latest[3])\n", "                all_prev_bid_vols.append(self.last_volumes[opt_token]['bv'])\n", "                all_prev_ask_vols.append(self.last_volumes[opt_token]['av'])\n", "                self.last_volumes[opt_token] = {'bv': latest[2], 'av': latest[3]}\n", "        \n", "        if all_bid_vols:\n", "            self.signals['OFI'] = calculate_ofi(np.array(all_bid_vols), np.array(all_ask_vols),\n", "                                               np.array(all_prev_bid_vols), np.array(all_prev_ask_vols))\n", "        if len(bid_prices) > 0 and len(ask_prices) > 0:\n", "            self.signals['DWI'] = calculate_dwi(bid_prices, bid_vols, ask_prices, ask_vols)\n", "        \n", "        if len(self.option_depth[token]) > 1:\n", "            prev = self.option_depth[token][-2]\n", "            canceled_bid = max(prev[2] - bid_vol, 0)\n", "            canceled_ask = max(prev[3] - ask_vol, 0)\n", "            total_vol = max(canceled_bid + canceled_ask + bid_vol + ask_vol, 1)\n", "            self.signals['CancellationRate'] = (canceled_bid + canceled_ask) / total_vol\n", "\n", "    async def update_futures_volume(self, tick_data):\n", "        \"\"\"Update Futures Volume Spikes with futures ticks.\"\"\"\n", "        if self.futures_token and self.futures_token in self.tick_collector.future_manager.future_ticks.get(self.index_token, {}):\n", "            latest_tick = self.tick_collector.future_manager.future_ticks[self.index_token][self.futures_token][0]\n", "            volume = float(latest_tick.get('v', 0))\n", "            self.futures_volume.append(volume)\n", "            if len(self.futures_volume) > 10:\n", "                avg_volume = np.mean(list(self.futures_volume)[-10:])\n", "                self.signals['FuturesSpike'] = volume / max(avg_volume, 1)\n", "        else:\n", "            volume = float(tick_data.get('volume', 0))\n", "            self.futures_volume.append(volume)\n", "            if len(self.futures_volume) > 10:\n", "                avg_volume = np.mean(list(self.futures_volume)[-10:])\n", "                self.signals['FuturesSpike'] = volume / max(avg_volume, 1)\n", "\n", "    async def update_velocity_acceleration(self, tick_data, dt):\n", "        \"\"\"Update Velocity & Acceleration with futures.\"\"\"\n", "        ltp = float(self.get_nifty_spot() if self.futures_token else tick_data.get('lp', 24500))\n", "        if self.index_velocity:\n", "            last_ltp, last_dt = self.index_velocity[-1]\n", "            velocity = (ltp - last_ltp) / dt\n", "            if len(self.index_velocity) > 1:\n", "                last_velocity = self.index_velocity[-2][1]\n", "                self.signals['Acceleration'] = (velocity - last_velocity) / dt\n", "            self.signals['Velocity'] = velocity\n", "        self.index_velocity.append((ltp, velocity if 'velocity' in locals() else 0.0))\n", "\n", "    def get_nifty_spot(self):\n", "        \"\"\"Get NIFTY spot from futures ticks, fallback to ring_buffers.\"\"\"\n", "        if self.futures_token and self.futures_token in self.tick_collector.future_manager.future_ticks.get(self.index_token, {}):\n", "            tick = self.tick_collector.future_manager.future_ticks[self.index_token][self.futures_token][0]\n", "            if 'ltp' in tick:\n", "                return tick['ltp']\n", "        if self.index_token in self.tick_collector.ring_buffers:\n", "            return self.tick_collector.ring_buffers[self.index_token]['ltp']\n", "        logger.warning(f\"No NIFTY spot available for {self.index_token}, using default.\")\n", "        return 24500\n", "\n", "    def get_current_price(self, strike, option_type):\n", "        \"\"\"Get LTP—adaptable.\"\"\"\n", "        for key, opt in self.tick_collector.option_manager.option_data[self.index_token].items():\n", "            opt_strike = float(key.split('_')[-1]) if 'ATM' not in key else float(key.split('_')[1])\n", "            if opt_strike == strike and option_type in key:\n", "                token = opt['token']\n", "                if token in self.tick_collector.option_ticks[self.index_token]:\n", "                    tick = self.tick_collector.option_ticks[self.index_token][token][0]\n", "                    if 'ltp' in tick:\n", "                        return tick['ltp']\n", "        return 0\n", "\n", "    async def analyze_signals(self, signals, timestamp, tick_data):\n", "        \"\"\"Analyze signals.\"\"\"\n", "        direction = \"up\" if signals['OFI'] > self.thresholds['OFI'] and signals['Velocity'] > 0 else \"down\" if signals['OFI'] < -self.thresholds['OFI'] and signals['Velocity'] < 0 else \"unclear\"\n", "        strength = signals['FuturesSpike'] > self.thresholds['FuturesSpike'] and signals['Acceleration'] > self.thresholds['Velocity'] / 2\n", "        trap = signals['CancellationRate'] > self.thresholds['CancellationRate']\n", "        \n", "        token = tick_data.get('tk', '')\n", "        strike_flow = \"\"\n", "        if token in [opt['token'] for opt in self.tick_collector.option_manager.option_data[self.index_token].values()]:\n", "            for key, opt in self.tick_collector.option_manager.option_data[self.index_token].items():\n", "                if opt['token'] == token:\n", "                    strike_type = \"ATM\" if 'ATM' in key else \"ITM\" if 'ITM' in key else \"OTM\"\n", "                    option_type = \"calls\" if 'CE' in key else \"puts\"\n", "                    strike_flow = f\"{strike_type} {option_type} active\"\n", "                    break\n", "\n", "        analysis = [f\"Micro Analysis at {timestamp}:\"]\n", "        if direction == \"up\" and not trap:\n", "            analysis.append(f\"NIFTY blasting up—buy calls now.\")\n", "            analysis.append(f\"Strong buy flow (OFI {signals['OFI']:.2f}) across ATM/OTM strikes, \"\n", "                           f\"deep bid support (DWI {signals['DWI']:.2f}).\")\n", "            if strength:\n", "                analysis.append(f\"Futures spiking (FuturesSpike {signals['FuturesSpike']:.1f}), \"\n", "                               f\"NIFTY accelerating (Velocity {signals['Velocity']:.1f}, \"\n", "                               f\"Acceleration {signals['Acceleration']:.1f})—\"\n", "                               f\"50-100 point blast in 5-15 min.\")\n", "            analysis.append(f\"Real orders (Cancellation {signals['CancellationRate']:.2f})—{strike_flow}.\")\n", "        elif direction == \"down\" and not trap:\n", "            analysis.append(f\"NIFTY dropping—buy puts now.\")\n", "            analysis.append(f\"Strong sell flow (OFI {signals['OFI']:.2f}) across ATM/OTM strikes, \"\n", "                           f\"ask-heavy depth (DWI {signals['DWI']:.2f}).\")\n", "            if strength:\n", "                analysis.append(f\"Futures selling (FuturesSpike {signals['FuturesSpike']:.1f}), \"\n", "                               f\"NIFTY decelerating (Velocity {signals['Velocity']:.1f}, \"\n", "                               f\"Acceleration {signals['Acceleration']:.1f})—\"\n", "                               f\"50-100 point drop in 5-15 min.\")\n", "            analysis.append(f\"Real orders (Cancellation {signals['CancellationRate']:.2f})—{strike_flow}.\")\n", "        elif trap:\n", "            analysis.append(f\"Skip—NIFTY unclear, high cancellations (Cancellation {signals['CancellationRate']:.2f}) \"\n", "                           f\"suggest spoofing despite flow (OFI {signals['OFI']:.2f}).\")\n", "            if strike_flow:\n", "                analysis.append(f\"Activity on {strike_flow}—not reliable.\")\n", "        else:\n", "            analysis.append(f\"Wait—NIFTY unclear, mixed signals (OFI {signals['OFI']:.2f}, \"\n", "                           f\"DWI {signals['DWI']:.2f}, Velocity {signals['Velocity']:.1f}).\")\n", "            if strike_flow:\n", "                analysis.append(f\"Some {strike_flow}—needs stronger confirmation.\")\n", "        \n", "        return \" \".join(analysis)\n", "\n", "    async def simulate_trade(self, timestamp, tick_data):\n", "        \"\"\"Simulate trades with futures.\"\"\"\n", "        S = self.get_nifty_spot()\n", "\n", "        for trade in list(self.trade_log):\n", "            current_ltp = self.get_current_price(trade['strike'], trade['type'])\n", "            if current_ltp > 0:\n", "                profit = (current_ltp - trade['entry_price']) / trade['entry_price'] * 100\n", "                trade['peak_S'] = max(trade.get('peak_S', trade['entry_S']), S if trade['type'] == 'CE' else trade['entry_S'])\n", "                trade['peak_S'] = min(trade.get('peak_S', trade['entry_S']), S if trade['type'] == 'PE' else trade['entry_S'])\n", "                S_drop = S - trade['peak_S'] if trade['type'] == 'CE' else trade['peak_S'] - S\n", "                \n", "                direction_valid = (self.signals['OFI'] > self.thresholds['OFI'] and self.signals['Velocity'] > 0) if trade['type'] == 'CE' else \\\n", "                                 (self.signals['OFI'] < -self.thresholds['OFI'] and self.signals['Velocity'] < 0)\n", "                if not direction_valid or self.signals['CancellationRate'] > self.thresholds['CancellationRate']:\n", "                    trade['exit_price'] = current_ltp\n", "                    trade['exit_time'] = timestamp\n", "                    trade['exit_reason'] = 'Direction failed' if not direction_valid else 'Trap detected'\n", "                    self.completed_trades.append(trade)\n", "                    self.trade_log.remove(trade)\n", "                elif <PERSON>_drop <= self.thresholds['nifty_drop']:\n", "                    trade['exit_price'] = current_ltp\n", "                    trade['exit_time'] = timestamp\n", "                    trade['exit_reason'] = 'NIFTY reversal'\n", "                    self.completed_trades.append(trade)\n", "                    self.trade_log.remove(trade)\n", "                elif (timestamp - trade['timestamp']).total_seconds() / 60 >= 15:\n", "                    trade['exit_price'] = current_ltp\n", "                    trade['exit_time'] = timestamp\n", "                    trade['exit_reason'] = 'Timeout'\n", "                    self.completed_trades.append(trade)\n", "                    self.trade_log.remove(trade)\n", "\n", "        if self.signals['OFI'] > self.thresholds['OFI'] and self.signals['Velocity'] > self.thresholds['Velocity'] and self.signals['CancellationRate'] < self.thresholds['CancellationRate']:\n", "            option_type = 'CE'\n", "            strike = next(iter(self.tick_collector.option_manager.option_data[self.index_token].values()))['strike']\n", "            entry_price = self.get_current_price(strike, option_type)\n", "            if entry_price > 0:\n", "                self.trade_log.append({\n", "                    'timestamp': timestamp,\n", "                    'strike': strike,\n", "                    'type': option_type,\n", "                    'entry_price': entry_price,\n", "                    'entry_S': S,\n", "                    'OFI': self.signals['OFI'],\n", "                    'DWI': self.signals['DWI'],\n", "                    'FuturesSpike': self.signals['FuturesSpike'],\n", "                    'Velocity': self.signals['Velocity'],\n", "                    'CancellationRate': self.signals['CancellationRate'],\n", "                    'peak_S': S\n", "                })\n", "        elif self.signals['OFI'] < -self.thresholds['OFI'] and self.signals['Velocity'] < -self.thresholds['Velocity'] and self.signals['CancellationRate'] < self.thresholds['CancellationRate']:\n", "            option_type = 'PE'\n", "            strike = next(iter(self.tick_collector.option_manager.option_data[self.index_token].values()))['strike']\n", "            entry_price = self.get_current_price(strike, option_type)\n", "            if entry_price > 0:\n", "                self.trade_log.append({\n", "                    'timestamp': timestamp,\n", "                    'strike': strike,\n", "                    'type': option_type,\n", "                    'entry_price': entry_price,\n", "                    'entry_S': S,\n", "                    'OFI': self.signals['OFI'],\n", "                    'DWI': self.signals['DWI'],\n", "                    'FuturesSpike': self.signals['FuturesSpike'],\n", "                    'Velocity': self.signals['Velocity'],\n", "                    'CancellationRate': self.signals['CancellationRate'],\n", "                    'peak_S': S\n", "                })\n", "\n", "        if len(self.completed_trades) >= 50:\n", "            self.update_thresholds()\n", "\n", "    def update_thresholds(self):\n", "        \"\"\"Update thresholds.\"\"\"\n", "        wins = 0\n", "        total_profit = 0\n", "        \n", "        for trade in self.completed_trades:\n", "            profit = (trade['exit_price'] - trade['entry_price']) / trade['entry_price'] * 100\n", "            if profit >= 50:\n", "                wins += 1\n", "            total_profit += profit\n", "        \n", "        win_rate = wins / len(self.completed_trades)\n", "        avg_profit = total_profit / len(self.completed_trades)\n", "        \n", "        if win_rate < 0.8:\n", "            for param, (min_val, max_val, step) in self.grid_ranges.items():\n", "                current = self.thresholds[param]\n", "                if param == 'CancellationRate':\n", "                    if win_rate < 0.7 and current > min_val + step:\n", "                        self.thresholds[param] -= step\n", "                    elif win_rate > 0.85 and current < max_val - step:\n", "                        self.thresholds[param] += step\n", "                elif param == 'nifty_drop':\n", "                    if win_rate < 0.7 and current < max_val - step:\n", "                        self.thresholds[param] += step\n", "                    elif win_rate > 0.85 and current > min_val + step:\n", "                        self.thresholds[param] -= step\n", "                else:\n", "                    if win_rate < 0.7 and current < max_val - step:\n", "                        self.thresholds[param] += step\n", "                    elif win_rate > 0.85 and current > min_val + step:\n", "                        self.thresholds[param] -= step\n", "                logger.info(f\"Micro Updated {param}: {self.thresholds[param]:.2f}, Win Rate: {win_rate:.2f}, Avg Profit: {avg_profit:.0f}%\")\n", "\n", "# Integration\n", "async def integrate_with_collector(collector):\n", "    micro_analyzer = AdvancedMicrostructureAnalyzer(collector)\n", "    \n", "    async def enhanced_feed_update(tick_data):\n", "        token = tick_data.get('tk')\n", "        timestamp = pd.to_datetime(tick_data.get('ft', 0), unit='s')\n", "        analysis = await micro_analyzer.process_tick(timestamp, tick_data)\n", "        logger.info(analysis)\n", "        collector.event_handler_feed_update(tick_data)\n", "    \n", "    collector.api.subscribe_callback = enhanced_feed_update\n", "    await collector.connect_and_subscribe()\n", "\n", "async def main():\n", "    resampler = AdvancedResampler(valid_timeframes=['5s', '15s', '60s', '120s', '300s'])\n", "    collector = TickCollector(resampler)\n", "    option_manager = OptionManager(collector)\n", "    future_manager = Future<PERSON>anager(collector)\n", "    collector.set_option_manager(option_manager)\n", "    collector.set_future_manager(future_manager)\n", "    await integrate_with_collector(collector)\n", "    await asyncio.sleep(3600)\n", "\n", "if __name__ == \"__main__\":\n", "    asyncio.run(main())"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.2"}}, "nbformat": 4, "nbformat_minor": 2}