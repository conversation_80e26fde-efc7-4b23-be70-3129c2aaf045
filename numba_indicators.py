import numpy as np
from numba import njit

@njit
def ema_numba(close, length):
    out = np.full_like(close, np.nan)
    
    if len(close) <= length:
        return out
    
    alpha = 2 / (length + 1)
    ema = np.mean(close[:length])  # Initialize with SMA
    out[length-1] = ema
    
    for i in range(length, len(close)):
        ema = alpha * close[i] + (1 - alpha) * ema
        out[i] = ema
    
    return np.round(out, 2)
#  code wrapper to meet indicator manager in main code

@njit
def jma_numba_direction_wrapper(high, low, close, length, phase, power):
    # We'll use close prices as the source
    hlc3 = (high + low + close) / 3
    return jma_numba_direction(hlc3, length, phase, power)


@njit
def jma_numba_direction(src, length, phase, power):
    out = np.full_like(src, np.nan)
    direction = np.zeros_like(src)  # Array to hold direction values
    
    if len(src) <= length:
        return out, direction
    
    phaseRatio = 1.5 + phase / 100 if -100 <= phase <= 100 else (0.5 if phase < -100 else 2.5)
    beta = 0.45 * (length - 1) / (0.45 * (length - 1) + 2)
    alpha = np.power(beta, power)
    
    e0 = e1 = e2 = jma = 0.0
    
    for i in range(len(src)):
        if i == 0:
            e0 = src[i]
            e1 = 0.0
            e2 = 0.0
            jma = 0.0
        else:
            e0 = (1 - alpha) * src[i] + alpha * e0
            e1 = (src[i] - e0) * (1 - beta) + beta * e1
            e2 = (e0 + phaseRatio * e1 - jma) * np.power(1 - alpha, 2) + np.power(alpha, 2) * e2
            jma = e2 + jma
            
            # Reset values if they become too large or unstable
            if np.abs(jma) > 1e10:
                e0 = e1 = e2 = jma = 0.0
        
        if i >= length - 1:
            out[i] = np.round(jma, 2)
            
            # Determine direction: 1 for uptrend, -1 for downtrend, 0 if no change
            if i > length:
                if out[i] > out[i-1]:
                    direction[i] = 1
                elif out[i] < out[i-1]:
                    direction[i] = -1
                else:
                    direction[i] = direction[i-1]
    
    return out, direction


@njit
def supertrend_numba(high, low, close, length= 3, multiplier =2):
    atr = atr_numba(high, low, close, length)

    hl2 = (high + low) / 2  # Calculate hl2

    up = np.full_like(close, np.nan)
    dn = np.full_like(close, np.nan)
    direction = np.zeros_like(close)
    supertrend = np.full_like(close, np.nan)

    for i in range(length, len(close)):
        basic_up = hl2[i] - multiplier * atr[i]
        basic_dn = hl2[i] + multiplier * atr[i]

        up[i] = basic_up if i == length else (
            max(basic_up, up[i-1]) if close[i-1] > up[i-1] else basic_up
        )
        dn[i] = basic_dn if i == length else (
            min(basic_dn, dn[i-1]) if close[i-1] < dn[i-1] else basic_dn
        )

        # Update trend
        if i > length:
            if close[i] > dn[i-1]:
                direction[i] = 1
            elif close[i] < up[i-1]:
                direction[i] = -1
            else:
                direction[i] = direction[i-1]  # Continue with the previous trend

        supertrend[i] = up[i] if direction[i] == 1 else dn[i]

    supertrend = np.round(supertrend, 2)

    return supertrend, direction



@njit
def atr_numba(high, low, close, length):
    true_range = np.zeros_like(close)
    atr = np.full_like(close, np.nan)
    
    for i in range(1, len(close)):
        true_range[i] = max(high[i] - low[i], abs(high[i] - close[i-1]), abs(low[i] - close[i-1]))
    
    for i in range(length, len(close)):
        if i == length:
            atr[i] = np.mean(true_range[:length])
        else:
            atr[i] = (atr[i - 1] * (length - 1) + true_range[i]) / length
    
    return atr

@njit
def check_significant_candle(high, low, length=5, threshold=2):
    if len(high) < length or len(low) < length:
        return False, 0.0, 0.0  # Not enough data

    last_n_high = high[-length:]
    last_n_low = low[-length:]

    # Calculate the height of each candle (high - low)
    candle_heights = last_n_high - last_n_low

    average_height = np.mean(candle_heights[:-1])  # Exclude the current candle
    current_candle_height = float(high[-1]) - float(low[-1])

    # Round to 2 decimal places
    average_height = round(average_height, 2)
    current_candle_height = round(current_candle_height, 2)
    
    # Check if the current candle is significantly larger
    is_significant = current_candle_height > (threshold * average_height)

    return is_significant, average_height, current_candle_height

@njit
def custom_round(x, decimals=2):
    """
    Custom rounding function for Numba (nopython mode).
    """
    multiplier = 10 ** decimals
    return np.floor(x * multiplier + 0.5) / multiplier



@njit
def candle_height_numba(high, low, close, lookback=5):
    heights = high - low
    n = len(heights)
    
    # Initialize arrays with nan
    avg_heights = np.full(n, np.nan)
    ratios = np.full(n, np.nan)
    
    # Calculate moving average of heights for each candle, start from lookback period
    for i in range(n):
        if i < lookback:
            # For the first lookback candles, use whatever data is available
            avg_height = np.nanmean(heights[:i+1]) if i > 0 else np.nan
        else:
            # Use the last lookback candles for calculation
            avg_height = np.mean(heights[i-lookback:i])
        
        if not np.isnan(avg_height):
            avg_heights[i] = round(avg_height, 2)
            if avg_height != 0:
                ratios[i] = round(heights[i] / avg_height, 2)
    
    # Replace NaN values with 0 in both arrays
    avg_heights = np.nan_to_num(avg_heights, nan=0.0)
    ratios = np.nan_to_num(ratios, nan=0.0)
    
    return avg_heights, ratios



import numpy as np
from numba import njit

@njit
def atr_numba1(high, low, close, period):
    """Calculate ATR using Numba"""
    tr = np.zeros_like(close)
    atr = np.full_like(close, np.nan)
    
    # Calculate True Range
    for i in range(len(close)):
        if i == 0:
            tr[i] = high[i] - low[i]
        else:
            tr[i] = max(high[i] - low[i], abs(high[i] - close[i-1]), abs(low[i] - close[i-1]))
    
    # Calculate ATR
    for i in range(len(close)):
        if i < period:
            if i == period - 1:
                atr[i] = np.mean(tr[:period])
        else:
            atr[i] = (atr[i-1] * (period - 1) + tr[i]) / period
            
    return atr

@njit
def sma_numba(data, period):
    """Calculate SMA using Numba"""
    sma = np.full_like(data, np.nan)
    for i in range(len(data)):
        if i >= period - 1:
            sma[i] = np.mean(data[i-period+1:i+1])
    return sma

@njit
def rsi_numba(data, period):
    """Calculate RSI using Numba"""
    rsi = np.full_like(data, np.nan)
    gain = np.zeros_like(data)
    loss = np.zeros_like(data)
    
    # Calculate gains and losses
    for i in range(1, len(data)):
        change = data[i] - data[i-1]
        if change > 0:
            gain[i] = change
            loss[i] = 0
        else:
            gain[i] = 0
            loss[i] = -change
    
    # Calculate RSI
    avg_gain = np.zeros_like(data)
    avg_loss = np.zeros_like(data)
    
    for i in range(period, len(data)):
        if i == period:
            avg_gain[i] = np.mean(gain[1:period+1])
            avg_loss[i] = np.mean(loss[1:period+1])
        else:
            avg_gain[i] = (avg_gain[i-1] * (period - 1) + gain[i]) / period
            avg_loss[i] = (avg_loss[i-1] * (period - 1) + loss[i]) / period
        
        if avg_loss[i] == 0:
            rsi[i] = 100
        else:
            rs = avg_gain[i] / avg_loss[i]
            rsi[i] = 100 - (100 / (1 + rs))
    
    return rsi

@njit
def improved_jma_numba_wrapper(high, low, close, base_length, base_phase, power):
    """
    Wrapper function for improved_jma_numba that calculates HLC3 as source
    
    Parameters:
    - high: High prices
    - low: Low prices
    - close: Close prices
    - base_length: Base length for JMA
    - base_phase: Base phase for JMA
    - power: Power parameter for JMA
    
    Returns:
    - jma_values: JMA values
    - trend_direction: Trend direction
    - signal_strength: Signal strength
    """
    # Calculate HLC3 as source (or use close only if preferred)
    src = (high + low + close) / 3  # HLC3
    # src = close  # Use this line instead if you want to use close only
    
    return improved_jma_numba(src, high, low, close, base_length, base_phase, power)

@njit
def improved_jma_numba(src, high, low, close, base_length, base_phase, power):
    """
    Improved Adaptive Jurik Moving Average with dynamic parameters
    
    Parameters:
    - src: Source data (usually close prices or HLC3)
    - high: High prices for ATR calculation
    - low: Low prices for ATR calculation 
    - close: Close prices for additional calculations
    - base_length: Base length for JMA
    - base_phase: Base phase for JMA
    - power: Power parameter for JMA
    
    Returns:
    - jma_values: JMA values
    - trend_direction: Trend direction (1: up, -1: down, 0: neutral)
    - signal_strength: Signal strength indicator
    """
    # Hardcoded parameters as requested
    volatility_period = 10
    sensitivity = 1.0  # 100/100 from original code
    signal_threshold = 1
    
    # Initialize output arrays
    jma_values = np.full_like(src, np.nan)
    trend_direction = np.zeros_like(src, dtype=np.int32)
    signal_strength = np.full_like(src, np.nan)
    
    # Calculate ATR for volatility measurement
    current_atr = atr_numba1(high, low, close, volatility_period)
    avg_atr = sma_numba(current_atr, volatility_period * 2)
    
    # Initialize volatility ratio
    volatility_ratio = np.full_like(src, 1.0)
    raw_volatility_ratio = np.full_like(src, 1.0)
    
    # Calculate volatility ratio
    for i in range(volatility_period * 2, len(src)):
        if avg_atr[i] > 0:
            raw_volatility_ratio[i] = current_atr[i] / avg_atr[i]
    
    # Smooth volatility ratio
    volatility_ratio = sma_numba(raw_volatility_ratio, 3)
    
    # Initialize arrays for tracking JMA
    e0_array = np.zeros_like(src)
    e1_array = np.zeros_like(src)
    e2_array = np.zeros_like(src)
    jma_array = np.zeros_like(src)
    
    # Calculate RSI for trend detection
    rsi_values = rsi_numba(close, 10)
    
    # Initialize variables
    e0 = e1 = e2 = jma = 0.0
    
    for i in range(len(src)):
        # Skip calculation for insufficient data
        if i < max(base_length, volatility_period * 2, 10):
            continue
        
        # Trend detection
        is_uptrend = sma_numba(close, 5)[i] > sma_numba(close, 10)[i]
        is_downtrend = sma_numba(close, 5)[i] < sma_numba(close, 10)[i]
        
        # Price-JMA alignment
        price_above_jma = close[i] > jma_array[i-1] if i > 0 else False
        price_below_jma = close[i] < jma_array[i-1] if i > 0 else False
        
        jma_trending_up = jma_array[i-1] > jma_array[i-2] if i > 1 else False
        jma_trending_down = jma_array[i-1] < jma_array[i-2] if i > 1 else False
        
        price_jma_aligned = (jma_trending_up and price_above_jma) or (jma_trending_down and price_below_jma)
        
        # RSI trending detection
        rsi_trending = (rsi_values[i] > 65) or (rsi_values[i] < 35)
        
        # Combined trend detection
        is_trending = rsi_trending or price_jma_aligned or is_uptrend or is_downtrend
        trend_factor = 0.7 if is_trending else 1.3
        
        # Adaptive length based on volatility
        adaptive_length = max(3, int(base_length * (2 - volatility_ratio[i])))
        
        # Adaptive phase
        adaptive_phase = int(base_phase * trend_factor)
        adaptive_phase = max(-100, min(100, adaptive_phase))
        
        # JMA core calculations
        phaseRatio = 0.5 if adaptive_phase < -100 else (2.5 if adaptive_phase > 100 else adaptive_phase / 100 + 1.5)
        beta = 0.45 * (adaptive_length - 1) / (0.45 * (adaptive_length - 1) + 2)
        alpha = np.power(beta, power)
        
        if i == 0:
            e0 = src[i]
            e1 = 0.0
            e2 = 0.0
            jma = src[i]
        else:
            e0 = (1 - alpha) * src[i] + alpha * e0
            e1 = (src[i] - e0) * (1 - beta) + beta * e1
            e2 = (e0 + phaseRatio * e1 - jma) * np.power(1 - alpha, 2) + np.power(alpha, 2) * e2
            jma = e2 + jma
            
            # Reset values if they become too large or unstable (similar to your original code)
            if np.abs(jma) > 1e10:
                e0 = e1 = e2 = jma = 0.0
        
        # Store values for reference in next iteration
        e0_array[i] = e0
        e1_array[i] = e1
        e2_array[i] = e2
        jma_array[i] = jma
        
        if i >= adaptive_length - 1:
            jma_values[i] = np.round(jma, 2)  # Round to 2 decimal places like in your original code
        
        # Signal quality assessment and trend direction
        if i >= 3 and i >= adaptive_length:
            jma_slope = (jma - jma_array[i-3]) / 3
            jma_momentum = jma_slope / (current_atr[i] + 0.0001) * 100
            signal_strength[i] = abs(jma_momentum) / signal_threshold
            
            # Improved trend direction detection
            jma_delta = jma - jma_array[i-1]
            
            # Adaptive multiplier
            adaptive_multiplier = 0.05 + (0.15 * volatility_ratio[i])
            
            # Filter signals based on quality assessment
            if abs(jma_delta) > current_atr[i] * adaptive_multiplier and signal_strength[i] > 1:
                trend_direction[i] = 1 if jma_delta > 0 else -1
            else:
                trend_direction[i] = trend_direction[i-1] if i > 0 else 0
    
    return jma_values, trend_direction

