<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Strategy Manager</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .strategy {
            padding: 10px;
            margin-bottom: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        .enabled {
            background-color: #e0ffe0;
        }
        .disabled {
            background-color: #ffe0e0;
        }
        button {
            padding: 5px 10px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .enable-btn {
            background-color: #4CAF50;
            color: white;
        }
        .disable-btn {
            background-color: #f44336;
            color: white;
        }
        .control-btn {
            background-color: #007BFF;
            color: white;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <h1>Strategy Manager</h1>
    <div id="strategies">
        <!-- Strategies will be dynamically loaded here -->
    </div>

    <script>
        // Fetch and display strategies
        async function loadStrategies() {
            const response = await fetch("/get_all_strategies");
            const strategies = await response.json();
            const strategiesDiv = document.getElementById("strategies");
            strategiesDiv.innerHTML = "";

            strategies.forEach(strategy => {
                const strategyDiv = document.createElement("div");
                strategyDiv.className = `strategy ${strategy.enabled ? "enabled" : "disabled"}`;
                strategyDiv.innerHTML = `
                    <h3>${strategy.strategy_id}</h3>
                    <p>Token: ${strategy.token} | Timeframe: ${strategy.timeframe}</p>
                    <button
                        class="${strategy.enabled ? "disable-btn" : "enable-btn"}"
                        onclick="toggleStrategy('${strategy.token}', '${strategy.timeframe}', '${strategy.strategy_id}')">
                        ${strategy.enabled ? "Disable" : "Enable"}
                    </button>
                    <button
                        class="control-btn"
                        onclick="toggleTradeControl('${strategy.token}', '${strategy.timeframe}', '${strategy.strategy_id}', ${!strategy.enable_buy})">
                        ${strategy.enable_buy ? "Disable Buy" : "Enable Buy"}
                    </button>
                    <button
                        class="control-btn"
                        onclick="toggleTradeControl('${strategy.token}', '${strategy.timeframe}', '${strategy.strategy_id}', ${!strategy.enable_sell}, false)">
                        ${strategy.enable_sell ? "Disable Sell" : "Enable Sell"}
                    </button>
                `;
                strategiesDiv.appendChild(strategyDiv);
            });
        }

        // Toggle strategy enable/disable
        async function toggleStrategy(token, timeframe, strategy_id) {
            const strategy = await fetch(`/get_strategy_state?token=${token}&timeframe=${timeframe}&strategy_id=${strategy_id}`);
            const strategyData = await strategy.json();
            const isEnabled = strategyData.enabled;

            const endpoint = isEnabled ? "/disable_strategy" : "/enable_strategy";
            const response = await fetch(endpoint, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({ token, timeframe, strategy_id }),
            });
            const result = await response.json();
            if (result.status === "success") {
                loadStrategies(); // Refresh the list
            }
        }

        // Toggle buy/sell permissions
        async function toggleTradeControl(token, timeframe, strategy_id, enable_buy, enable_sell = true) {
            const response = await fetch("/set_trade_control", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({ token, timeframe, strategy_id, enable_buy, enable_sell }),
            });
            const result = await response.json();
            if (result.status === "success") {
                loadStrategies(); // Refresh the list
            }
        }

        // Load strategies on page load
        loadStrategies();
    </script>
</body>
</html>