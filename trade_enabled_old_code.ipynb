{"cells": [{"cell_type": "code", "execution_count": null, "id": "3df69203", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "# Load the CSV files into DataFrames\n", "nfo_scrips_df = pd.read_csv('/home/<USER>/Desktop/new/NFO_symbols.csv')\n", "mcx_scrips_df = pd.read_csv('/home/<USER>/Desktop/new/NFO_symbols.csv')\n", "\n", "async def get_lotsize():\n", "    global atm_strike\n", "     \n", "    while True:\n", "        await asyncio.sleep(5)\n", "        if exchange.upper() == 'NSE':\n", "            df = nfo_scrips_df\n", "        elif exchange.upper() == 'MCX':\n", "            df = mcx_scrips_df\n", "        else:\n", "            raise ValueError(\"Unsupported exchange. Please use 'NSE' or 'MCX'.\")\n", "\n", "        # Find the first matching symbol\n", "        match = df[df['Symbol'] == symbol]\n", "        \n", "        if not match.empty:\n", "            # Get LotSize\n", "            lotsize = match.iloc[0]['LotSize']\n", "            \n", "            # Check feedJson if token is provided\n", "            if Initial_token:\n", "                if not feedJson[Initial_token]:\n", "                    logger.info(f\"<PERSON><PERSON><PERSON><PERSON> for token {Initial_token} is empty. LotSize: {lotsize}.\")                    \n", "                else:\n", "                    # Retrieve the last recent price\n", "                    last_price = int(feedJson[Initial_token][-1]['ltp'])\n", "                    # Calculate ATM strike price\n", "                    mod = int(last_price) % 100\n", "                    atm_strike = last_price - mod if mod <= 50 else last_price + (100 - mod)                    \n", "                    # Find trading symbols for ATM strike\n", "                    filtered_df = df[\n", "                        (df['Symbol'] == symbol) &\n", "                        (df['StrikePrice'] == float(atm_strike))\n", "                    ]\n", "                    \n", "                    if filtered_df.empty:\n", "                        logger.info(f\"Could not find trading symbols for ATM strike {atm_strike}\")\n", "                        #return lotsize, last_price, None, None\n", "                        logger.info(f\"wait for lot size to update {lotsize}.\")\n", "\n", "                    # Find the CE and PE trading symbols\n", "                    ce_row = filtered_df[filtered_df['OptionType'] == 'CE'].sort_values('Expiry').iloc[0]\n", "                    pe_row = filtered_df[filtered_df['OptionType'] == 'PE'].sort_values('Expiry').iloc[0]\n", "\n", "                    ce_trading_symbol, pe_trading_symbol = ce_row['TradingSymbol'], pe_row['TradingSymbol']\n", "                    ce_trading_token, pe_trading_token = ce_row['Token'], pe_row['Token']                    \n", "                    \n", "                      # Update the state\n", "                    state.ce_trading_symbol = ce_trading_symbol\n", "                    state.pe_trading_symbol = pe_trading_symbol\n", "                    state.ce_trading_token = ce_trading_token\n", "                    state.pe_trading_token = pe_trading_token\n", "\n", "                    #logger.info(f\"CE Symbol: {ce_trading_symbol}, PE Symbol: {pe_trading_symbol}\")            \n", "        else:\n", "            logger.warning(f\"{symbol} not found on {exchange}.\")\n", "\n", "async def resample_ticks():\n", "    global resampled_data\n", "    last_timestamp = None\n", "    \n", "    while True:\n", "        await asyncio.sleep(0.1)  # Reduced sleep time\n", "        if not feedJson:\n", "            continue\n", "\n", "        with feed_lock:\n", "            feed_data_copy = feedJson.copy()\n", "        \n", "        # Check for the latest tick timestamp to detect changes\n", "        latest_timestamp = max([ticks[-1]['tt'] for token, ticks in feed_data_copy.items() if ticks])\n", "\n", "        if latest_timestamp == last_timestamp:\n", "            continue  # Skip if there's no new tick data\n", "\n", "        temp_resampled_data = {}\n", "        for token, ticks in feed_data_copy.items():\n", "            if not ticks:\n", "                continue\n", "            \n", "            df_new = pd.DataFrame(ticks)\n", "            df_new[\"tt\"] = pd.to_datetime(df_new[\"tt\"])\n", "            df_new.set_index(\"tt\", inplace=True)\n", "\n", "            current_resample_interval = df_new.index.max().floor(resample_frequency)\n", "\n", "            if token not in resampled_data:\n", "                df_resampled = df_new['ltp'].resample(resample_frequency).ohlc()\n", "                temp_resampled_data[token] = df_resampled\n", "                last_resample_time[token] = df_resampled.index.max()\n", "            else:\n", "                temp_df = resampled_data[token].copy()\n", "                df_resampled = df_new['ltp'].resample(resample_frequency).ohlc()\n", "\n", "                for idx, row in df_resampled.iterrows():\n", "                    if idx in temp_df.index:\n", "                        temp_df.loc[idx, 'high'] = max(temp_df.loc[idx, 'high'], row['high'])\n", "                        temp_df.loc[idx, 'low'] = min(temp_df.loc[idx, 'low'], row['low'])\n", "                        temp_df.loc[idx, 'close'] = row['close']\n", "                    else:\n", "                        temp_df.loc[idx] = row\n", "\n", "                last_resample_time[token] = df_resampled.index.max()\n", "                temp_resampled_data[token] = temp_df\n", "\n", "            if token in temp_resampled_data:\n", "                supertrend, su_direction = numba_indicators.supertrend_numba(\n", "                    temp_resampled_data[token]['high'].values,\n", "                    temp_resampled_data[token]['low'].values,\n", "                    temp_resampled_data[token]['close'].values\n", "                \n", "                )\n", "                temp_resampled_data[token]['supertrend'] = supertrend\n", "                temp_resampled_data[token]['su_direction'] = su_direction\n", "\n", "                jma, ce_direction = numba_indicators.jma_numba_direction(\n", "                    temp_resampled_data[token]['close'].values\n", "                )\n", "                temp_resampled_data[token]['jma'] = jma\n", "                temp_resampled_data[token]['ce_direction'] = ce_direction\n", "\n", "            temp_resampled_data[token] = temp_resampled_data[token].dropna(subset=['open', 'high', 'low', 'close'])\n", "\n", "        with feed_lock:\n", "            resampled_data = temp_resampled_data\n", "           \n", "        # Update the last feed data\n", "        last_timestamp = latest_timestamp\n", "\n", "async def candle_end_finder():\n", "    global resampled_data, resample_frequency\n", "    logger.info(\"Starting candle_end_finder\")\n", "\n", "    while True:\n", "        current_time = pd.Timestamp.now()\n", "        time_bucket_start = current_time.floor(resample_frequency)\n", "        time_bucket_end = time_bucket_start + pd.Timedelta(resample_frequency)\n", "        wait_time = (time_bucket_end - current_time).total_seconds() + 0.001\n", "\n", "        if wait_time > 0:\n", "            await asyncio.sleep(wait_time)\n", "\n", "        data_copy = {}\n", "        with feed_lock:\n", "            if not resampled_data:\n", "                logger.warning(\"resampled_data is empty.\")\n", "                continue\n", "\n", "            # Copying the data outside of the lock to avoid data modification during processing\n", "            for token, df in resampled_data.items():\n", "                if df is None or df.empty:\n", "                    logger.warning(f\"DataFrame for token {token} is empty or None.\")\n", "                    continue\n", "\n", "                data_copy[token] = df.copy()\n", "\n", "        # Process each token's data outside of the lock\n", "        for token, df in data_copy.items():\n", "            logger.debug(f\"Processing token: {token}\")\n", "\n", "            # Pre-emptive calculations outside the lock (if possible)\n", "            previous_length = len(completed_candles_dfs[token]) if token in completed_candles_dfs else 0\n", "\n", "            async with data_lock:               \n", "\n", "                if token not in last_processed_candle or last_processed_candle[token] < time_bucket_end:\n", "                    try:\n", "                        if time_bucket_start == df.index[-1]:\n", "                            # Only include the last candle if it matches the bucket start\n", "                            completed_candles_dfs[token] = df\n", "                            last_processed_candle[token] = time_bucket_start\n", "\n", "                        elif time_bucket_end == df.index[-1]:\n", "                            # If the end bucket matches, take all up to that point\n", "                            completed_candles_dfs[token] = df.loc[:time_bucket_end - pd.Timedelta(microseconds=1)]\n", "                            last_processed_candle[token] = time_bucket_start\n", "                        else:\n", "                            logger.error(f\"No new candle forming now for token {token}\")\n", "                            continue\n", "\n", "                        new_length = len(completed_candles_dfs[token])  # Calculate new_length inside the lock\n", "                        rows_added = new_length - previous_length\n", "\n", "                        if rows_added > 1:\n", "                            logger.warning(f\"Multiple rows ({rows_added}) added for token {token} at {pd.Timestamp.now()}. \"\n", "                                           f\"Previous length: {previous_length}, New length: {new_length}\")\n", "                        # Verify that the required columns exist\n", "                        try:\n", "                            required_columns = ['ce_direction']\n", "                            missing_columns = [col for col in required_columns if\n", "                                               col not in completed_candles_dfs[token].columns]\n", "                            if missing_columns:\n", "                                raise KeyError(f\"Missing columns {missing_columns}\")\n", "\n", "                        except KeyError as e:\n", "                            logger.error(f\"Error processing token {token}: {e}\")\n", "\n", "                    except KeyError as e:\n", "                        logger.error(f\"Error processing token {token}: Column {e} not found.\")\n", "                    except IndexError:\n", "                        logger.error(f\"Error processing token {token}: Not enough candles to calculate indicators.\")\n", "                logger.debug(f\"candle_end_finder released data_lock for token {token}\")\n", "\n", "        # Calculate the time for the next bucket\n", "        next_bucket_start = time_bucket_end\n", "        wait_time = (next_bucket_start - pd.Timestamp.now()).total_seconds()\n", "        if wait_time > 0:\n", "            await asyncio.sleep(wait_time)\n", "            \n", "def get_latest_price_option(entry_instrument):\n", "    entry_instrument_str = str(entry_instrument)\n", "    with feed_lock:\n", "        if entry_instrument_str in extra_feedJson:\n", "            latest_data = extra_feedJson[entry_instrument_str][-1]\n", "            latest_price = latest_data['ltp']            \n", "            return latest_price\n", "        else:\n", "            print(f\"{entry_instrument_str} not found in extra_feedJson\")  # Debug print\n", "            return None\n", "        \n", "def get_latest_price_index(entry_instrument):\n", "    entry_instrument_str = str(entry_instrument)\n", "    with feed_lock:\n", "        if entry_instrument_str in feedJson:\n", "            latest_data = feedJson[entry_instrument_str][-1]\n", "            latest_price = latest_data['ltp']            \n", "            return latest_price\n", "        else:\n", "            print(f\"{entry_instrument_str} not found in feedJson\")  # Debug print\n", "            return None\n", "\n", "async def direction_change_event_handler():\n", "    last_changes = {}\n", "    while True:        \n", "        async with data_lock:            \n", "            for token, df in completed_candles_dfs.items():\n", "                if df.empty:\n", "                    continue\n", "                current_direction = df['ce_direction'].iloc[-1]\n", "                second_direction = df['su_direction'].iloc[-1] \n", "                last_direction = last_changes.get(token, {}).get('ce_direction', None)\n", "\n", "                # Check if the 'ce_direction' value has changed\n", "                if last_direction is None or current_direction != last_direction:\n", "                    logger.info(f\"Direction change detected for token {token} at {df.index[-1]}: {current_direction}\")\n", "                    #await direction_change_queue.put((token, current_direction, last_direction, second_direction)) \n", "                    await direction_change_queue.put((1, token, current_direction, last_direction, second_direction))\n", "\n", "                # Update last changes\n", "                last_changes[token] = {'index': df.index[-1], 'ce_direction': current_direction}\n", "        \n", "        await asyncio.sleep(0.1)\n", "    \n", "async def process_direction_changes():\n", "    while True:\n", "        #token, current_direction, previous_direction, second_direction = await direction_change_queue.get() \n", "        priority, token, current_direction, previous_direction, second_direction = await direction_change_queue.get()\n", "\n", "        try:\n", "            await execute_trade_logic(token, current_direction, previous_direction, second_direction) \n", "        except Exception as e:\n", "            logger.error(f\"Error executing trade logic for {token}: {e}\")\n", "\n", "        direction_change_queue.task_done()\n", "    \n", "import asyncio\n", "import nest_asyncio\n", "import logging\n", "import pandas as pd\n", "import pyotp \n", "from asyncio import CancelledError\n", "from collections import defaultdict, deque\n", "from threading import Thread\n", "from threading import Lock\n", "from datetime import datetime\n", "import numba_indicators\n", "import uuid\n", "from NorenRestApiPy.NorenApi import NorenApi\n", "\n", "# Initialize logger\n", "logger = logging.getLogger('S<PERSON><PERSON>aApi')\n", "logger.setLevel(logging.INFO) \n", "handler = logging.StreamHandler()\n", "formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')\n", "handler.set<PERSON><PERSON><PERSON><PERSON>(formatter)\n", "logger.add<PERSON><PERSON><PERSON>(handler)\n", "\n", "file_handler = logging.FileHandler('shoonya_api.log') # Replace 'shoonya_api.log' with your desired log file name\n", "file_handler.setLevel(logging.INFO)  # Set the logging level for the file handler\n", "# Apply the same formatter to the file handler\n", "file_handler.setFormatter(formatter)\n", "# Add the file handler to the logger\n", "logger.addHandler(file_handler) \n", "\n", "# Global variables\n", "completed_candles_dfs = defaultdict(pd.DataFrame)\n", "last_processed_candle = defaultdict(pd.Timestamp)\n", "direction_change_queue = asyncio.PriorityQueue() ##@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@\n", "use_second_direction = True\n", "resampled_data = {}\n", "last_resample_time = {}\n", "monitoring_tasks = {}\n", "current_positions = {}\n", "feed_opened = False\n", "feed_lock = Lock()\n", "data_lock = asyncio.Lock()\n", "position_lock = asyncio.Lock()\n", "feedJson = defaultdict(lambda: deque(maxlen=20))\n", "extra_feedJson = defaultdict(lambda: deque(maxlen=20))\n", "api = None\n", "exchange = 'MCX'\n", "Initial_token = '432294'\n", "subscription_string = f\"{exchange}|{Initial_token}\"\n", "resample_frequency = \"15s\"\n", "symbol = \"CRUDEOILM\"\n", "atm_strike = int\n", "\n", "# TRADE CONTROL flags \n", "enable_call_trades = True\n", "enable_put_trades = True\n", "enable_all_trades = True\n", "force_exit_triggered = False\n", "\n", "class TradingState:\n", "    def __init__(self):\n", "        self.ce_trading_symbol = None\n", "        self.pe_trading_symbol = None\n", "        self.ce_trading_token = None\n", "        self.pe_trading_token = None\n", "state = TradingState()\n", "\n", "def initialize_api(credentials_file=\"usercred.xlsx\"):\n", "    global api\n", "    api = NorenApi(\n", "        host=\"https://api.shoonya.com/NorenWClientTP/\",\n", "        websocket=\"wss://api.shoonya.com/NorenWSTP/\"\n", "    )\n", "\n", "    credentials = pd.read_excel(credentials_file)\n", "    user = credentials.iloc[0, 0]\n", "    password = credentials.iloc[0, 1]\n", "    vendor_code = credentials.iloc[0, 2]\n", "    app_key = credentials.iloc[0, 3]\n", "    imei = credentials.iloc[0, 4]\n", "    qr_code = credentials.iloc[0, 5]\n", "    factor2 = pyotp.TOTP(qr_code).now()\n", "\n", "    api.login_result = api.login(\n", "        userid=user,\n", "        password=password,\n", "        twoFA=factor2,\n", "        vendor_code=vendor_code,\n", "        api_secret=app_key,\n", "        imei=imei\n", "    )\n", "\n", "def event_handler_order_update(data):\n", "    logger.info(f\"Order update: {data}\")\n", "\n", "def event_handler_feed_update(tick_data):\n", "    try:\n", "        if 'lp' in tick_data and 'tk' in tick_data:\n", "            timest = datetime.fromtimestamp(int(tick_data['ft'])).isoformat()\n", "            token = tick_data['tk']\n", "\n", "            with feed_lock:  # Acquire lock for thread-safety\n", "\n", "                    if token == Initial_token:\n", "                        feedJson[token].append({'ltp': float(tick_data['lp']), 'tt': timest})\n", "                    else:\n", "                        extra_feedJson[token].append({'ltp': float(tick_data['lp']), 'tt': timest})\n", "\n", "    except (<PERSON><PERSON><PERSON><PERSON>, <PERSON>E<PERSON>r) as e:\n", "        logger.error(f\"Error processing tick data: {e}\")\n", "\n", "async def connect_and_subscribe():\n", "    global feed_opened\n", "    retry_delay = 1  # Initial retry delay in seconds\n", "    max_retry_delay = 32  # Maximum retry delay in seconds\n", "    while True:\n", "        try:\n", "            api.start_websocket(\n", "                order_update_callback=event_handler_order_update,\n", "                subscribe_callback=event_handler_feed_update,\n", "                socket_open_callback=open_callback,\n", "                socket_close_callback=close_callback\n", "            )\n", "            await wait_for_feed_open(timeout=30)  # Wait for feed to open with a timeout\n", "            api.subscribe([subscription_string])\n", "            logger.info(\"WebSocket connected and subscribed successfully.\")\n", "            retry_delay = 1  # Reset retry delay after successful connection\n", "            await monitor_connection()\n", "        except Exception as e:\n", "            logger.error(f\"WebSocket connection error: {e}\")\n", "            logger.info(f\"Reconnecting in {retry_delay} seconds...\")\n", "            await asyncio.sleep(retry_delay)\n", "            retry_delay = min(retry_delay * 2, max_retry_delay)  # Exponential backoff\n", "\n", "async def wait_for_feed_open(timeout):\n", "    global feed_opened\n", "    start_time = asyncio.get_event_loop().time()\n", "    while not feed_opened:\n", "        if asyncio.get_event_loop().time() - start_time > timeout:\n", "            raise TimeoutError(\"Timed out waiting for feed to open\")\n", "        await asyncio.sleep(1)\n", "\n", "async def monitor_connection():\n", "    global feed_opened\n", "    while True:\n", "        if not feed_opened:\n", "            logger.warning(\"Feed closed unexpectedly. Reconnecting...\")\n", "            raise Exception(\"Feed closed\")\n", "        await asyncio.sleep(5)  # Check connection status every 5 seconds\n", "\n", "def close_callback():\n", "    global feed_opened\n", "    feed_opened = False\n", "    logger.warning(\"WebSocket connection closed.\")\n", "\n", "def open_callback():\n", "    global feed_opened\n", "    if not feed_opened:\n", "        feed_opened = True\n", "        logger.info('Feed Opened')\n", "    else:\n", "        logger.warning('Feed Opened callback called multiple times.')\n", "\n", "async def main():   \n", "    initialize_api()  \n", "    websocket_task = asyncio.create_task(connect_and_subscribe())\n", "    resample_task = asyncio.create_task(resample_ticks())\n", "    lot_size_task = asyncio.create_task(get_lotsize()) \n", "    candle_end_finder_task = asyncio.create_task(candle_end_finder())     \n", "    direction_change_event_handler_task = asyncio.create_task(direction_change_event_handler())\n", "    process_direction_changes_task = asyncio.create_task(process_direction_changes())\n", "\n", "    try:\n", "        await asyncio.gather(\n", "            websocket_task, lot_size_task, resample_task, candle_end_finder_task,\n", "            direction_change_event_handler_task, process_direction_changes_task\n", "        )   \n", "\n", "    except async<PERSON>.CancelledError:\n", "        logger.info(\"Tasks cancelled. Shutting down...\")\n", "\n", "\n", "#Your existing event loop setup\n", "loop = asyncio.get_event_loop()\n", "loop.set_debug(True)\n", "if loop.is_running():\n", "    nest_asyncio.apply()\n", "asyncio.create_task(main())\n", "if not loop.is_running():\n", "    try:\n", "        loop.run_forever()\n", "    except KeyboardInterrupt:\n", "        logger.info(\"Received exit signal. Cleaning up...\")\n", "        # Add any cleanup code here\n", "    finally:\n", "        loop.close()\n", "        logger.info(\"Event loop closed. Exiting.\")\n", "\n", "class TradeTracker:\n", "    def __init__(self):\n", "        self.total_trades = 0\n", "        self.total_points_collected = 0\n", "        self.total_points_lost = 0\n", "        self.overall_profit_loss = 0\n", "        self.win_trades = 0\n", "        self.loss_trades = 0\n", "        self.zero_pnl_trades = 0  # New attribute to track zero P/L trades\n", "\n", "    async def update_stats(self, profit_loss):\n", "        self.total_trades += 1\n", "\n", "        if profit_loss > 0:\n", "            self.win_trades += 1\n", "            self.total_points_collected += profit_loss\n", "        elif profit_loss < 0:\n", "            self.loss_trades += 1\n", "            self.total_points_lost += abs(profit_loss)\n", "        else:  # Handle zero P/L trades\n", "            self.zero_pnl_trades += 1\n", "        self.overall_profit_loss += profit_loss\n", "\n", "    async def print_summary(self):\n", "        logger.info(\"Trade Summary:\")\n", "        logger.info(f\"  Total Trades: {self.total_trades}\")\n", "        logger.info(f\"  Win Trades: {self.win_trades}\")\n", "        logger.info(f\"  Loss Trades: {self.loss_trades}\")\n", "        logger.info(f\"  Zero P/L Trades: {self.zero_pnl_trades}\")  # Add this line\n", "        logger.info(f\"  Total Points Collected: {self.total_points_collected:.2f}\")\n", "        logger.info(f\"  Total Points Lost: {self.total_points_lost:.2f}\")\n", "        logger.info(f\"  Overall Profit/Loss: {self.overall_profit_loss:.2f} points\")\n", "\n", "# Create a TradeTracker instance\n", "trade_tracker = TradeTracker()\n", "\n", "class HistoricalTrade:\n", "    def __init__(self, trade_id, token, symbol_name, entry_price, position_type, entry_time=None, exit_time=None, exit_price=None, pnl=None):\n", "        self.trade_id = trade_id  # Unique identifier (UUID)\n", "        self.token = token\n", "        self.symbol_name = symbol_name\n", "        self.entry_price = entry_price\n", "        self.position_type = position_type  # 'call_buy' or 'put_buy'\n", "        self.entry_time = entry_time\n", "        self.exit_time = exit_time\n", "        self.exit_price = exit_price\n", "        self.pnl = pnl\n", "\n", "class HistoricalTradeTracker:\n", "    def __init__(self):\n", "        self.trades = {}  # Store trades by trade_id\n", "\n", "    def add_trade(self, trade_id, trade):\n", "        self.trades[trade_id] = trade\n", "\n", "    def get_trade_by_id(self, trade_id):\n", "        return self.trades.get(trade_id, None)\n", "\n", "    def update_trade(self, trade_id, exit_time, exit_price, pnl):\n", "        historical_trade = self.get_trade_by_id(trade_id)\n", "        if historical_trade:\n", "            historical_trade.exit_time = exit_time\n", "            historical_trade.exit_price = exit_price\n", "            historical_trade.pnl = pnl\n", "\n", "    def print_historical_trades(self):\n", "        logger.info(\"Historical Trades:\")\n", "        for trade in self.trades.values():\n", "            entry_time = trade.entry_time or \"N/A\"\n", "            exit_time = trade.exit_time or \"N/A\"\n", "            exit_price = trade.exit_price if trade.exit_price is not None else \"N/A\"\n", "            pnl = f\"{trade.pnl:.2f}\" if trade.pnl is not None else \"N/A\"\n", "            \n", "            logger.info(f\"  Token: {trade.token}, Symbol: {trade.symbol_name}, Entry Time: {entry_time}, \"\n", "                        f\"Entry Price: {trade.entry_price}, Exit Time: {exit_time}, \"\n", "                        f\"Exit Price: {exit_price}, P/L: {pnl} points\")\n", "\n", "# Create an instance of HistoricalTradeTracker\n", "historical_trade_tracker = HistoricalTradeTracker()\n", "\n", "class Trade:\n", "    # Class-level attribute for default SL\n", "    default_sl_price = 5\n", "    default_trailing_sl_price = 5\n", "\n", "    def __init__(self, token, position_type, entry_price, option_token, symbol_name):\n", "        self.token = token\n", "        self.position_type = position_type\n", "        self.entry_price = entry_price\n", "        self.option_token = option_token\n", "        self.symbol_name = symbol_name\n", "        self.monitoring_task = None\n", "        self.exit_event = asyncio.Event()\n", "        self.exited = False  # Flag to indicate if the trade has exited\n", "        self.trade_id = None  # Added trade_id attribute   @@@@@@@@@@@@@@@@@@@@@@@@@@@\n", "        \n", "        # Assign default SL and trailing SL from class-level attribute\n", "        self.highest_price = entry_price\n", "        self.target_price = entry_price + 20\n", "        self.sl_price = entry_price - Trade.default_sl_price  # Use class-level default SL\n", "        self.trailing_sl_price = entry_price - Trade.default_trailing_sl_price\n", "        self.trail_activated = False\n", "        self.trail_activation_point = 5\n", "\n", "    @classmethod\n", "    async def update_default_sl(cls, new_default_sl_price):\n", "        \"\"\"Update the class-level default SL price.\"\"\"\n", "        cls.default_sl_price = new_default_sl_price\n", "        logger.info(f\"Default SL updated globally to {cls.default_sl_price}\")\n", "\n", "    @classmethod\n", "    async def update_default_trailing_sl(cls, new_default_trailing_sl_price):\n", "        \"\"\"Update the class-level default trailing SL price.\"\"\"\n", "        cls.default_trailing_sl_price = new_default_trailing_sl_price\n", "        logger.info(f\"Default trailing SL updated globally to {cls.default_trailing_sl_price}\")\n", "\n", "    async def update_sl(self, new_sl_price):\n", "        \"\"\"Update the stop loss price.\"\"\"\n", "        \n", "        if not self.exited:\n", "            self.sl_price = self.entry_price - new_sl_price\n", "            logger.info(f\"SL updated for {self.symbol_name} to {self.sl_price}\")\n", "\n", "    async def update_trailing_sl(self, new_trailing_sl_price):\n", "        \"\"\"Update the trailing stop loss price.\"\"\"\n", "        \n", "        if not self.exited:\n", "            self.trailing_sl_price = self.highest_price - new_trailing_sl_price\n", "            self.trail_activated = True \n", "            logger.info(f\"Trailing SL updated for {self.symbol_name} to {self.trailing_sl_price}\")\n", "    \n", "    # Add a method to update the target price if needed\n", "    async def update_target_price(self, new_target_price):\n", "        if not self.exited:\n", "            self.target_price = new_target_price\n", "            logger.info(f\"Target updated for {self.symbol_name} to {self.target_price}\")\n", "\n", "    async def force_exit(self, exit_reason=\"Forced Exit\"):\n", "        \"\"\"Force exit the trade immediately.\"\"\"\n", "        \n", "        if not self.exited:\n", "            await exit_trade(self, exit_reason)\n", "            logger.info(f\"Trade forcibly exited for {self.symbol_name}\")\n", "\n", "async def monitor_position(trade):\n", "    try:\n", "        while not trade.exited:\n", "            await asyncio.sleep(0.10)\n", "            current_price = get_latest_price_option(trade.option_token)\n", "\n", "            if current_price is None:\n", "                continue\n", "            if current_price >= trade.target_price:\n", "                await exit_trade(trade, \"Target Reached\")\n", "                break\n", "            elif current_price <= trade.sl_price:\n", "                await exit_trade(trade, \"Stop Loss Hit\")\n", "                break\n", "            elif not trade.trail_activated and current_price >= (trade.entry_price + trade.trail_activation_point):\n", "                trade.trail_activated = True\n", "                trade.trailing_sl_price = trade.entry_price\n", "                logger.info(f\"Trailing SL activated for {trade.symbol_name} at {trade.trailing_sl_price}\")\n", "            elif trade.trail_activated and current_price <= trade.trailing_sl_price:\n", "                await exit_trade(trade, \"Trailing Stop Loss Hit\")\n", "                break\n", "\n", "            if current_price > trade.highest_price:\n", "                trade.highest_price = current_price\n", "                # if trade.trail_activated:\n", "                #     trade.trailing_sl_price = trade.highest_price - (trade.trail_activation_point - 3)\n", "        # Once the trade is exited, remove it from the UI\n", " \n", "    except async<PERSON>.CancelledError:\n", "        logger.info(f\"Monitoring cancelled for {trade.token} due to normal exit\")\n", "    except Exception as e:\n", "        logger.error(f\"Error in monitor_position for {trade.token}: {e}\")\n", "        \n", "async def enter_trade(token, position_type, entry_type):\n", "    try:\n", "        option_token = state.ce_trading_token if position_type == \"call_buy\" else state.pe_trading_token\n", "        symbol_name = state.ce_trading_symbol if position_type == \"call_buy\" else state.pe_trading_symbol\n", "\n", "        entry_price = get_latest_price_option(option_token)  # Assuming this function exists\n", "        if entry_price is None:\n", "            logger.error(f\"Unable to get entry price for {symbol_name}\")\n", "            return\n", "        # Create a new Trade object\n", "        # Create a unique identifier for the trade and its monitoring task\n", "        trade_id = uuid.uuid4()\n", "        trade = Trade(token, position_type, entry_price, option_token, symbol_name)\n", "        trade.trade_id = trade_id\n", "\n", "        # Update current_positions\n", "        current_positions[token] = trade\n", "        action_time = pd.Timestamp.now()\n", "        logger.warning(f\"Trade entered at {action_time} - {symbol_name} at {entry_price}\")\n", "\n", "        # Create a unique identifier for the trade and its monitoring task\n", "        # trade_id = uuid.uuid4()\n", "        trade.monitoring_task = asyncio.create_task(monitor_position(trade))\n", "        monitoring_tasks[trade_id] = trade.monitoring_task\n", "\n", "        historical_trade = HistoricalTrade(trade_id, token, symbol_name, entry_price, position_type, entry_time=action_time)\n", "        historical_trade_tracker.add_trade(trade_id, historical_trade)\n", "\n", "        # Place actual order using your brokerage API (replace with your actual order placement logic)\n", "        # order_result = place_order(symbol_name, position_type, lot_size, entry_price)  # Example\n", "        # logger.info(f\"Order placed: {order_result}\")\n", "\n", "        # Broadcast the trade update to all connected clients FASTAPI\n", "       \n", "    except Exception as e:\n", "        logger.error(f\"Error in enter_trade for {token}: {e}\")\n", "        raise\n", "\n", "async def exit_trade(trade, exit_type):    \n", "    try:\n", "        exit_price = get_latest_price_option(trade.option_token)  # Assuming this function exists\n", "        if exit_price is None:\n", "            logger.error(f\"Unable to get exit price for {trade.symbol_name}\")\n", "            return\n", "\n", "        profit_loss = (exit_price - trade.entry_price) \n", "\n", "        action_time = pd.Timestamp.now()\n", "        logger.warning(f\"Trade exited at {action_time} - {trade.symbol_name} at {exit_price}. {exit_type}. P/L: {profit_loss:.2f} points\")\n", "\n", "        # Mark the trade as exited and cancel the monitoring task\n", "        trade.exited = True\n", "        if trade.monitoring_task:\n", "            trade_id = next((tid for tid, task in monitoring_tasks.items() if task == trade.monitoring_task), None)\n", "            if trade_id:\n", "                monitoring_tasks[trade_id].cancel()\n", "                del monitoring_tasks[trade_id]        \n", "        await trade_tracker.update_stats(profit_loss)\n", "\n", "        historical_trade_tracker.update_trade(trade_id, exit_time=action_time, exit_price=exit_price, pnl=profit_loss)\n", "\n", "        # Place actual exit order using your brokerage API (replace with your actual order placement logic)\n", "        # order_result = place_exit_order(trade.symbol_name, trade.position_type, lot_size, exit_price)  # Example\n", "        # logger.info(f\"Exit order placed: {order_result}\")\n", "\n", "    except Exception as e:\n", "        logger.error(f\"Error in exit_trade for {trade.token}: {e}\")\n", "        raise\n", "\n", "async def execute_trade_logic(token, current_direction, previous_direction, second_direction):\n", "    global current_positions, position_lock\n", "\n", "    # 1. Check for active trade outside the lock (read-only operation)\n", "    active_trade = next((trade for trade in current_positions.values() if not trade.exited), None)\n", "\n", "    if active_trade:\n", "        exit_signal = (\n", "            (active_trade.position_type == 'call_buy' and current_direction == -1 and previous_direction == 1) or\n", "            (active_trade.position_type == 'put_buy' and current_direction == 1 and previous_direction == -1)\n", "        )\n", "\n", "        if exit_signal:\n", "            async with position_lock:  # Acquire lock only for exiting the trade\n", "                try:\n", "                    logger.warning(\"exiting\")\n", "                    await exit_trade(active_trade, \"Regular Exit\")\n", "                except Exception as e:\n", "                    print(f\"Error exiting trade for {token}: {e}\")\n", "\n", "                # After exiting, set active_trade to None\n", "                active_trade = None\n", "\n", "    if not active_trade:\n", "        entry_signal = False\n", "\n", "        # 2. Calculate entry signal outside the lock\n", "        if use_second_direction:\n", "            second_direction_valid = second_direction == current_direction\n", "        else:\n", "            second_direction_valid = True\n", "\n", "        if current_direction == 1 and previous_direction == -1 and enable_call_trades and enable_all_trades and second_direction_valid:\n", "            position_type = \"call_buy\"\n", "            entry_signal = True\n", "        elif current_direction == -1 and previous_direction == 1 and enable_put_trades and enable_all_trades and second_direction_valid:\n", "            position_type = \"put_buy\"\n", "            entry_signal = True\n", "\n", "        if entry_signal:\n", "            # 3. Check candle significance outside the lock\n", "            df = completed_candles_dfs[token]\n", "            high = df['high'].values\n", "            low = df['low'].values\n", "            is_significant, avg_height, current_height = numba_indicators.check_significant_candle(high, low)\n", "\n", "            if not is_significant:\n", "                async with position_lock:  # Acquire lock only for entering the trade\n", "                    try:\n", "                        logger.warning(\"entering normal trade.\")\n", "                        await enter_trade(token, position_type, \"Regular Entry\")\n", "                    except Exception as e:\n", "                        print(f\"Error entering trade for {token}: {e}\")\n", "            else:\n", "                logger.warning(f\"Skipping entry due to significant candle. Avg height: {avg_height}, Current height: {current_height}\")\n", "                    # Start monitoring for 50% retracement\n", "                    #asyncio.create_task(monitor_retracement(token, position_type, current_height, df['close'].iloc[-1], current_direction))"]}, {"cell_type": "code", "execution_count": null, "id": "63f091c7", "metadata": {}, "outputs": [], "source": ["resampled_data"]}, {"cell_type": "code", "execution_count": null, "id": "8558115c", "metadata": {}, "outputs": [], "source": ["trade1 = current_positions.get('432294')  # Fetch the trade object\n", "\n", "if trade1:\n", "    await trade1.update_sl(new_sl_price=1)  # Update SL dynamically"]}, {"cell_type": "code", "execution_count": null, "id": "545a4b95", "metadata": {}, "outputs": [], "source": ["trade1 = current_positions.get('432294')  # Fetch the trade object\n", "if trade1:\n", "    \n", "   await trade1.update_trailing_sl(new_trailing_sl_price=0)  # Update Trailing SL"]}, {"cell_type": "code", "execution_count": 5, "id": "cebcf70c", "metadata": {}, "outputs": [], "source": ["trade1 = current_positions.get('432294')  # Fetch the trade object\n", "if trade1:\n", "   \n", "    await trade1.force_exit()  # Force exit the trade if needed"]}, {"cell_type": "code", "execution_count": null, "id": "b025b068", "metadata": {}, "outputs": [], "source": ["await trade_tracker.print_summary()"]}, {"cell_type": "code", "execution_count": null, "id": "4948a7d7", "metadata": {}, "outputs": [], "source": ["historical_trade_tracker.print_historical_trades()"]}, {"cell_type": "code", "execution_count": null, "id": "d13962f4", "metadata": {}, "outputs": [], "source": ["await Trade.update_default_sl(10)  # Update global default SL to -10\n", "await Trade.update_default_trailing_sl(10)  # Update global default trailing SL to -8"]}, {"cell_type": "code", "execution_count": null, "id": "769152ed", "metadata": {}, "outputs": [], "source": ["async def get_current_positions_info():\n", "    async with position_lock:  # Acquire the lock to ensure data consistency\n", "        for token, trade in current_positions.items():\n", "            print(f\"Token: {token}\")\n", "            print(f\"  Position Type: {trade.position_type}\")\n", "            print(f\"  Entry Price: {trade.entry_price}\")\n", "            print(f\"  Stop Loss Price: {trade.sl_price}\")\n", "            print(f\"  Trailing Stop Loss Price: {trade.trailing_sl_price}\")\n", "            print(f\"  Trailing SL Activated: {trade.trail_activated}\")\n", "            print(f\"  Trade Exited: {trade.exited}\")  # Add this line\n", "            print(\"------------------\")\n", "await get_current_positions_info()"]}, {"cell_type": "code", "execution_count": null, "id": "aa5061c8", "metadata": {}, "outputs": [], "source": ["def option_subscription():\n", "    op_chain = api.get_option_chain(exchange='NFO', tradingsymbol=state.ce_trading_symbol, strikeprice=atm_strike, count = 15)\n", "    tokens = [item['token'] for item in op_chain['values']]\n", "    subscriptions = [f\"NFO|{token}\" for token in tokens]\n", "    # Subscribe to each token\n", "    for sub in subscriptions:\n", "        api.subscribe(sub)\n", "\n", "    # Print the subscriptions for verification\n", "    print(\"Subscribed to:\", subscriptions)\n", "\n", "option_subscription()"]}, {"cell_type": "code", "execution_count": null, "id": "20a66149", "metadata": {}, "outputs": [], "source": ["\n", "def append_to_csv(trade_log):\n", "    with open('trading_log.csv', 'a', newline='') as file:\n", "        writer = csv.writer(file)\n", "        writer.writerow()\n", "\n", "import pandas as pd\n", "\n", "# Assuming resampled_data has the format {'token_name': DataFrame}\n", "token, df = next(iter(completed_candles_dfs.items()))  # Get the token name and DataFrame\n", "\n", "df.to_csv(f\"{token}.csv\", index=True)   # Save the DataFrame to a CSV file (include the index)\n", "print(f\"Saved data to {token}.csv\")   "]}, {"cell_type": "code", "execution_count": null, "id": "a39f4f49", "metadata": {}, "outputs": [], "source": ["# import ipywidgets as widgets\n", "# from IPython.display import display, clear_output, HTML\n", "# import pandas as pd\n", "# import asyncio\n", "\n", "\n", "# # Function to update trade history\n", "# def update_trade_history():\n", "#     trades = [\n", "#         {\n", "#             \"Token\": trade.token,\n", "#             \"Symbol\": trade.symbol_name,\n", "#             \"Entry Time\": trade.entry_time,\n", "#             \"Entry Price\": trade.entry_price,\n", "#             \"Exit Time\": trade.exit_time if hasattr(trade, 'exit_time') else \"N/A\",\n", "#             \"Exit Price\": trade.exit_price if hasattr(trade, 'exit_price') else \"N/A\",\n", "#             \"P/L\": trade.pnl if hasattr(trade, 'pnl') else \"N/A\"\n", "#         }\n", "#         for trade in historical_trade_tracker.trades.values()\n", "#     ]\n", "#     return pd.DataFrame(trades)\n", "\n", "# # Function to get current positions\n", "# def get_current_positions():\n", "#     return [\n", "#         {\n", "#             \"Token\": trade.token,\n", "#             \"Symbol\": trade.symbol_name,\n", "#             \"Entry Price\": trade.entry_price,\n", "#             \"Current Price\": get_latest_price_option(trade.option_token),\n", "#             \"P/L\": get_latest_price_option(trade.option_token) - trade.entry_price,\n", "#             \"SL\": trade.sl_price,\n", "#             \"Trail SL\": trade.trailing_sl_price\n", "#         }\n", "#         for trade in current_positions.values() if not trade.exited\n", "#     ]\n", "\n", "# # Update the toggle button widgets\n", "# enable_call_trades_widget = widgets.ToggleButton(value=enable_call_trades, description='Disable Call Trades' if enable_call_trades else 'Enable Call Trades', button_style='success')\n", "# enable_put_trades_widget = widgets.ToggleButton(value=enable_put_trades, description='Disable Put Trades' if enable_put_trades else 'Enable Put Trades', button_style='danger')\n", "# enable_all_trades_widget = widgets.ToggleButton(value=enable_all_trades, description='Disable All Trades' if enable_all_trades else 'Enable All Trades', button_style='info')\n", "# force_exit_widget = widgets.ToggleButton(value=force_exit_triggered, description='Force Exit All Trades', button_style='warning')\n", "\n", "# # # Create widgets\n", "# # enable_call_trades_widget = widgets.ToggleButton(value=enable_call_trades, description='Enable Call Trades', button_style='success')\n", "# # enable_put_trades_widget = widgets.ToggleButton(value=enable_put_trades, description='Enable Put Trades', button_style='danger')\n", "# # enable_all_trades_widget = widgets.ToggleButton(value=enable_all_trades, description='Enable All Trades', button_style='info')\n", "# # force_exit_widget = widgets.ToggleButton(value=force_exit_triggered, description='Force Exit All Trades', button_style='warning')\n", "# default_sl_widget = widgets.FloatText(value=Trade.default_sl_price, description='Default SL:', style={'description_width': 'initial'})\n", "# default_trailing_sl_widget = widgets.FloatText(value=Trade.default_trailing_sl_price, description='Default Trailing SL:', style={'description_width': 'initial'})\n", "\n", "# # # Connect callbacks for default SL and Trail SL (these should already be in place)\n", "# # default_sl_widget.observe(lambda change: asyncio.create_task(update_default_sl(change['new'])), 'value')\n", "# # default_trailing_sl_widget.observe(lambda change: asyncio.create_task(update_default_trailing_sl(change['new'])), 'value')\n", "\n", "# # Output widgets\n", "# current_positions_output = widgets.Output()\n", "# trade_history_output = widgets.Output()\n", "# trade_summary_output = widgets.Output()\n", "\n", "# # Layout\n", "# dashboard = widgets.VBox([\n", "#     widgets.HBox([enable_call_trades_widget, enable_put_trades_widget, enable_all_trades_widget, force_exit_widget]),\n", "#     widgets.HBox([default_sl_widget, default_trailing_sl_widget]),\n", "#     widgets.HTML(\"<h3 style='color: #4CAF50;'>Current Positions</h3>\"),\n", "#     current_positions_output,\n", "#     widgets.HTML(\"<h3 style='color: #2196F3;'>Trade History</h3>\"),\n", "#     trade_history_output,\n", "#     widgets.HTML(\"<h3 style='color: #FF9800;'>Trade Summary</h3>\"),\n", "#     trade_summary_output\n", "# ])\n", "\n", "# # Callback functions\n", "# # def update_trade_controls(change):\n", "# #     global enable_call_trades, enable_put_trades, enable_all_trades, force_exit_triggered\n", "# #     if change['owner'] == enable_call_trades_widget:\n", "# #         enable_call_trades = change['new']\n", "# #     elif change['owner'] == enable_put_trades_widget:\n", "# #         enable_put_trades = change['new']\n", "# #     elif change['owner'] == enable_all_trades_widget:\n", "# #         enable_all_trades = change['new']\n", "# #     elif change['owner'] == force_exit_widget:\n", "# #         force_exit_triggered = change['new']\n", "# #         if change['new']:\n", "# #             asyncio.create_task(force_exit_all_trades())\n", "# def update_trade_controls(change):\n", "#     global enable_call_trades, enable_put_trades, enable_all_trades, force_exit_triggered\n", "#     if change['owner'] == enable_call_trades_widget:\n", "#         enable_call_trades = change['new']\n", "#         enable_call_trades_widget.description = 'Disable Call Trades' if enable_call_trades else 'Enable Call Trades'\n", "#     elif change['owner'] == enable_put_trades_widget:\n", "#         enable_put_trades = change['new']\n", "#         enable_put_trades_widget.description = 'Disable Put Trades' if enable_put_trades else 'Enable Put Trades'\n", "#     elif change['owner'] == enable_all_trades_widget:\n", "#         enable_all_trades = change['new']\n", "#         enable_all_trades_widget.description = 'Disable All Trades' if enable_all_trades else 'Enable All Trades'\n", "#     elif change['owner'] == force_exit_widget:\n", "#         force_exit_triggered = change['new']\n", "#         if change['new']:\n", "#             asyncio.create_task(force_exit_all_trades())\n", "\n", "\n", "# async def force_exit_all_trades():\n", "#     for trade in current_positions.values():\n", "#         if not trade.exited:\n", "#             await trade.force_exit(\"Manual Force Exit\")\n", "#     force_exit_widget.value = False\n", "#     update_dashboard()\n", "\n", "# def update_default_sl(change):\n", "#     asyncio.create_task(Trade.update_default_sl(change['new']))\n", "\n", "# def update_default_trailing_sl(change):\n", "#     asyncio.create_task(Trade.update_default_trailing_sl(change['new']))\n", "\n", "\n", "# # Connect callbacks\n", "# enable_call_trades_widget.observe(update_trade_controls, 'value')\n", "# enable_put_trades_widget.observe(update_trade_controls, 'value')\n", "# enable_all_trades_widget.observe(update_trade_controls, 'value')\n", "# force_exit_widget.observe(update_trade_controls, 'value')\n", "# default_sl_widget.observe(update_default_sl, 'value')\n", "# default_trailing_sl_widget.observe(update_default_trailing_sl, 'value')\n", "\n", "# # Function to update individual trade SL and Trail SL\n", "# # def update_trade_sl(trade_token, new_sl, new_trail_sl):\n", "# #     trade = current_positions.get(trade_token)\n", "# #     if trade:\n", "# #         asyncio.create_task(trade.update_sl(new_sl))\n", "# #         asyncio.create_task(trade.update_trailing_sl(new_trail_sl))\n", "\n", "# async def update_trade_sl(trade_token, new_sl):\n", "#     trade = current_positions.get(trade_token)\n", "#     if trade:\n", "#         await trade.update_sl(new_sl)\n", "#         update_dashboard()\n", "\n", "# async def update_trade_trail_sl(trade_token, new_trail_sl):\n", "#     trade = current_positions.get(trade_token)\n", "#     if trade:\n", "#         await trade.update_trailing_sl(new_trail_sl)\n", "#         update_dashboard()\n", "        \n", "\n", "# # Function to update the dashboard\n", "# # def update_dashboard():\n", "# #     with current_positions_output:\n", "# #         clear_output(wait=True)\n", "# #         positions = get_current_positions()\n", "# #         if positions:\n", "# #             position_widgets = []\n", "# #             for pos in positions:\n", "# #                 pnl = pos['P/L']\n", "# #                 pnl_color = 'green' if pnl > 0 else 'red' if pnl < 0 else 'black'\n", "# #                 position_info = widgets.HTML(\n", "# #                     f\"<p><strong>{pos['Symbol']}</strong> - Entry: {pos['Entry Price']:.2f}, \"\n", "# #                     f\"Current: {pos['Current Price']:.2f}, \"\n", "# #                     f\"P/L: <span style='color: {pnl_color};'>{pnl:.2f}</span></p>\"\n", "# #                 )\n", "# #                 sl_widget = widgets.FloatText(value=pos['SL'], description='SL:', style={'description_width': 'initial'})\n", "# #                 trail_sl_widget = widgets.FloatText(value=pos['Trail SL'], description='Trail SL:', style={'description_width': 'initial'})\n", "                \n", "# #                 def create_update_callback(token):\n", "# #                     def update_callback(change):\n", "# #                         update_trade_sl(token, sl_widget.value, trail_sl_widget.value)\n", "# #                     return update_callback\n", "                \n", "# #                 sl_widget.observe(create_update_callback(pos['Token']), 'value')\n", "# #                 trail_sl_widget.observe(create_update_callback(pos['Token']), 'value')\n", "                \n", "# #                 position_widgets.extend([position_info, sl_widget, trail_sl_widget, widgets.HTML(\"<hr>\")])\n", "            \n", "# #             display(widgets.VBox(position_widgets))\n", "# #         else:\n", "# #             display(HTML(\"<p>No active positions</p>\"))\n", "\n", "# def update_dashboard():\n", "#     with current_positions_output:\n", "#         clear_output(wait=True)\n", "#         positions = get_current_positions()\n", "#         if positions:\n", "#             position_widgets = []\n", "#             for pos in positions:\n", "#                 pnl = pos['P/L']\n", "#                 pnl_color = 'green' if pnl > 0 else 'red' if pnl < 0 else 'black'\n", "#                 position_info = widgets.HTML(\n", "#                     f\"<p><strong>{pos['Symbol']}</strong> - Entry: {pos['Entry Price']:.2f}, \"\n", "#                     f\"Current: {pos['Current Price']:.2f}, \"\n", "#                     f\"P/L: <span style='color: {pnl_color};'>{pnl:.2f}</span></p>\"\n", "#                 )\n", "                \n", "#                 # SL control\n", "#                 sl_widget = widgets.FloatText(\n", "#                     value=pos['SL'],\n", "#                     description='SL:',\n", "#                     style={'description_width': 'initial'},\n", "#                     layout=widgets.Layout(width='200px')\n", "#                 )\n", "                \n", "#                 # Trail SL control\n", "#                 trail_sl_widget = widgets.FloatText(\n", "#                     value=pos['Trail SL'],\n", "#                     description='Trail SL:',\n", "#                     style={'description_width': 'initial'},\n", "#                     layout=widgets.Layout(width='200px')\n", "#                 )\n", "                \n", "#                 def create_sl_update_callback(token):\n", "#                     def sl_update_callback(change):\n", "#                         asyncio.create_task(update_trade_sl(token, change['new']))\n", "#                     return sl_update_callback\n", "                \n", "#                 def create_trail_sl_update_callback(token):\n", "#                     def trail_sl_update_callback(change):\n", "#                         asyncio.create_task(update_trade_trail_sl(token, change['new']))\n", "#                     return trail_sl_update_callback\n", "                \n", "#                 sl_widget.observe(create_sl_update_callback(pos['Token']), 'value')\n", "#                 trail_sl_widget.observe(create_trail_sl_update_callback(pos['Token']), 'value')\n", "                \n", "#                 position_widgets.extend([\n", "#                     position_info, \n", "#                     widgets.HBox([sl_widget, trail_sl_widget]),\n", "#                     widgets.HTML(\"<hr>\")\n", "#                 ])\n", "            \n", "#             display(widgets.VBox(position_widgets))\n", "#         else:\n", "#             display(HTML(\"<p>No active positions</p>\"))\n", "    \n", "#     with trade_history_output:\n", "#         clear_output(wait=True)\n", "#         display(update_trade_history().style.set_properties(**{'background-color': '#E3F2FD', 'color': 'black'}))\n", "    \n", "#     with trade_summary_output:\n", "#         clear_output(wait=True)\n", "#         summary_html = f\"\"\"\n", "#         <div style='background-color: #FFF3E0; padding: 10px; border-radius: 5px;'>\n", "#             <p><strong>Total Trades:</strong> {trade_tracker.total_trades}</p>\n", "#             <p><strong>Win Trades:</strong> <span style='color: green;'>{trade_tracker.win_trades}</span></p>\n", "#             <p><strong>Loss Trades:</strong> <span style='color: red;'>{trade_tracker.loss_trades}</span></p>\n", "#             <p><strong>Zero P/L Trades:</strong> {trade_tracker.zero_pnl_trades}</p>\n", "#             <p><strong>Total Points Collected:</strong> <span style='color: green;'>{trade_tracker.total_points_collected:.2f}</span></p>\n", "#             <p><strong>Total Points Lost:</strong> <span style='color: red;'>{trade_tracker.total_points_lost:.2f}</span></p>\n", "#             <p><strong>Overall Profit/Loss:</strong> <span style='color: {\"green\" if trade_tracker.overall_profit_loss > 0 else \"red\"};'>{trade_tracker.overall_profit_loss:.2f} points</span></p>\n", "#         </div>\n", "#         \"\"\"\n", "#         display(HTML(summary_html))\n", "\n", "# # Function to initialize and display the dashboard\n", "# def init_dashboard():\n", "#     display(dashboard)\n", "#     update_dashboard()\n", "\n", "# # Call this function to start the dashboard\n", "# init_dashboard()\n", "\n", "# # Optional: Setup periodic updates (every 5 seconds)\n", "# from IPython.display import display, clear_output\n", "# import time\n", "\n", "# def periodic_update():\n", "#     while True:\n", "#         time.sleep(5)\n", "#         update_dashboard()\n", "        \n", "# # Run this in a separate cell to start periodic updates\n", "# import threading\n", "# threading.Thread(target=periodic_update, daemon=True).start()"]}, {"cell_type": "code", "execution_count": 1, "id": "cf536f51", "metadata": {}, "outputs": [], "source": ["###### if one indicator only use and need retracement monitoring for significant candle - claude code- working- just need to enable it in execute trade logic\n", "# async def monitor_retracement(token, position_type, entry_candle_height, last_close, initial_direction):\n", "#     retracement_target = entry_candle_height * 0.5\n", "#     entry_price = last_close\n", "\n", "#     while True:\n", "#         current_price = get_latest_price_index(token)\n", "        \n", "#         if current_price is None:\n", "#             await asyncio.sleep(1)\n", "#             continue\n", "\n", "#         price_change = current_price - entry_price\n", "\n", "#         # Check the current direction\n", "#         df = completed_candles_dfs[token]\n", "#         current_direction = df['su_direction'].iloc[-1]\n", "\n", "#         # Check if the direction has changed\n", "#         if current_direction != initial_direction:\n", "#             logger.warning(f\"Direction changed from {initial_direction} to {current_direction}. Exiting retracement monitoring.\")\n", "#             return  # Exit the monitoring function\n", "\n", "#         # Check if the retracement condition is met based on the position type\n", "#         retracement_condition = (\n", "#             (position_type == \"call_buy\" and price_change < 0 and abs(price_change) >= retracement_target) or\n", "#             (position_type == \"put_buy\" and price_change > 0 and abs(price_change) >= retracement_target)\n", "#         )\n", "\n", "#         if retracement_condition:\n", "#             # Check if no other trade is active\n", "#             async with position_lock:\n", "#                 active_trade = next((trade for trade in current_positions.values() if not trade.exited), None)\n", "#                 if not active_trade:\n", "#                     try:\n", "#                         logger.warning(\"entering trade after retracement.\")\n", "#                         await enter_trade(token, position_type, \"Retracement Entry\")\n", "#                     except Exception as e:\n", "#                         print(f\"Error entering trade after retracement for {token}: {e}\")\n", "#                     return  # Exit the monitoring function\n", "#                 else:\n", "#                     logger.warning(\"Retracement condition met, but a trade is already active. Exiting monitoring.\")\n", "#                     return  # Exit the monitoring function if a trade is already active\n", "\n", "#         await asyncio.sleep(1)  # Wait for 1 second before checking again"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.2"}}, "nbformat": 4, "nbformat_minor": 5}