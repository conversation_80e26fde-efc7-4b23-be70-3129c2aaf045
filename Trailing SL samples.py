## perfectly working
async def _check_trailing_sl(self, trade_key: str, current_price: float):
        trade = self.active_trades.get(trade_key)
        if not trade or trade.exit_price is not None:
            return

        # Get tick data
        option_ticks = self.option_manager.option_ticks.get(
            int(trade.index_token), {}
        ).get(trade.option_token, deque())
        
        # Ensure we have enough data points (increased to 20 for longer EMA)
        if len(option_ticks) < 20:
            return
        
        # Update highest price seen during trade
        if current_price > trade.highest_price:
            trade.highest_price = current_price
        
        # Parse timeframe correctly - handle formats like "15s", "1m", etc.
        tf_seconds = 60  # Default fallback
        try:
            if trade.timeframe.endswith('s'):
                tf_seconds = int(trade.timeframe[:-1])
            elif trade.timeframe.endswith('m'):
                tf_seconds = int(trade.timeframe[:-1]) * 60
            elif trade.timeframe.endswith('h'):
                tf_seconds = int(trade.timeframe[:-1]) * 3600
            else:
                tf_seconds = int(trade.timeframe)
        except (ValueError, AttributeError):
            self.logger.warning(f"Couldn't parse timeframe '{trade.timeframe}', using default 60s")
        
        # Initialize EMA state for this trade if not present (20-period EMA)
        if not hasattr(trade, 'ema_value'):
            trade.ema_value = current_price  # Start with current price
            trade.ema_alpha = 2 / (20 + 1)  # 20-period EMA (less responsive, more smoothing)
            trade.last_tick_count = 0
        
        # Update EMA incrementally with new ticks
        current_tick_count = len(option_ticks)
        if current_tick_count > trade.last_tick_count:
            new_ticks = list(option_ticks)[trade.last_tick_count - current_tick_count:]
            for tick in new_ticks:
                price = tick['ltp']
                trade.ema_value = trade.ema_alpha * price + (1 - trade.ema_alpha) * trade.ema_value
            trade.last_tick_count = current_tick_count
        
        # Get recent ticks for volatility and range calculation
        ticks_to_use = max(20, min(50, tf_seconds // 2))  # Increased minimum to 20
        recent_ticks = list(option_ticks)[-ticks_to_use:]
        recent_prices = [tick['ltp'] for tick in recent_ticks]
        
        # Calculate EMA-based price ranges (20-period EMA for smoother ranges)
        if len(recent_prices) >= 20:
            ema_temp = recent_prices[0]
            ema_alpha = 2 / (20 + 1)  # 20-period EMA
            smoothed_prices = [ema_temp]
            for price in recent_prices[1:]:
                ema_temp = ema_alpha * price + (1 - ema_alpha) * ema_temp
                smoothed_prices.append(ema_temp)
            
            if len(smoothed_prices) > 1:
                price_ranges = [abs(smoothed_prices[i] - smoothed_prices[i-1]) 
                            for i in range(1, len(smoothed_prices))]
                avg_range = sum(price_ranges) / len(price_ranges) if price_ranges else 0
            else:
                avg_range = 0
        else:
            avg_range = 0
        
        # Calculate volatility for dynamic adjustment
        price_std = statistics.stdev(recent_prices) if len(recent_prices) > 1 else 0
        
        # Base trail distance on average range and volatility (wider to reduce noise)
        multiplier = max(4, min(10, 4 + (price_std / avg_range) if avg_range > 0 else 4))  # Increased range
        trail_distance = avg_range * multiplier
        
        # Dynamic minimum distance based on volatility (slightly wider)
        min_distance = max(current_price * 0.005, price_std * 2.0)  # 0.5% or 2x std dev
        trail_distance = max(min_distance, trail_distance)
        
        # Set initial trigger price if not set
        if not hasattr(trade, 'trail_sl_trigger') or trade.trail_sl_trigger is None:
            trade.trail_sl_trigger = trade.entry_price - trail_distance
            trade._record_event("Initial Trigger Set", details={
                "trigger": trade.trail_sl_trigger,
                "entry_price": trade.entry_price,
                "trail_distance": trail_distance,
                "ema_value": trade.ema_value
            })
            self.logger.info(f"Initial trailing trigger for {trade_key} set: {trade.trail_sl_trigger:.2f}")
        
        # Calculate new stop loss trigger
        new_trigger = trade.highest_price - trail_distance
        
        # Update trigger only if tighter
        if new_trigger > trade.trail_sl_trigger:
            old_trigger = trade.trail_sl_trigger
            trade.trail_sl_trigger = new_trigger
            trade._record_event("Trigger Updated", details={
                "new_trigger": new_trigger,
                "old_trigger": old_trigger,
                "trail_distance": trail_distance,
                "avg_range": avg_range,
                "multiplier": multiplier,
                "timeframe_seconds": tf_seconds,
                "highest_price": trade.highest_price,
                "ema_value": trade.ema_value
            })
            self.logger.info(
                f"Trailing trigger for {trade_key} updated: {new_trigger:.2f} "
                f"(Dist: {trail_distance:.2f}, Range: {avg_range:.4f}, TF: {tf_seconds}s)"
            )
            asyncio.create_task(broadcast_trade_updates())  # Using your existing function
        
        # Check stop with confirmation (increased to 5 for less noise)
        if current_price <= trade.trail_sl_trigger:
            if not hasattr(trade, 'stop_confirm_count'):
                trade.stop_confirm_count = 0
            trade.stop_confirm_count += 1
            if trade.stop_confirm_count >= 5:  # 5 consecutive confirmations
                trade.exit_reason = "Trailing Stop Hit"
                await self.close_trade(
                    trade.index_token, trade.timeframe, trade.option_symbol,
                    trade.option_token, trade.order_id, current_price,
                    datetime.now().isoformat()
                )
                self.logger.info(f"Trade {trade_key} exited at {current_price:.2f} - Trailing Stop Hit")
                asyncio.create_task(broadcast_trade_updates())  # Using your existing function
            else:
                self.logger.debug(f"Stop hit {trade.stop_confirm_count}/5 for {trade_key}")
        else:
            trade.stop_confirm_count = 0  # Reset if price recovers
            
            
            
            
       #  working but it takes trigger from default SL and progress from there     
async def _check_trailing_sl(self, trade_key: str, current_price: float):
        trade = self.active_trades.get(trade_key)
        if not trade or trade.exit_price is not None:
            return

        # Get tick data (ltp is option premium)
        option_ticks = self.option_manager.option_ticks.get(
            int(trade.index_token), {}
        ).get(trade.option_token, deque())
        
        if len(option_ticks) < 10:
            return
        
        tf_seconds = self._parse_timeframe_to_seconds(trade.timeframe)
        
        # Initialize trade metrics
        if not hasattr(trade, 'trade_metrics'):
            trade.trade_metrics = {
                'entry_time': datetime.now(),
                'phase': 'evaluation',
                'volatility_short': 0,
                'volatility_long': 0,
                'avg_range_short': 0,
                'avg_range_long': 0,
                'confirm_count': 0,
                'profit_milestones': [0.1, 0.2, 0.5, 1.0, 2.0],
                'milestone_reached': 0,
                'market_condition': 'unknown',
                'price_history': deque(maxlen=max(150, tf_seconds // 2)),
                'last_tick_count': 0,
                'evaluation_period': max(15, min(tf_seconds // 4, 60)),
                'trailing_ratio': 2.0,
                'stop_moved_count': 0,
                'last_price_peak': trade.entry_price,
                'price_momentum': 0
            }
            trade._record_event("Trailing SL Initialized", details={
                "entry_price": trade.entry_price,
                "timeframe": trade.timeframe,
                "default_sl": trade.default_sl,
                "initial_trail_sl_trigger": trade.trail_sl_trigger,
                "action": trade.action
            })
            self.logger.info(f"Trailing SL initialized for {trade_key} with trigger: {trade.trail_sl_trigger}")
        
        metrics = trade.trade_metrics
        current_tick_count = len(option_ticks)
        
        if current_tick_count > metrics['last_tick_count']:
            new_ticks = list(option_ticks)[metrics['last_tick_count'] - current_tick_count:]
            for tick in new_ticks:
                price = tick['ltp']
                metrics['price_history'].append(price)
            metrics['last_tick_count'] = current_tick_count
        
        # Profit calculation (premium-based for call/put buys)
        current_profit_pct = (current_price / trade.entry_price - 1) * 100
        
        # Update metrics
        elapsed_seconds = (datetime.now() - metrics['entry_time']).total_seconds()
        self._update_trade_phase(trade, current_profit_pct, elapsed_seconds, tf_seconds)
        self._calculate_market_metrics(trade, option_ticks, tf_seconds)
        self._update_price_momentum(trade, current_price)
        
        # Calculate adaptive trailing distance
        trail_distance = self._calculate_adaptive_trail_distance(trade, current_price, tf_seconds)
        
        # Set initial trigger from default_sl if not set
        if trade.trail_sl_trigger is None and trade.default_sl is not None:
            trade.trail_sl_trigger = trade.entry_price - trade.default_sl
            trade.trail_sl = trade.default_sl
            trade._record_event("Initial Trigger Set", details={
                "trail_sl_trigger": trade.trail_sl_trigger,
                "default_sl": trade.default_sl,
                "action": trade.action
            })
            self.logger.info(f"Initial trail_sl_trigger for {trade_key} set to {trade.trail_sl_trigger:.2f} from default_sl")
        
        # New trigger trails upward as premium rises
        new_trigger = trade.highest_price - trail_distance
        
        # Adaptive update threshold
        improvement_threshold = self._get_adaptive_improvement_threshold(trade, current_price)
        significant_improvement = (
            ((new_trigger - trade.trail_sl_trigger) / trade.trail_sl_trigger) * 100 >= improvement_threshold
        )
        
        # Price action condition
        should_update_stop = (
            abs(metrics['price_momentum']) > metrics['volatility_short'] * 1.5 or
            current_profit_pct > 50 or
            (metrics['phase'] == 'evaluation' and current_profit_pct <= 0)
        )
        
        # Update trigger if it tightens
        if new_trigger > trade.trail_sl_trigger and (should_update_stop or significant_improvement):
            old_trigger = trade.trail_sl_trigger
            trade.trail_sl_trigger = new_trigger
            trade.trail_sl = trade.highest_price - trade.trail_sl_trigger
            metrics['stop_moved_count'] += 1
            if current_price > metrics['last_price_peak']:
                metrics['last_price_peak'] = current_price
            
            trade._record_event("Trigger Updated", details={
                "new_trigger": trade.trail_sl_trigger,
                "old_trigger": old_trigger,
                "trail_sl": trade.trail_sl,
                "phase": metrics['phase'],
                "profit_pct": current_profit_pct,
                "momentum": metrics['price_momentum'],
                "action": trade.action
            })
            self.logger.info(
                f"Trail_sl_trigger for {trade_key} updated to {trade.trail_sl_trigger:.2f} "
                f"(Phase: {metrics['phase']}, Profit: {current_profit_pct:.1f}%, "
                f"Trail Distance: {trade.trail_sl:.2f}, Action: {trade.action})"
            )
            asyncio.create_task(broadcast_trade_updates())
        
        # Check stop hit (premium drops below trigger)
        required_confirms = self._get_required_confirmations(trade, tf_seconds)
        price_discount_pct = ((trade.highest_price - current_price) / trade.highest_price) * 100
        
        if current_price <= trade.trail_sl_trigger:
            if price_discount_pct > 8 and metrics['phase'] in ['established', 'runner']:
                metrics['confirm_count'] = required_confirms
            metrics['confirm_count'] += 1
            
            if metrics['confirm_count'] >= required_confirms:
                trade.exit_reason = f"Trailing SL Hit ({metrics['phase']} phase)"
                await self.close_trade(
                    trade.index_token, trade.timeframe, trade.option_symbol,
                    trade.option_token, trade.order_id, current_price,
                    datetime.now().isoformat()
                )
                self.logger.info(
                    f"Trade {trade_key} exited at {current_price:.2f} - "
                    f"Trailing SL Hit ({metrics['phase']} phase, {current_profit_pct:.1f}% profit, Action: {trade.action})"
                )
                asyncio.create_task(broadcast_trade_updates())
            else:
                self.logger.debug(
                    f"Trail SL hit {metrics['confirm_count']}/{required_confirms} "
                    f"for {trade_key} ({metrics['phase']} phase)"
                )
        else:
            metrics['confirm_count'] = max(0, metrics['confirm_count'] - 2)

    # Supporting Functions
    def _parse_timeframe_to_seconds(self, timeframe):
        """Convert timeframe string to seconds"""
        try:
            if isinstance(timeframe, (int, float)):
                return int(timeframe)
            if timeframe.endswith('s'):
                return int(timeframe[:-1])
            elif timeframe.endswith('m'):
                return int(timeframe[:-1]) * 60
            elif timeframe.endswith('h'):
                return int(timeframe[:-1]) * 3600
            else:
                return int(timeframe)
        except (ValueError, AttributeError):
            self.logger.warning(f"Couldn't parse timeframe '{timeframe}', using default 60s")
            return 60

    def _update_trade_phase(self, trade, current_profit_pct, elapsed_seconds, tf_seconds):
        """Update trade phase based on profit and time"""
        metrics = trade.trade_metrics
        
        for i, milestone in enumerate(metrics['profit_milestones']):
            if current_profit_pct >= milestone * 100 and i > metrics['milestone_reached']:
                metrics['milestone_reached'] = i
                trade._record_event("Profit Milestone", details={
                    "milestone": f"{milestone * 100}%",
                    "current_profit": f"{current_profit_pct:.1f}%",
                    "elapsed_time": f"{elapsed_seconds:.0f}s"
                })
        
        if metrics['phase'] == 'evaluation' and elapsed_seconds < metrics['evaluation_period']:
            return
        
        if current_profit_pct <= -5 and elapsed_seconds < tf_seconds * 3:
            new_phase = 'early'
        elif current_profit_pct >= 80 or metrics['milestone_reached'] >= 3:
            new_phase = 'runner'
        elif current_profit_pct > 15 or elapsed_seconds > tf_seconds * 5:
            new_phase = 'established'
        elif metrics['phase'] == 'evaluation':
            new_phase = 'early'
        else:
            new_phase = metrics['phase']
        
        if new_phase != metrics['phase']:
            trade._record_event("Phase Change", details={
                "old_phase": metrics['phase'],
                "new_phase": new_phase,
                "profit_pct": f"{current_profit_pct:.1f}%",
                "elapsed_time": f"{elapsed_seconds:.0f}s"
            })
            metrics['phase'] = new_phase

    def _calculate_market_metrics(self, trade, option_ticks, tf_seconds):
        """Calculate market metrics without linregress"""
        metrics = trade.trade_metrics
        
        short_window = max(10, min(40, tf_seconds // 5))
        long_window = max(40, min(200, tf_seconds))
        
        recent_ticks = list(option_ticks)
        if len(recent_ticks) < short_window:
            return
        
        recent_prices = [tick['ltp'] for tick in recent_ticks]
        short_prices = recent_prices[-short_window:]
        long_prices = recent_prices[-min(len(recent_prices), long_window):]
        
        short_ranges = [abs(short_prices[i] - short_prices[i-1]) for i in range(1, len(short_prices))]
        long_ranges = [abs(long_prices[i] - long_prices[i-1]) for i in range(1, len(long_prices))]
        
        metrics['avg_range_short'] = sum(short_ranges) / len(short_ranges) if short_ranges else 0
        metrics['avg_range_long'] = sum(long_ranges) / len(long_ranges) if long_ranges else 0
        
        metrics['volatility_short'] = statistics.stdev(short_prices) if len(short_prices) > 1 else 0
        metrics['volatility_long'] = statistics.stdev(long_prices) if len(long_prices) > 1 else 0
        
        if len(short_prices) > 10 and len(long_prices) > 20:
            short_direction = sum(1 if short_prices[i] > short_prices[i-1] else -1 for i in range(1, len(short_prices)))
            trend_strength = abs(short_direction) / (len(short_prices) - 1)
            
            rel_volatility = metrics['volatility_short'] / metrics['volatility_long'] if metrics['volatility_long'] else 1
            
            if trend_strength > 0.7:
                metrics['market_condition'] = 'trending'
            elif rel_volatility > 1.3:
                metrics['market_condition'] = 'volatile'
            else:
                metrics['market_condition'] = 'ranging'

    def _update_price_momentum(self, trade, current_price):
        """Calculate momentum without linregress"""
        metrics = trade.trade_metrics
        price_history = list(metrics['price_history'])
        
        if len(price_history) < 5:
            metrics['price_momentum'] = 0
            return
        
        recent_prices = price_history[-5:]
        start_price = recent_prices[0]
        end_price = recent_prices[-1]
        
        if start_price != 0:
            metrics['price_momentum'] = (end_price - start_price) / start_price
        else:
            metrics['price_momentum'] = 0

    def _get_adaptive_improvement_threshold(self, trade, current_price):
        """Adaptive threshold for trigger updates"""
        metrics = trade.trade_metrics
        current_profit_pct = (current_price / trade.entry_price - 1) * 100
        
        base_threshold = max(0.3, metrics['volatility_short'] / current_price * 100)
        profit_factor = max(0.5, 1.0 - (current_profit_pct / 200))
        market_factor = 1.5 if metrics['market_condition'] == 'volatile' else 0.8 if metrics['market_condition'] == 'trending' else 1.0
        return base_threshold * profit_factor * market_factor

    def _calculate_adaptive_trail_distance(self, trade, current_price, tf_seconds):
        """Calculate adaptive trail distance"""
        metrics = trade.trade_metrics
        current_profit_pct = (current_price / trade.entry_price - 1) * 100
        
        base_multiplier = {
            'evaluation': 6.0,
            'early': 4.0,
            'established': 3.5,
            'runner': 3.0
        }.get(metrics['phase'], 3.0)
        
        market_factor = 1.8 if metrics['market_condition'] == 'volatile' else 1.4 if metrics['market_condition'] == 'ranging' else 1.0
        momentum_factor = max(0.8, min(1.5, 1.0 + abs(metrics['price_momentum']) * 10))
        tf_factor = math.pow(tf_seconds / 60, 1/3) * 1.2
        
        metrics['trailing_ratio'] = base_multiplier * market_factor * momentum_factor * tf_factor
        base_distance = max(metrics['volatility_short'] * 2.5, metrics['avg_range_short'] * 3.5)
        trail_distance = base_distance * metrics['trailing_ratio']
        
        min_distance = max(current_price * 0.005 * tf_factor, metrics['volatility_short'] * 2.0)
        if current_profit_pct > 100:
            tightening_factor = 1.0 - min(0.4, (current_profit_pct - 100) / 250)
            trail_distance *= tightening_factor
        
        return max(min_distance, trail_distance) if trade.default_sl is None else max(trade.default_sl, min_distance, trail_distance)

    def _get_required_confirmations(self, trade, tf_seconds):
        """Determine required confirmations"""
        metrics = trade.trade_metrics
        
        base_confirms = {
            'evaluation': 6,
            'early': 5,
            'established': 4,
            'runner': 3
        }.get(metrics['phase'], 3)
        
        tf_factor = max(0.5, min(1.5, math.pow(60 / tf_seconds, 1/3))) if tf_seconds > 0 else 1
        market_factor = 1.4 if metrics['market_condition'] == 'volatile' else 1.2 if metrics['market_condition'] == 'ranging' else 1.0
        
        return max(2, round(base_confirms * tf_factor * market_factor))