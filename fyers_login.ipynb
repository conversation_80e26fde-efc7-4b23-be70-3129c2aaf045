{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from fyers_apiv3 import fyersModel\n", "import webbrowser\n", "\n", "\n", "redirect_uri= \"https://127.0.0.1:5000\"  ## redircet_uri you entered while creating APP.\n", "client_id = \"L0PM7PGZKI-100\"                       ## Client_id here refers to APP_ID of the created app\n", "secret_key = \"6UK6REV9SD\"                          ## app_secret key which you got after creating the app \n", "grant_type = \"authorization_code\"                  ## The grant_type always has to be \"authorization_code\"\n", "response_type = \"code\"                             ## The response_type always has to be \"code\"\n", "state = \"sample\"                                   ##  The state field here acts as a session manager. you will be sent with the state field after successfull generation of auth_code \n", "\n", "\n", "### Connect to the sessionModel object here with the required input parameters\n", "appSession = fyersModel.SessionModel(client_id = client_id, redirect_uri = redirect_uri,response_type=response_type,state=state,secret_key=secret_key,grant_type=grant_type)\n", "generateTokenUrl = appSession.generate_authcode()\n", "webbrowser.open(generateTokenUrl,new=1)\n", "\n", "###################################################################\n", "\n", "### After succesfull login the user can copy the generated auth_code over here and make the request to generate the accessToken \n", "auth_code = \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJhcGkubG9naW4uZnllcnMuaW4iLCJpYXQiOjE3NDE3ODMxOTIsImV4cCI6MTc0MTgxMzE5MiwibmJmIjoxNzQxNzgyNTkyLCJhdWQiOiJbXCJ4OjBcIiwgXCJ4OjFcIiwgXCJ4OjJcIiwgXCJkOjFcIiwgXCJkOjJcIiwgXCJ4OjFcIiwgXCJ4OjBcIiwgXCJ4OjFcIiwgXCJ4OjBcIiwgXCJ4OjFcIiwgXCJ4OjBcIiwgXCJ4OjFcIiwgXCJ4OjBcIiwgXCJ4OjFcIiwgXCJ4OjBcIiwgXCJ4OjFcIiwgXCJ4OjBcIiwgXCJ4OjFcIiwgXCJ4OjBcIl0iLCJzdWIiOiJhdXRoX2NvZGUiLCJkaXNwbGF5X25hbWUiOiJZRDE1NTY5Iiwib21zIjoiSzEiLCJoc21fa2V5IjoiNTc2YzY2MmQ4ZGRlYTZkNGVkYzllZmM4MjRlMGI2YTExNDYwOWVjMGQ5YTEwZTcwNDE2MjI2ZTkiLCJpc0RkcGlFbmFibGVkIjoiTiIsImlzTXRmRW5hYmxlZCI6Ik4iLCJub25jZSI6IiIsImFwcF9pZCI6IkwwUE03UEdaS0kiLCJ1dWlkIjoiNTFkMmNlYzdjM2VlNDAzMWFhNWRlYjE4YTFhNTVjMjQiLCJpcEFkZHIiOiIwLjAuMC4wIiwic2NvcGUiOiIifQ.u_T26S_C4OMfX5kMMaWTM9dNV4Ul-ckUUmXXB1tC3u0\"\n", "appSession.set_token(auth_code)\n", "response = appSession.generate_token()\n", "\n", "## There can be two cases over here you can successfully get the acccessToken over the request or you might get some error over here. so to avoid that have this in try except block\n", "try: \n", "    access_token = response[\"access_token\"]\n", "except Exception as e:\n", "    print(e,response)  ## This will help you in debugging then and there itself like what was the error and also you would be able to see the value you got in response variable. instead of getting key_error for unsuccessfull response.\n", "    \n", "    \n", "fyers_client = fyersModel.FyersModel(token=access_token,is_async=False,client_id=client_id,log_path=\"\")\n", "\n", "data = {\"symbol\":\"NSE:SBIN-EQ\",\"ohlcv_flag\":\"1\"}\n", "print(fyers_client.depth(data))\n", "\n", "\n", "factor2 = pyotp.TOTP(\"SPXZAJLIHCRMKD653OAO3HQB53QNXT5C\").now()\n", "\n", "factor2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pyotp\n", "\n", "factor2 = pyotp.TOTP(\"SPXZAJLIHCRMKD653OAO3HQB53QNXT5C\").now()\n", "\n", "factor2"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Server error: [Errno 98] Address already in use\n", "\n", "Login successful! Fyers client created.\n", "Access Token: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJhcGkuZnllcnMuaW4iLCJpYXQiOjE3NDI0NjA0NjUsImV4cCI6MTc0MjUxNzA0NSwibmJmIjoxNzQyNDYwNDY1LCJhdWQiOlsieDowIiwieDoxIiwieDoyIiwiZDoxIiwiZDoyIiwieDoxIiwieDowIiwieDoxIiwieDowIiwieDoxIiwieDowIiwieDoxIiwieDowIiwieDoxIiwieDowIiwieDoxIiwieDowIiwieDoxIiwieDowIl0sInN1YiI6ImFjY2Vzc190b2tlbiIsImF0X2hhc2giOiJnQUFBQUFCbjI5WXgyRmRNYVlGWExDLVBMYmJUeEJwdm5aZWtmWjI4NzZJa2x4dGtxTWN5eUFiQUIxcG5pR0VEbmFlU3FMRVVkSjQ1d2hJR3J0MnBCdGVfQzhxVWFud29udEhVZ1V5UzdWLVdGVGNGZWRCUWlfND0iLCJkaXNwbGF5X25hbWUiOiJERUVQIEpZT1RJIEJPUkEiLCJvbXMiOiJLMSIsImhzbV9rZXkiOiI1NzZjNjYyZDhkZGVhNmQ0ZWRjOWVmYzgyNGUwYjZhMTE0NjA5ZWMwZDlhMTBlNzA0MTYyMjZlOSIsImlzRGRwaUVuYWJsZWQiOiJOIiwiaXNNdGZFbmFibGVkIjoiTiIsImZ5X2lkIjoiWUQxNTU2OSIsImFwcFR5cGUiOjEwMCwicG9hX2ZsYWciOiJOIn0.DJpKXVMhZqkoRCojLCVlFUyiLO_wbZYjO86jeT8F8kw\n", "\n", "Example profile request:\n", "{'s': 'ok', 'code': 200, 'message': '', 'data': {'fy_id': 'YD15569', 'name': 'DEEP JYOTI BORA', 'image': None, 'display_name': None, 'pin_change_date': '10-03-2025 14:06:49', 'email_id': '<EMAIL>', 'pwd_change_date': None, 'PAN': '---------', 'mobile_number': '9884445467', 'totp': True, 'pwd_to_expire': 90, 'ddpi_enabled': False, 'mtf_enabled': False}}\n"]}], "source": ["import http.server\n", "import socketserver\n", "import threading\n", "import webbrowser\n", "from urllib.parse import urlparse, parse_qs\n", "from fyers_apiv3 import fyersModel\n", "import time\n", "\n", "global access_token\n", "\n", "# Global variables to store authentication details\n", "auth_code = None\n", "error = None\n", "\n", "class CallbackHandler(http.server.SimpleHTTPRequestHandler):\n", "    expected_state = None  # Will be set before server starts\n", "\n", "    def do_GET(self):\n", "        global auth_code, error\n", "        parsed = urlparse(self.path)\n", "        if parsed.path == '/':\n", "            query_params = parse_qs(parsed.query)\n", "            received_state = query_params.get('state', [None])[0]\n", "\n", "            # Validate state parameter\n", "            if received_state != self.expected_state:\n", "                error = f\"State mismatch. Expected: {self.expected_state}, Received: {received_state}\"\n", "                self.send_response(400)\n", "                self.end_headers()\n", "                self.wfile.write(error.encode())\n", "                self.server.shutdown()\n", "                return\n", "\n", "            if 'auth_code' in query_params:\n", "                auth_code = query_params['auth_code'][0]\n", "                self.send_response(200)\n", "                self.end_headers()\n", "                self.wfile.write(b\"\"\"\n", "                <html>\n", "                <head><title>Authentication Successful</title></head>\n", "                <body>\n", "                <h1>Authentication successful.</h1>\n", "                <p>You can close this window now.</p>\n", "                <script>window.close();</script>\n", "                </body>\n", "                </html>\n", "                \"\"\")\n", "                self.server.shutdown()\n", "            else:\n", "                error = query_params.get('error', ['Unknown error'])[0]\n", "                self.send_response(400)\n", "                self.end_headers()\n", "                self.wfile.write(f\"Error: {error}\".encode())\n", "                self.server.shutdown()\n", "        else:\n", "            self.send_response(404)\n", "            self.end_headers()\n", "            self.wfile.write(b\"Not found\")\n", "\n", "def run_server():\n", "    try:\n", "        with socketserver.TCPServer(('127.0.0.1', 5000), CallbackHandler) as httpd:\n", "            print(\"Server started on port 5000. Waiting for authentication redirect...\")\n", "            httpd.serve_forever()\n", "    except Exception as e:\n", "        global error\n", "        error = f\"Server error: {e}\"\n", "        print(error)\n", "\n", "def main():\n", "    global auth_code, error\n", "    global access_token\n", "\n", "    # Configuration parameters\n", "    client_id = \"L0PM7PGZKI-100\"\n", "    secret_key = \"6UK6REV9SD\"\n", "    redirect_uri = \"http://127.0.0.1:5000\"  # Must match your app's redirect URI\n", "    state = \"sample\"\n", "    grant_type = \"authorization_code\"\n", "    response_type = \"code\"\n", "\n", "    # Set expected state for CSRF protection\n", "    CallbackHandler.expected_state = state\n", "\n", "    # Create session model\n", "    app_session = fyersModel.SessionModel(\n", "        client_id=client_id,\n", "        redirect_uri=redirect_uri,\n", "        response_type=response_type,\n", "        state=state,\n", "        secret_key=secret_key,\n", "        grant_type=grant_type\n", "    )\n", "\n", "    # Generate authentication URL\n", "    auth_url = app_session.generate_authcode()\n", "\n", "    # Start server in a separate thread\n", "    server_thread = threading.Thread(target=run_server)\n", "    server_thread.start()\n", "\n", "    # Open authentication URL in browser\n", "    webbrowser.open(auth_url, new=1)\n", "    time.sleep(2)  # allow user to see login page.\n", "\n", "    # Wait for server to complete (with timeout)\n", "    start_time = time.time()\n", "    while time.time() - start_time < 5:\n", "        if auth_code:\n", "            break  # Break if auth_code is received\n", "        time.sleep(0.1)  # Check every 0.1 seconds\n", "\n", "    time.sleep(0.5) #allow server to fully shutdown.\n", "\n", "    if not auth_code:\n", "        error = \"Timed out waiting for authentication.\"\n", "        server_thread.join(timeout=1)  # Additional grace period\n", "\n", "    # Process authentication results\n", "    if auth_code:\n", "        try:\n", "            app_session.set_token(auth_code)\n", "            token_response = app_session.generate_token()\n", "\n", "            if 'access_token' in token_response:\n", "                access_token = token_response['access_token']\n", "                # Create Fyers API client\n", "                fyers = fyersModel.FyersModel(\n", "                    token=access_token,\n", "                    is_async=False,\n", "                    client_id=client_id,\n", "                    log_path=\"\"\n", "                )\n", "                print(\"\\nLogin successful! Fyers client created.\")\n", "                print(\"Access Token:\", access_token)\n", "                return fyers\n", "            else:\n", "                error = f\"Token generation failed: {token_response}\"\n", "        except Exception as e:\n", "            error = f\"Error generating access token: {str(e)}\"\n", "            if 'token_response' in locals():\n", "                error += f\"\\nResponse: {token_response}\"\n", "\n", "    if error:\n", "        print(f\"\\nError occurred: {error}\")\n", "        return None\n", "\n", "if __name__ == \"__main__\":\n", "    fyers_client = main()\n", "    if fyers_client:\n", "        # Use the fyers_client for API calls\n", "        print(\"\\nExample profile request:\")\n", "        print(fyers_client.get_profile())"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJhcGkuZnllcnMuaW4iLCJpYXQiOjE3NDI0NjA0NjUsImV4cCI6MTc0MjUxNzA0NSwibmJmIjoxNzQyNDYwNDY1LCJhdWQiOlsieDowIiwieDoxIiwieDoyIiwiZDoxIiwiZDoyIiwieDoxIiwieDowIiwieDoxIiwieDowIiwieDoxIiwieDowIiwieDoxIiwieDowIiwieDoxIiwieDowIiwieDoxIiwieDowIiwieDoxIiwieDowIl0sInN1YiI6ImFjY2Vzc190b2tlbiIsImF0X2hhc2giOiJnQUFBQUFCbjI5WXgyRmRNYVlGWExDLVBMYmJUeEJwdm5aZWtmWjI4NzZJa2x4dGtxTWN5eUFiQUIxcG5pR0VEbmFlU3FMRVVkSjQ1d2hJR3J0MnBCdGVfQzhxVWFud29udEhVZ1V5UzdWLVdGVGNGZWRCUWlfND0iLCJkaXNwbGF5X25hbWUiOiJERUVQIEpZT1RJIEJPUkEiLCJvbXMiOiJLMSIsImhzbV9rZXkiOiI1NzZjNjYyZDhkZGVhNmQ0ZWRjOWVmYzgyNGUwYjZhMTE0NjA5ZWMwZDlhMTBlNzA0MTYyMjZlOSIsImlzRGRwaUVuYWJsZWQiOiJOIiwiaXNNdGZFbmFibGVkIjoiTiIsImZ5X2lkIjoiWUQxNTU2OSIsImFwcFR5cGUiOjEwMCwicG9hX2ZsYWciOiJOIn0.DJpKXVMhZqkoRCojLCVlFUyiLO_wbZYjO86jeT8F8kw'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["access_token"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["access_token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJhcGkuZnllcnMuaW4iLCJpYXQiOjE3NDE5MTI2NDYsImV4cCI6MTc0MTk5ODYyNiwibmJmIjoxNzQxOTEyNjQ2LCJhdWQiOlsieDowIiwieDoxIiwieDoyIiwiZDoxIiwiZDoyIiwieDoxIiwieDowIiwieDoxIiwieDowIiwieDoxIiwieDowIiwieDoxIiwieDowIiwieDoxIiwieDowIiwieDoxIiwieDowIiwieDoxIiwieDowIl0sInN1YiI6ImFjY2Vzc190b2tlbiIsImF0X2hhc2giOiJnQUFBQUFCbjAzcEdqazd3WDl0TjZuXzhWbjhqYjNackU0N0E5cl9lLWwybTRtbG5vTlRueDRtVndneUJQNERsZDJ2T3J4MGpJXzh5OWZIZHhuU1NDM2tSMXJrTUE2cEdGR0lfVWVWWlBZUmdYRkVCQ21aSFluMD0iLCJkaXNwbGF5X25hbWUiOiJERUVQIEpZT1RJIEJPUkEiLCJvbXMiOiJLMSIsImhzbV9rZXkiOiI1NzZjNjYyZDhkZGVhNmQ0ZWRjOWVmYzgyNGUwYjZhMTE0NjA5ZWMwZDlhMTBlNzA0MTYyMjZlOSIsImlzRGRwaUVuYWJsZWQiOiJOIiwiaXNNdGZFbmFibGVkIjoiTiIsImZ5X2lkIjoiWUQxNTU2OSIsImFwcFR5cGUiOjEwMCwicG9hX2ZsYWciOiJOIn0.DED6XBTw8Tt7h2mYoU4MT5EOkf0bTHnuVWiKrdBIRKQ'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from fyers_apiv3.FyersWebsocket import data_ws\n", "\n", "\n", "def onmessage(message):\n", "    \"\"\"\n", "    Callback function to handle incoming messages from the FyersDataSocket WebSocket.\n", "\n", "    Parameters:\n", "        message (dict): The received message from the WebSocket.\n", "\n", "    \"\"\"\n", "    print(\"Response:\", message)\n", "\n", "\n", "def onerror(message):\n", "    \"\"\"\n", "    Callback function to handle WebSocket errors.\n", "\n", "    Parameters:\n", "        message (dict): The error message received from the WebSocket.\n", "\n", "\n", "    \"\"\"\n", "    print(\"Error:\", message)\n", "\n", "\n", "def onclose(message):\n", "    \"\"\"\n", "    Callback function to handle WebSocket connection close events.\n", "    \"\"\"\n", "    print(\"Connection closed:\", message)\n", "\n", "\n", "def onopen():\n", "    \"\"\"\n", "    Callback function to subscribe to data type and symbols upon WebSocket connection.\n", "\n", "    \"\"\"\n", "    # Specify the data type and symbols you want to subscribe to\n", "    data_type = \"SymbolUpdate\"\n", "    \n", "\n", "    # Subscribe to the specified symbols and data type\n", "    symbols = ['NSE:NIFTY25MARFUT']\n", "    fyers.subscribe(symbols=symbols, data_type=data_type)\n", "\n", "    # Keep the socket running to receive real-time data\n", "    fyers.keep_running()\n", "\n", "\n", "# Replace the sample access token with your actual access token obtained from Fyers\n", "#access_token = \"XCXXXXXXM-100:eyJ0tHfZNSBoLo\"\n", "\n", "# Create a FyersDataSocket instance with the provided parameters\n", "fyers = data_ws.FyersDataSocket(\n", "    access_token=access_token,       # Access token in the format \"appid:accesstoken\"\n", "    log_path=\"\",                     # Path to save logs. Leave empty to auto-create logs in the current directory.\n", "    litemode=False,                  # Lite mode disabled. Set to True if you want a lite response.\n", "    write_to_file=False,              # Save response in a log file instead of printing it.\n", "    reconnect=True,                  # Enable auto-reconnection to WebSocket on disconnection.\n", "    on_connect=onopen,               # Callback function to subscribe to data upon connection.\n", "    on_close=onclose,                # Callback function to handle WebSocket connection close events.\n", "    on_error=onerror,                # Callback function to handle WebSocket errors.\n", "    on_message=onmessage             # Callback function to handle incoming messages from the WebSocket.\n", ")\n", "\n", "# Establish a connection to the Fyers WebSocket\n", "fyers.connect()"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'access_token' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 51\u001b[0m\n\u001b[1;32m     44\u001b[0m     fyers\u001b[38;5;241m.\u001b[39mkeep_running()\n\u001b[1;32m     47\u001b[0m \u001b[38;5;66;03m# Replace the sample access token with your actual access token obtained from <PERSON><PERSON>\u001b[39;00m\n\u001b[1;32m     48\u001b[0m \u001b[38;5;66;03m#access_token = \"XCXXXXXXM-100:eyJ0tHfZNSBoLo\"\u001b[39;00m\n\u001b[1;32m     50\u001b[0m fyers \u001b[38;5;241m=\u001b[39m FyersTbtSocket(\n\u001b[0;32m---> 51\u001b[0m     access_token\u001b[38;5;241m=\u001b[39m\u001b[43maccess_token\u001b[49m,  \u001b[38;5;66;03m# Your access token for authenticating with the Fyers API.\u001b[39;00m\n\u001b[1;32m     52\u001b[0m     write_to_file\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m,        \u001b[38;5;66;03m# A boolean flag indicating whether to write data to a log file or not.\u001b[39;00m\n\u001b[1;32m     53\u001b[0m     log_path\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m\"\u001b[39m,                \u001b[38;5;66;03m# The path to the log file if write_to_file is set to True (empty string means current directory).\u001b[39;00m\n\u001b[1;32m     54\u001b[0m     on_open\u001b[38;5;241m=\u001b[39monopen,          \u001b[38;5;66;03m# Callback function to be executed upon successful WebSocket connection.\u001b[39;00m\n\u001b[1;32m     55\u001b[0m     on_close\u001b[38;5;241m=\u001b[39monclose,           \u001b[38;5;66;03m# Callback function to be executed when the WebSocket connection is closed.\u001b[39;00m\n\u001b[1;32m     56\u001b[0m     on_error\u001b[38;5;241m=\u001b[39monerror,           \u001b[38;5;66;03m# Callback function to handle any WebSocket errors that may occur.\u001b[39;00m\n\u001b[1;32m     57\u001b[0m     on_depth_update\u001b[38;5;241m=\u001b[39mon_depth_update, \u001b[38;5;66;03m# Callback function to handle depth-related events from the WebSocket\u001b[39;00m\n\u001b[1;32m     58\u001b[0m     on_error_message\u001b[38;5;241m=\u001b[39monerror_message         \u001b[38;5;66;03m# Callback function to handle server-related erros from the WebSocket.\u001b[39;00m\n\u001b[1;32m     59\u001b[0m )\n\u001b[1;32m     62\u001b[0m \u001b[38;5;66;03m# Establish a connection to the Fyers WebSocket\u001b[39;00m\n\u001b[1;32m     63\u001b[0m fyers\u001b[38;5;241m.\u001b[39mconnect()\n", "\u001b[0;31mNameError\u001b[0m: name 'access_token' is not defined"]}], "source": ["from  fyers_apiv3.FyersWebsocket.tbt_ws import FyersTbtSocket, SubscriptionModes\n", "from datetime import datetime\n", "\n", "def on_depth_update(ticker, message):\n", "    \n", "    \n", "    current_time = datetime.now().strftime(\"%H:%M:%S.%f\")\n", "    print(f\"[{current_time}] Depth Response:\", ticker, message)\n", "\n", "\n", "def onerror_message( message):\n", "    \n", "    print(\"server returned error:\", message)\n", "\n", "\n", "def onerror(message):\n", "   \n", "    print(\"Error:\", message)\n", "\n", "\n", "def onclose(message):\n", "    \"\"\"\n", "    Callback function to handle WebSocket connection close events.\n", "    \"\"\"\n", "    print(\"Connection closed:\", message)\n", "\n", "\n", "def onopen():\n", "    \"\"\"\n", "    Callback function to subscribe to data type and symbols upon WebSocket connection.\n", "\n", "    \"\"\"\n", "    print(\"Connection opened\")\n", "    # Specify the data type and symbols you want to subscribe to\n", "    mode = SubscriptionModes.DEPTH\n", "    Channel = '1'\n", "    # Subscribe to the specified symbols and data type\n", "    symbols = ['NSE:NIFTY25MARFUT']\n", "    \n", "    fyers.subscribe(symbol_tickers=symbols, channelNo=Channel, mode=mode)\n", "    fyers.switchChannel(resume_channels=[Channel], pause_channels=[])\n", "\n", "    # Keep the socket running to receive real-time data\n", "    fyers.keep_running()\n", "\n", "\n", "# Replace the sample access token with your actual access token obtained from Fyers\n", "#access_token = \"XCXXXXXXM-100:eyJ0tHfZNSBoLo\"\n", "\n", "fyers = FyersTbtSocket(\n", "    access_token=access_token,  # Your access token for authenticating with the Fyers API.\n", "    write_to_file=False,        # A boolean flag indicating whether to write data to a log file or not.\n", "    log_path=\"\",                # The path to the log file if write_to_file is set to True (empty string means current directory).\n", "    on_open=onopen,          # Callback function to be executed upon successful WebSocket connection.\n", "    on_close=onclose,           # Callback function to be executed when the WebSocket connection is closed.\n", "    on_error=onerror,           # Callback function to handle any WebSocket errors that may occur.\n", "    on_depth_update=on_depth_update, # Callback function to handle depth-related events from the WebSocket\n", "    on_error_message=onerror_message         # Callback function to handle server-related erros from the WebSocket.\n", ")\n", "\n", "\n", "# Establish a connection to the Fyers WebSocket\n", "fyers.connect()\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.2"}}, "nbformat": 4, "nbformat_minor": 2}