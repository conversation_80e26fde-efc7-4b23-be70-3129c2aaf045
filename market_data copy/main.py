import asyncio
import logging
import sys
import os
from typing import List

from market_data.event_engine import EventEngine
from market_data.market_data_manager import MarketDataManager
from market_data.fyers_handler import FyersHandler
from market_data.data_structures import MarketEvent

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

async def on_orderbook_update(snapshot):
    """Example handler for orderbook updates"""
    logger = logging.getLogger("OrderBookHandler")
    logger.info(f"OrderBook update for {snapshot.symbol}: "
                f"Mid: {snapshot.mid_price:.2f}, Spread: {snapshot.spread:.2f}")
    
    # Log top bids and asks when available
    if snapshot.bids:
        logger.info(f"Top bid: {snapshot.bids[0].price:.2f} x {snapshot.bids[0].quantity}")
    if snapshot.asks:
        logger.info(f"Top ask: {snapshot.asks[0].price:.2f} x {snapshot.asks[0].quantity}")

async def main():
    # Create event engine
    event_engine = EventEngine()
    
    # Create market data manager
    market_data_manager = MarketDataManager(event_engine)
    
    # Register event handlers
    event_engine.register(MarketEvent.ORDERBOOK_UPDATE.value, on_orderbook_update)
    
    # Get access token from environment variable
    access_token = os.environ.get("FYERS_ACCESS_TOKEN")
    if not access_token:
        logging.error("FYERS_ACCESS_TOKEN environment variable not set")
        return
    
    # Create Fyers handler
    fyers_handler = FyersHandler(access_token, market_data_manager)
    
    # Set symbols to subscribe to
    symbols = ['NSE:NIFTY25MARFUT']
    fyers_handler.set_symbols(symbols)
    
    # Connect to Fyers
    fyers_handler.connect()
    
    # Keep the event loop running
    while True:
        await asyncio.sleep(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logging.info("Shutting down...")
