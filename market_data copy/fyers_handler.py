from fyers_apiv3.FyersWebsocket.tbt_ws import FyersTbtSocket, SubscriptionModes
import asyncio
import logging
from typing import List, Dict, Any
import queue
import threading

from market_data.market_data_manager import MarketDataManager

class FyersHandler:
    """Handler for Fyers WebSocket connection"""
    def __init__(self, access_token: str, market_data_manager: MarketDataManager):
        self.market_data_manager = market_data_manager
        self.access_token = access_token
        self.logger = logging.getLogger("FyersHandler")
        self.fyers = None
        self.symbols = []
        self.message_queue = queue.Queue()
        self.running = True
        self.main_loop = asyncio.get_event_loop()
        
        # Start the processor thread
        self.processor_thread = threading.Thread(target=self._process_queue)
        self.processor_thread.daemon = True
        self.processor_thread.start()
        
    def on_depth_update(self, ticker: str, message: Dict[str, Any]):
        """Callback for depth updates from Fyers"""
        # Check if this is a snapshot message
        is_snapshot = False
        if isinstance(message, dict) and 'snapshot' in message:
            is_snapshot = message['snapshot']
        elif hasattr(message, 'snapshot'):
            is_snapshot = message.snapshot
            
        # Add to queue with snapshot flag
        self.message_queue.put((ticker, message, is_snapshot))
        
    def _process_queue(self):
        """Process messages from the queue in a separate thread"""
        while self.running:
            try:
                # Get message from queue with timeout
                ticker, message, is_snapshot = self.message_queue.get(timeout=0.1)
                
                # Create a synchronous function to call the async function
                def process_message():
                    asyncio.create_task(
                        self.market_data_manager.process_depth_tick(ticker, message, is_snapshot)
                    )
                
                # Schedule the function to run in the main event loop
                self.main_loop.call_soon_threadsafe(process_message)
                
            except queue.Empty:
                # No messages in queue, continue
                continue
            except Exception as e:
                self.logger.error(f"Error processing message: {e}", exc_info=True)
        
    def on_error(self, message):
        """Callback for WebSocket errors"""
        self.logger.error(f"Fyers Error: {message}")
        
    def on_close(self, message):
        """Callback for WebSocket connection close"""
        self.logger.info(f"Fyers Connection Closed: {message}")
        self.running = False
        
    def on_open(self):
        """Callback for WebSocket connection open"""
        self.logger.info("Fyers Connection Opened")
        mode = SubscriptionModes.DEPTH
        channel = '1'
        
        # Add symbols to market data manager
        for symbol in self.symbols:
            self.market_data_manager.add_symbol(symbol)
            
        # Subscribe to symbols
        self.fyers.subscribe(symbol_tickers=self.symbols, channelNo=channel, mode=mode)
        self.fyers.switchChannel(resume_channels=[channel], pause_channels=[])
        self.fyers.keep_running()
        
    def set_symbols(self, symbols: List[str]):
        """Set the symbols to subscribe to"""
        self.symbols = symbols
        
    def connect(self):
        """Connect to Fyers WebSocket"""
        self.fyers = FyersTbtSocket(
            access_token=self.access_token,
            write_to_file=False,
            log_path="",
            on_open=self.on_open,
            on_close=self.on_close,
            on_error=self.on_error,
            on_depth_update=self.on_depth_update
        )
        self.logger.info("Connecting to Fyers WebSocket...")
        self.fyers.connect()
