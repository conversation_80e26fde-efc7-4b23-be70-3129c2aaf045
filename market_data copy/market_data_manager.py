from typing import Dict, Set, Any
import asyncio
import logging
import time

from market_data.data_structures import <PERSON><PERSON><PERSON><PERSON><PERSON>, CircularBuffer, MarketEvent
from market_data.orderbook import OrderBook
from market_data.event_engine import EventEngine

class MarketDataManager:
    """Central manager for market data processing"""
    def __init__(self, event_engine: EventEngine):
        self.event_engine = event_engine
        self.tick_buffers: Dict[str, CircularBuffer] = {}
        self.order_books: Dict[str, OrderBook] = {}
        self.subscribed_symbols: Set[str] = set()
        self.logger = logging.getLogger("MarketDataManager")
        
    def add_symbol(self, symbol: str):
        """Add a symbol to track"""
        if symbol not in self.subscribed_symbols:
            self.tick_buffers[symbol] = CircularBuffer()
            self.order_books[symbol] = OrderBook(symbol)
            self.subscribed_symbols.add(symbol)
            self.logger.info(f"Added symbol: {symbol}")
        
    async def process_depth_tick(self, symbol: str, message: Any, is_snapshot: bool = False):
        """Process a depth tick from the market data source"""
        try:
            # Create a DepthTick from the message
            depth_tick = DepthTick.from_fyers_message(symbol, message, is_snapshot)
            
            # Update the orderbook
            if symbol in self.order_books:  # Fixed variable name from orderbooks to order_books
                # If it's a snapshot, clear the orderbook first
                if depth_tick.is_snapshot:
                    self.order_books[symbol].clear()
                
                # Update the orderbook with the new data
                await self.order_books[symbol].update(depth_tick)
                
                # Store the depth tick in the buffer
                await self.tick_buffers[symbol].append(depth_tick)  # Fixed variable name from depth_buffers to tick_buffers
                
                # Emit events
                await asyncio.gather(
                    self.event_engine.emit(MarketEvent.TICK.value, depth_tick),
                    self.event_engine.emit(MarketEvent.ORDERBOOK_UPDATE.value, 
                                         await self.order_books[symbol].get_snapshot())
                )
        except Exception as e:
            self.logger.error(f"Error processing depth tick for {symbol}: {e}", exc_info=True)
    
    async def get_latest_ticks(self, symbol: str, n: int = 1):
        """Get the latest n ticks for a symbol"""
        if symbol in self.tick_buffers:
            return await self.tick_buffers[symbol].get_latest(n)
        return []
    
    async def get_orderbook_snapshot(self, symbol: str):
        """Get the current orderbook snapshot for a symbol"""
        if symbol in self.order_books:
            return await self.order_books[symbol].get_snapshot()
        return None



