import numpy as np
import asyncio
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from market_data.data_structures import DepthTick

@dataclass
class OrderBookLevel:
    price: float
    quantity: float
    orders: int

@dataclass
class OrderBookSnapshot:
    symbol: str
    timestamp: float
    bids: List[OrderBookLevel]
    asks: List[OrderBookLevel]
    mid_price: float
    spread: float

class OrderBook:
    def __init__(self, symbol: str):
        self.symbol = symbol
        self.bids = {}  # price -> (quantity, orders)
        self.asks = {}  # price -> (quantity, orders)
        self.last_update_time = 0
        self._lock = asyncio.Lock()
        
    def clear(self):
        """Clear the orderbook (used for snapshots)"""
        self.bids = {}
        self.asks = {}
        
    async def update(self, depth_tick: DepthTick):
        """Update the orderbook with a new depth tick"""
        async with self._lock:
            self.last_update_time = depth_tick.timestamp
            
            # If it's a snapshot, we've already cleared the book
            # Now just add all the new levels
            
            # Update bids
            for i in range(len(depth_tick.bid_prices)):
                price = depth_tick.bid_prices[i]
                qty = depth_tick.bid_quantities[i]
                orders = depth_tick.bid_orders[i]
                
                if qty > 0:
                    self.bids[price] = OrderBookLevel(price=price, quantity=qty, orders=orders)
                elif price in self.bids:
                    # Remove the price level if quantity is 0
                    del self.bids[price]
            
            # Update asks
            for i in range(len(depth_tick.ask_prices)):
                price = depth_tick.ask_prices[i]
                qty = depth_tick.ask_quantities[i]
                orders = depth_tick.ask_orders[i]
                
                if qty > 0:
                    self.asks[price] = OrderBookLevel(price=price, quantity=qty, orders=orders)
                elif price in self.asks:
                    # Remove the price level if quantity is 0
                    del self.asks[price]
                    
    async def get_snapshot(self) -> OrderBookSnapshot:
        """Get a snapshot of the current order book"""
        async with self._lock:
            bids = sorted(self.bids.values(), key=lambda x: x.price, reverse=True)
            asks = sorted(self.asks.values(), key=lambda x: x.price)
            
            best_bid = bids[0].price if bids else 0
            best_ask = asks[0].price if asks else 0
            
            mid_price = (best_bid + best_ask) / 2 if best_bid and best_ask else 0
            spread = best_ask - best_bid if best_bid and best_ask else 0
            
            return OrderBookSnapshot(
                symbol=self.symbol,
                timestamp=self.last_update_time,
                bids=bids,
                asks=asks,
                mid_price=mid_price,
                spread=spread
            )
    
    async def get_best_bid_ask(self) -> Tuple[Optional[OrderBookLevel], Optional[OrderBookLevel]]:
        """Get the best bid and ask"""
        async with self._lock:
            best_bid = max(self.bids.values(), key=lambda x: x.price) if self.bids else None
            best_ask = min(self.asks.values(), key=lambda x: x.price) if self.asks else None
            return best_bid, best_ask
