<template>
    <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <!-- Dashboard Header -->
      <header class="bg-white/80 backdrop-blur-md shadow-lg border-b border-blue-100 mb-8 sticky top-0 z-50">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-6 flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
          <div class="text-center sm:text-left">
            <h1 class="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">Trading Panel</h1>
            <p class="text-base sm:text-lg text-slate-600 mt-2 font-medium">HFT Real-time position management and trade analytics</p>
          </div>
          <div class="flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-8">
            <div class="p-4 bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 rounded-xl shadow-lg border border-blue-200/50 flex flex-col sm:flex-row items-center space-y-3 sm:space-y-0 sm:space-x-4 backdrop-blur-sm">
              <div class="text-sm font-semibold text-slate-700 uppercase tracking-wider">Total P&L</div>
              <div class="flex items-center space-x-3">
                <svg v-if="totalPnL > 0" class="h-6 w-6 text-emerald-500" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
                <svg v-else-if="totalPnL < 0" class="h-6 w-6 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd" />
                </svg>
                <p class="text-xl font-bold" :class="{'text-emerald-600': totalPnL > 0, 'text-red-600': totalPnL < 0, 'text-slate-600': totalPnL === 0}">
                  {{ formatCurrency(totalPnL) }}
                </p>
              </div>
            </div>
            <button @click="exitAllTrades" class="px-6 py-3 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-xl hover:from-red-600 hover:to-red-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 flex items-center justify-center font-semibold">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
              </svg>
              Exit All Positions
            </button>
          </div>
        </div>
      </header>
  
      <div class="container mx-auto px-4 sm:px-6 lg:px-8 space-y-8">
        <!-- Active Trades Card -->
        <div class="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-blue-100/50">
          <div class="p-6 border-b border-slate-200/70 bg-gradient-to-r from-blue-50/50 to-indigo-50/50 rounded-t-2xl">
            <div class="flex items-center space-x-3">
              <div class="p-2 bg-blue-500 rounded-lg">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                </svg>
              </div>
              <h2 class="text-2xl font-bold text-slate-800">Active Positions</h2>
              <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold">{{ activeTrades.length }}</span>
            </div>
          </div>
          <div class="overflow-x-auto">
            <div class="max-h-[450px] overflow-y-auto">
              <table class="min-w-full divide-y divide-slate-200">
                <thead class="bg-gradient-to-r from-slate-50 to-slate-100 sticky top-0">
                  <tr>
                    <th class="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider" style="min-width: 120px;">Symbol</th>
                    <th class="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider" style="min-width: 100px;">Direction</th>
                    <th class="px-6 py-4 text-right text-xs font-bold text-slate-600 uppercase tracking-wider" style="min-width: 120px;">Entry Price</th>
                    <th class="px-6 py-4 text-right text-xs font-bold text-slate-600 uppercase tracking-wider" style="min-width: 120px;">Current P&L</th>
                    <th class="px-6 py-4 text-center text-xs font-bold text-slate-600 uppercase tracking-wider" style="min-width: 120px;">Stop Loss</th>
                    <th class="px-6 py-4 text-center text-xs font-bold text-slate-600 uppercase tracking-wider" style="min-width: 120px;">SL-Trigger</th>
                    <th class="px-6 py-4 text-center text-xs font-bold text-slate-600 uppercase tracking-wider" style="min-width: 120px;">Target</th>
                    <th class="px-6 py-4 text-center text-xs font-bold text-slate-600 uppercase tracking-wider" style="min-width: 100px;">Actions</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-slate-100">
                  <tr v-for="trade in activeTrades" :key="trade.order_id" class="hover:bg-gradient-to-r hover:from-blue-50/30 hover:to-indigo-50/30 transition-all duration-300">
                    <td class="px-6 py-4 text-sm font-semibold text-slate-900">{{ trade.symbol }}</td>
                    <td class="px-6 py-4">
                      <span class="px-3 py-1 rounded-full text-xs font-bold" :class="trade.action === 'BUY' ? 'bg-emerald-100 text-emerald-800 border border-emerald-200' : 'bg-red-100 text-red-800 border border-red-200'">
                        {{ trade.action }}
                      </span>
                    </td>
                    <td class="px-6 py-4 text-sm font-medium text-slate-900 text-right">₹{{ formatPrice(trade.entry_price) }}</td>
                    <td class="px-6 py-4 text-sm font-bold text-right" :class="{'text-emerald-600': trade.current_pnl > 0, 'text-red-600': trade.current_pnl < 0, 'text-slate-600': trade.current_pnl === 0}">
                      {{ formatCurrency(trade.current_pnl) }}
                    </td>
                    <td class="px-6 py-4 text-center">
                      <div class="flex flex-col items-center space-y-1">
                        <input type="number" :value="trade.sl" @input="(e) => updateSL(trade.symbol, e.target.value)" 
                               class="w-24 p-2 border-2 border-slate-200 rounded-lg text-center text-sm font-medium focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all bg-slate-50 hover:bg-white" step="1" />
                        <span class="text-xs text-slate-500 font-medium">₹{{ calculatePrice(trade, 'sl') }}</span>
                      </div>
                    </td>
                    <td class="px-6 py-4 text-center">
                      <span class="px-3 py-2 rounded-lg text-sm font-semibold" :class="trade.trail_sl_trigger ? 'bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 border border-blue-200 animate-pulse' : 'bg-slate-100 text-slate-600'">
                        {{ trade.trail_sl_trigger ? '₹' + formatPrice(trade.trail_sl_trigger) : '-' }}
                      </span>
                    </td>
                    <td class="px-6 py-4 text-center">
                      <div class="flex flex-col items-center space-y-1">
                        <input type="number" :value="trade.target" @input="(e) => updateTarget(trade.symbol, e.target.value)" 
                               class="w-24 p-2 border-2 border-slate-200 rounded-lg text-center text-sm font-medium focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all bg-slate-50 hover:bg-white" step="1" />
                        <span class="text-xs text-slate-500 font-medium">₹{{ calculatePrice(trade, 'target') }}</span>
                      </div>
                    </td>
                    <td class="px-6 py-4 text-center">
                      <button @click="closeTrade(trade.symbol)" class="px-4 py-2 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-lg hover:from-red-600 hover:to-red-700 transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 font-semibold text-sm">
                        Close
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
  
        <!-- Trade History Card -->
        <div class="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-blue-100/50">
          <div class="p-6 border-b border-slate-200/70 bg-gradient-to-r from-slate-50/50 to-blue-50/50 rounded-t-2xl">
            <div class="flex items-center space-x-3">
              <div class="p-2 bg-slate-600 rounded-lg">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
              </div>
              <h2 class="text-2xl font-bold text-slate-800">Trade History</h2>
              <span class="px-3 py-1 bg-slate-100 text-slate-800 rounded-full text-sm font-semibold">{{ tradeHistory.length }}</span>
            </div>
          </div>
          <div class="overflow-x-auto">
            <div class="max-h-[300px] overflow-y-auto">
              <table class="min-w-full divide-y divide-slate-200">
                <thead class="bg-gradient-to-r from-slate-50 to-slate-100 sticky top-0">
                  <tr>
                    <th class="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider" style="min-width: 200px;">Date & Time</th>
                    <th class="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">Symbol</th>
                    <th class="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">Signal</th>
                    <th class="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">Entry</th>
                    <th class="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">Exit</th>
                    <th class="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider" style="min-width: 100px;">Exit Time</th>
                    <th class="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">P&L</th>
                    <th class="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">Exit Reason</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-slate-100">
                  <tr v-for="(trade, index) in tradeHistory" :key="trade.Symbol" :class="{'bg-white': index % 2 === 0, 'bg-slate-50/30': index % 2 !== 0}" class="hover:bg-gradient-to-r hover:from-slate-50 hover:to-blue-50/30 transition-all duration-200">
                    <td class="px-6 py-3 text-sm font-medium text-slate-900">{{ `${trade.Date} ${trade.Time}` }}</td>
                    <td class="px-6 py-3 text-sm font-semibold text-slate-900">{{ trade.Symbol }}</td>
                    <td class="px-6 py-3">
                      <span class="px-2 py-1 rounded-full text-xs font-bold" :class="trade.Signal === 'BUY' ? 'bg-emerald-100 text-emerald-800 border border-emerald-200' : 'bg-red-100 text-red-800 border border-red-200'">
                        {{ trade.Signal }}
                      </span>
                    </td>
                    <td class="px-6 py-3 text-sm font-medium text-slate-900">₹{{ trade.Entry }}</td>
                    <td class="px-6 py-3 text-sm font-medium text-slate-900">₹{{ trade.Exit }}</td>
                    <td class="px-6 py-3 text-sm text-slate-600">{{ trade['Exit Time'] }}</td>
                    <td class="px-6 py-3 text-sm font-bold" :class="{'text-emerald-600': parseFloat(trade['P&L']) > 0, 'text-red-600': parseFloat(trade['P&L']) < 0, 'text-slate-600': parseFloat(trade['P&L']) === 0}">
                      {{ formatCurrency(parseFloat(trade['P&L'])) }}
                    </td>
                    <td class="px-6 py-3">
                      <span class="px-2 py-1 rounded-full text-xs font-semibold" :class="trade.Status === 'Active' ? 'bg-blue-100 text-blue-800 border border-blue-200' : 'bg-slate-100 text-slate-800 border border-slate-200'">
                        {{ trade.Status }}
                      </span>
                    </td>
                    <td class="px-6 py-3 text-sm text-slate-500">{{ trade['Exit Reason'] }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
  
        <!-- Strategy Manager Card -->
        <div class="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-blue-100/50 mb-8">
          <div class="p-6 border-b border-slate-200/70 bg-gradient-to-r from-indigo-50/50 to-purple-50/50 rounded-t-2xl">
            <div class="flex items-center space-x-3">
              <div class="p-2 bg-indigo-600 rounded-lg">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
              </div>
              <h2 class="text-2xl font-bold text-slate-800">Strategy Manager</h2>
              <span class="px-3 py-1 bg-indigo-100 text-indigo-800 rounded-full text-sm font-semibold">{{ strategies.length }}</span>
            </div>
          </div>
          <div class="overflow-x-auto">
            <div class="p-6">
              <table class="min-w-full divide-y divide-slate-200">
                <thead class="bg-gradient-to-r from-slate-50 to-slate-100">
                  <tr>
                    <th class="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">Strategy ID</th>
                    <th class="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">Token</th>
                    <th class="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">Timeframe</th>
                    <th class="px-6 py-4 text-center text-xs font-bold text-slate-600 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-4 text-center text-xs font-bold text-slate-600 uppercase tracking-wider">Buy</th>
                    <th class="px-6 py-4 text-center text-xs font-bold text-slate-600 uppercase tracking-wider">Sell</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-slate-100">
                  <tr v-for="strategy in strategies" :key="`${strategy.token}-${strategy.timeframe}-${strategy.strategy_id}`" class="hover:bg-gradient-to-r hover:from-indigo-50/30 hover:to-purple-50/30 transition-all duration-300">
                    <td class="px-6 py-4 text-sm font-semibold text-slate-900">{{ strategy.strategy_id }}</td>
                    <td class="px-6 py-4 text-sm font-medium text-slate-700">{{ strategy.token }}</td>
                    <td class="px-6 py-4 text-sm font-medium text-slate-700">{{ strategy.timeframe }}</td>
                    <td class="px-6 py-4 text-center">
                      <button @click="toggleStrategy(strategy)" 
                              class="px-4 py-2 rounded-xl text-sm font-bold transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5" 
                              :class="strategy.enabled ? 'bg-gradient-to-r from-emerald-100 to-emerald-200 text-emerald-800 border border-emerald-300 hover:from-emerald-200 hover:to-emerald-300' : 'bg-gradient-to-r from-red-100 to-red-200 text-red-800 border border-red-300 hover:from-red-200 hover:to-red-300'">
                        {{ strategy.enabled ? 'Enabled' : 'Disabled' }}
                      </button>
                    </td>
                    <td class="px-6 py-4 text-center" v-if="strategy.enabled">
                      <button @click="toggleTradeControl(strategy, !strategy.enable_buy, strategy.enable_sell)" 
                              class="px-4 py-2 rounded-xl text-sm font-bold transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5" 
                              :class="strategy.enable_buy ? 'bg-gradient-to-r from-emerald-100 to-emerald-200 text-emerald-800 border border-emerald-300 hover:from-emerald-200 hover:to-emerald-300' : 'bg-gradient-to-r from-red-100 to-red-200 text-red-800 border border-red-300 hover:from-red-200 hover:to-red-300'">
                        {{ strategy.enable_buy ? 'Enabled' : 'Disabled' }}
                      </button>
                    </td>
                    <td class="px-6 py-4 text-center" v-if="strategy.enabled">
                      <button @click="toggleTradeControl(strategy, strategy.enable_buy, !strategy.enable_sell)" 
                              class="px-4 py-2 rounded-xl text-sm font-bold transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5" 
                              :class="strategy.enable_sell ? 'bg-gradient-to-r from-emerald-100 to-emerald-200 text-emerald-800 border border-emerald-300 hover:from-emerald-200 hover:to-emerald-300' : 'bg-gradient-to-r from-red-100 to-red-200 text-red-800 border border-red-300 hover:from-red-200 hover:to-red-300'">
                        {{ strategy.enable_sell ? 'Enabled' : 'Disabled' }}
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script>
  export default {
    data() {
      return {
        ws: null,
        activeTrades: [],
        tradeHistory: [],
        totalPnL: 0,
        strategies: []
      };
    },
    mounted() {
      this.connectWebSocket();
      this.loadStrategies();
    },
    beforeUnmount() {
      if (this.ws) {
        this.ws.close();
      }
    },
    methods: {
      formatCurrency(value) {
        return new Intl.NumberFormat('en-IN', {
          style: 'currency',
          currency: 'INR',
          minimumFractionDigits: 2
        }).format(value);
      },
      formatPrice(value) {
        return new Intl.NumberFormat('en-IN', {
          minimumFractionDigits: 2
        }).format(value);
      },
      connectWebSocket() {
        this.ws = new WebSocket('ws://localhost:8000/ws/trades');
  
        this.ws.onmessage = (event) => {
          const data = JSON.parse(event.data);
          if (data.type === 'initial_data') {
            this.activeTrades = data.active_trades;
            this.tradeHistory = data.trade_history;
          } else if (data.type === 'update') {
            this.activeTrades = data.active_trades;
            this.parseTradeHistory(data.trade_history);
            this.calculateTotalPnL();
          } else if (data.type === 'strategy_update') {
            this.strategies = [...data.strategy_states];
          }
        };
  
        this.ws.onclose = () => {
          setTimeout(() => {
            this.connectWebSocket();
          }, 1000);
        };
      },
  
      calculatePrice(trade, type) {
        if (!trade || !trade.entry_price) return '-'; // Check if trade or entry_price is missing
        const points = trade[type]; // Get the SL or Target points
        if (!points) return '-'; // If points are missing, return '-'
  
        // For both calls and puts:
        // - SL is calculated as entry_price - points
        // - Target is calculated as entry_price + points
        return type === 'target'
          ? (trade.entry_price + points).toFixed(2) // Target calculation
          : (trade.entry_price - points).toFixed(2); // SL calculation
      },
  
      calculateTotalPnL() {
        const historyPnL = this.tradeHistory.reduce((sum, trade) => sum + parseFloat(trade['P&L'] || 0), 0);
        const activePnL = this.activeTrades.reduce((sum, trade) => sum + (trade.current_pnl || 0), 0);
        this.totalPnL = historyPnL;
      },
      
      parseTradeHistory(historyString) {
          if (typeof historyString !== 'string') return [];
          const lines = historyString.split('\n');
          const trades = [];
  
          for (const line of lines) {
              if (line.includes('Trade History:') || line.includes('Trade Key') || line.trim().startsWith('---')) {
              continue;
              }
              if (line.includes('Total P&L:')) break;
              if (line.trim()) {
              const parts = line.trim().split(/\s+/).filter(Boolean);
              if (parts.length >= 11) {
                  const [tradeKey, date, time, symbol, action, entry, exit, exitTime, pnl, status, ...exitReason] = parts;
                  trades.push({
                  'Date': date,
                  'Time': time,
                  'Symbol': symbol,
                  'Signal': action,
                  'Entry': parseFloat(entry).toFixed(2),
                  'Exit': parseFloat(exit).toFixed(2),
                  'Exit Time': exitTime,
                  'P&L': parseFloat(pnl).toFixed(2),
                  'Status': status,
                  'Exit Reason': exitReason.join(' ') || 'N/A'
                  });
              }
              }
          }
  
          this.tradeHistory = trades.sort((a, b) => {
              const dateTimeA = new Date(`${a.Date} ${a.Time}`);
              const dateTimeB = new Date(`${b.Date} ${b.Time}`);
              return dateTimeB - dateTimeA; // Sort by date & time DESCENDING (newest first)
          });
  
          this.calculateTotalPnL();
          },
      
      async updateSL(symbol, sl) {
        await this.sendTradeAction({
          action: 'update_sl',
          trade_key: symbol,
          new_sl: parseFloat(sl)
        });
      },
      async loadStrategies() {
      try {
        const response = await fetch('http://localhost:8000/get_all_strategies');
        const data = await response.json();
        this.strategies = data;
      } catch (error) {
        console.error('Error loading strategies:', error);
      }
    },
    async toggleStrategy(strategy) {
      const endpoint = strategy.enabled ? 'http://localhost:8000/disable_strategy' : 'http://localhost:8000/enable_strategy';
      try {
        const response = await fetch(endpoint, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            token: strategy.token,
            timeframe: strategy.timeframe,
            strategy_id: strategy.strategy_id
          })
        });
        await this.loadStrategies();
      } catch (error) {
        console.error('Error toggling strategy:', error);
      }
    },
    async toggleTradeControl(strategy, enable_buy, enable_sell) {
      try {
        const response = await fetch('http://localhost:8000/set_trade_control', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            token: strategy.token,
            timeframe: strategy.timeframe,
            strategy_id: strategy.strategy_id,
            enable_buy: enable_buy,
            enable_sell: enable_sell
          })
        });
        await this.loadStrategies();
      } catch (error) {
        console.error('Error updating trade controls:', error);
      }
    },
    async updateTarget(symbol, target) {
      await this.sendTradeAction({
        action: 'update_target',
        trade_key: symbol,
        new_target: parseFloat(target)
      });
    },
    async closeTrade(symbol) {
      await this.sendTradeAction({
        action: 'close_trade',
        trade_key: symbol
      });
    },
    async exitAllTrades() {
      await this.sendTradeAction({
        action: 'exit_all_trades'
      });
    },
    async sendTradeAction(action) {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify(action));
      }
    }
  }
};
</script>

<style scoped>
/* Add any component-specific styles here if needed */
</style>
