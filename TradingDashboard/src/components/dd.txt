<template>
    <div class="min-h-screen bg-gray-50/30 dark:bg-gray-900">
      <!-- Dashboard Header -->
      <header class="bg-white/80 dark:bg-gray-800 backdrop-blur-sm sticky top-0 z-50 border-b border-gray-200 dark:border-gray-700">
        <div class="container mx-auto px-4 py-4">
          <div class="flex flex-col lg:flex-row justify-between items-center gap-4">
            <div>
              <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Trading Dashboard</h1>
              <p class="text-sm text-gray-600 dark:text-gray-400">Real-time position management</p>
            </div>
            
            <div class="flex flex-col sm:flex-row items-center gap-4">
              <!-- P&L Card -->
              <div class="bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 shadow-lg rounded-xl p-4 border border-gray-200/50 dark:border-gray-700/50">
                <div class="flex items-center gap-3">
                  <div class="flex flex-col">
                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Total P&L</span>
                    <span class="text-xl font-bold" :class="{'text-green-600 dark:text-green-400': totalPnL > 0, 'text-red-600 dark:text-red-400': totalPnL < 0}">
                      {{ formatCurrency(totalPnL) }}
                    </span>
                  </div>
                  <div class="p-2 rounded-full" :class="{'bg-green-100 dark:bg-green-900': totalPnL > 0, 'bg-red-100 dark:bg-red-900': totalPnL < 0}">
                    <svg v-if="totalPnL > 0" class="h-6 w-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                    <svg v-else class="h-6 w-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0v-8m0 8l-8-8-4 4-6-6"></path>
                    </svg>
                  </div>
                </div>
              </div>
              
              <button @click="exitAllTrades" class="px-6 py-2.5 bg-red-600 hover:bg-red-700 text-white rounded-lg shadow-lg transition-all duration-200 flex items-center gap-2">
                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                Exit All Positions
              </button>
            </div>
          </div>
        </div>
      </header>
  
      <div class="container mx-auto px-4 py-6 space-y-6">
        <!-- Active Trades Panel -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-xl border border-gray-200/50 dark:border-gray-700/50 overflow-hidden">
          <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
              <div>
                <h2 class="text-xl font-bold text-gray-900 dark:text-white">Active Positions</h2>
                <p class="text-sm text-gray-600 dark:text-gray-400">Currently open trades</p>
              </div>
              <div class="flex items-center gap-2">
                <span class="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded-full text-sm">
                  {{ activeTrades.length }} Active
                </span>
              </div>
            </div>
        
        <div class="overflow-x-auto">
          <div class="max-h-[400px] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-700">
            <table class="w-full">
              <thead class="bg-gray-50 dark:bg-gray-900/50 sticky top-0">
                <tr>
                  <th class="px-6 py-4 sm:px-4 text-left text-xs sm:text-sm font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wider whitespace-nowrap">Symbol</th>
                  <th class="px-6 py-4 sm:px-4 text-left text-xs sm:text-sm font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wider whitespace-nowrap">Direction</th>
                  <th class="px-6 py-4 sm:px-4 text-right text-xs sm:text-sm font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wider whitespace-nowrap">Entry Price</th>
                  <th class="px-6 py-4 sm:px-4 text-right text-xs sm:text-sm font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wider whitespace-nowrap">Curr. P&L</th>
                  <th class="px-6 py-4 sm:px-4 text-center text-xs sm:text-sm font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wider whitespace-nowrap">Stop Loss</th>
                  <th class="px-6 py-4 sm:px-4 text-center text-xs sm:text-sm font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wider whitespace-nowrap">SL Trigger</th>
                  <th class="px-6 py-4 sm:px-4 text-center text-xs sm:text-sm font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wider whitespace-nowrap">Target</th>
                  <th class="px-6 py-4 sm:px-4 text-center text-xs sm:text-sm font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wider whitespace-nowrap">Actions</th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                <tr v-for="trade in activeTrades" :key="trade.order_id" class="hover:bg-gray-50 dark:hover:bg-gray-900/30 transition-colors duration-150">
                  <td class="px-6 py-4 sm:px-4">
                    <div class="flex flex-col">
                      <span class="text-sm font-medium text-gray-900 dark:text-white">{{ trade.symbol }}</span>
                      <span class="text-xs text-gray-500 dark:text-gray-400">{{ formatTime(trade.entry_time) }}</span>
                    </div>
                  </td>
                  <td class="px-6 py-4 sm:px-4">
                    <span class="px-3 py-1 rounded-full text-sm font-medium" 
                      :class="trade.action === 'BUY' ? 'bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300' : 'bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300'">
                      {{ trade.action }}
                    </span>
                  </td>
                  <td class="px-6 py-4 sm:px-4 text-right">
                    <div class="flex flex-col">
                      <span class="text-sm font-medium text-gray-900 dark:text-white">{{ formatPrice(trade.entry_price) }}</span>
                      <span class="text-xs text-gray-500 dark:text-gray-400">Entry Price</span>
                    </div>
                  </td>
                  <td class="px-6 py-4 sm:px-4">
                    <div class="flex flex-col items-end">
                      <span class="text-sm font-bold" :class="{'text-green-600 dark:text-green-400': trade.current_pnl > 0, 'text-red-600 dark:text-red-400': trade.current_pnl < 0}">
                        {{ formatCurrency(trade.current_pnl) }}
                      </span>
                      <span class="text-xs" :class="{'text-green-600 dark:text-green-400': trade.current_pnl > 0, 'text-red-600 dark                      -text-red-400': trade.current_pnl < 0}">
                        {{ ((trade.current_pnl / (trade.entry_price * 100)) * 100).toFixed(2) }}%
                      </span>
                    </div>
                  </td>
                  <td class="px-6 py-4 sm:px-4">
                    <div class="flex flex-col items-center gap-1">
                      <input 
                        type="number" 
                        :value="trade.sl" 
                        @input="(e) => updateSL(trade.symbol, e.target.value)"
                        class="w-24 px-2 py-1 text-center rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-900 focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600 focus:border-transparent transition-all"
                      />
                      <span class="text-xs text-gray-500 dark:text-gray-400">{{ calculatePrice(trade, 'sl') }}</span>
                    </div>
                  </td>
                  <td class="px-6 py-4 sm:px-4">
                    <div class="flex items-center justify-center h-full min-h-[52px]">
                      <span 
                        class="w-24 px-2 py-1 text-center rounded-lg transition-all font-medium"
                        :class="{
                          'bg-blue-200 dark:bg-blue-400 text-blue-900 dark:text-blue-900': trade.trail_sl_trigger,
                          'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-900': !trade.trail_sl_trigger,
                          'ring-4 ring-red-400 dark:ring-red-500': trade.trail_sl_trigger !== trade.initial_trail_sl_trigger
                        }"
                      >
                        {{ trade.trail_sl_trigger ? formatPrice(trade.trail_sl_trigger) : '-' }}
                      </span>
                    </div>
                  </td>
                  <td class="px-6 py-4 sm:px-4">
                    <div class="flex flex-col items-center gap-1">
                      <input 
                        type="number" 
                        :value="trade.target" 
                        @input="(e) => updateTarget(trade.symbol, e.target.value)"
                        class="w-24 px-2 py-1 text-center rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-900 focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600 focus:border-transparent transition-all"
                      />
                      <span class="text-xs text-gray-500 dark:text-gray-400">{{ calculatePrice(trade, 'target') }}</span>
                    </div>
                  </td>
                  <td class="px-6 py-4 sm:px-4">
                    <button @click="closeTrade(trade.symbol)" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg shadow-sm transition-colors duration-200">
                      Close
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Trade History Panel -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-xl border border-gray-200/50 dark:border-gray-700/50 overflow-hidden">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between">
            <div>
              <h2 class="text-xl font-bold text-gray-900 dark:text-white">Trade History</h2>
              <p class="text-sm text-gray-600 dark:text-gray-400">Past trading activity</p>
            </div>
            <div class="flex items-center gap-2">
              <span class="px-3 py-1 bg-gray-100 dark:bg-gray-900 text-gray-700 dark:text-gray-300 rounded-full text-sm">
                {{ tradeHistory.length }} Trades
              </span>
            </div>
          </div>
        </div>
        
        <div class="overflow-x-auto">
          <div class="max-h-[400px] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-700">
            <table class="w-full">
              <thead class="bg-gray-50 dark:bg-gray-900/50 sticky top-0">
                <tr>
                  <th class="px-6 py-4 sm:px-4 text-left text-xs sm:text-sm font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wider whitespace-nowrap">Trade Info</th>
                  <th class="px-6 py-4 sm:px-4 text-left text-xs sm:text-sm font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wider whitespace-nowrap">Signal</th>
                  <th class="px-6 py-4 sm:px-4 text-left text-xs sm:text-sm font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wider whitespace-nowrap">Entry/Exit</th>
                  <th class="px-6 py-4 sm:px-4 text-right text-xs sm:text-sm font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wider whitespace-nowrap">P&L</th>
                  <th class="px-6 py-4 sm:px-4 text-left text-xs sm:text-sm font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wider whitespace-nowrap">Status</th>
                  <th class="px-6 py-4 sm:px-4 text-left text-xs sm:text-sm font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wider whitespace-nowrap">Exit Info</th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                <tr v-for="trade in tradeHistory" :key="`${trade.Symbol}-${trade.Date}-${trade.Time}`" 
                    class="hover:bg-gray-50 dark:hover:bg-gray-900/30 transition-colors duration-150"
                    :class="{'bg-blue-50/50 dark:bg-blue-900/20': trade.Status === 'Active'}">
                  <td class="px-6 py-4 sm:px-4">
                    <div class="flex flex-col">
                      <span class="text-sm font-medium text-gray-900 dark:text-white">{{ trade.Symbol }}</span>
                      <span class="text-xs text-gray-500 dark:text-gray-400">{{ `${trade.Date} ${trade.Time}` }}</span>
                    </div>
                  </td>
                  <td class="px-6 py-4 sm:px-4">
                    <span class="px-3 py-1 rounded-full text-sm font-medium"
                          :class="trade.Signal === 'BUY' ? 'bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300' : 'bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300'">
                      {{ trade.Signal }}
                    </span>
                  </td>
                  <td class="px-6 py-4 sm:px-4">
                    <div class="flex flex-col">
                      <div class="flex items-center gap-2">
                        <span class="text-sm font-medium text-gray-900 dark:text-white">{{ trade.Entry }}</span>
                        <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">{{ trade.Exit }}</span>
                      </div>
                      <span class="text-xs text-gray-500 dark:text-gray-400">Exit: {{ trade['Exit Time'] }}</span>
                    </div>
                  </td>
                  <td class="px-6 py-4 sm:px-4">
                    <div class="flex flex-col items-end">
                      <span class="text-sm font-bold" 
                            :class="{'text-green-600 dark:text-green-400': parseFloat(trade['P&L']) > 0, 
                                    'text-red-600 dark:text-red-400': parseFloat(trade['P&L']) < 0}">
                        {{ formatCurrency(parseFloat(trade['P&L'])) }}
                      </span>
                      <span class="text-xs text-gray-500 dark:text-gray-400">
                        {{ ((parseFloat(trade['P&L']) / parseFloat(trade.Entry)) * 100).toFixed(2) }}%
                      </span>
                    </div>
                  </td>
                  <td class="px-6 py-4 sm:px-4">
                    <span class="px-3 py-1 rounded-full text-sm font-medium"
                          :class="trade.Status === 'Active' ? 
                                 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300' : 
                                 'bg-gray-100 dark:bg-gray-900 text-gray-700 dark:text-gray-300'">
                      {{ trade.Status }}
                  </span>
                </td>
                  <td class="px-6 py-4 sm:px-4">
                    <div class="flex flex-col">
                      <span class="text-sm text-gray-600 dark:text-gray-400">{{ trade['Exit Reason'] }}</span>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Strategy Manager Panel -->
      <!-- <div class="bg-white dark:bg-gray-800 rounded-xl shadow-xl border border-gray-200/50 dark:border-gray-700/50 overflow-hidden">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 class="text-xl font-bold text-gray-900 dark:text-white">Strategy Manager</h2>
          <p class="text-sm text-gray-600 dark:text-gray-400">Manage trading strategies</p>
        </div>
        
        <div class="overflow-x-auto">
          <div class="p-4">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50 dark:bg-gray-900/50">
                <tr>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">Strategy ID</th>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">Token</th>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">Timeframe</th>
                  <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">Status</th>
                  <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">Buy</th>
                  <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">Sell</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
                <tr v-for="strategy in strategies" :key="`${strategy.token}-${strategy.timeframe}-${strategy.strategy_id}`" class="hover:bg-gray-50 dark:hover:bg-gray-900/30 transition-colors duration-200">
                  <td class="px-4 py-4 text-sm text-gray-900 dark:text-white">{{ strategy.strategy_id }}</td>
                  <td class="px-4 py-4 text-sm text-gray-500 dark:text-gray-400">{{ strategy.token }}</td>
                  <td class="px-4 py-4 text-sm text-gray-500 dark:text-gray-400">{{ strategy.timeframe }}</td>
                  <td class="px-4 py-4 text-center">
                    <button @click="toggleStrategy(strategy)" :class="`px-3 py-1 rounded-full text-xs font-semibold ${strategy.enabled ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'}`">
                      {{ strategy.enabled ? 'Enabled' : 'Disabled' }}
                    </button>
                  </td>
                  <td class="px-4 py-4 text-center" v-if="strategy.enabled">
                    <button @click="toggleTradeControl(strategy, !strategy.enable_buy, strategy.enable_sell)" :class="`px-3 py-1 rounded-full text-xs font-semibold ${strategy.enable_buy ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'}`">
                      {{ strategy.enable_buy ? 'Enabled' : 'Disabled' }}
                    </button>
                  </td>
                  <td class="px-4 py-4 text-center" v-if="strategy.enabled">
                    <button @click="toggleTradeControl(strategy, strategy.enable_buy, !strategy.enable_sell)" :class="`px-3 py-1 rounded-full text-xs font-semibold ${strategy.enable_sell ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'}`">
                      {{ strategy.enable_sell ? 'Enabled' : 'Disabled' }}
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div> -->
			<div class="bg-white dark:bg-gray-800 rounded-xl shadow-xl border border-gray-200/50 dark:border-gray-700/50">
  <div class="p-4 border-b border-gray-200 dark:border-gray-700">
    <div class="flex items-center justify-between">
      <h2 class="text-lg font-bold text-gray-900 dark:text-white">Strategies</h2>
      <span class="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded-full text-sm">
        {{ strategies.length }} Active
      </span>
    </div>
  </div>
  
  <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
    <div v-for="strategy in strategies" 
         :key="`${strategy.token}-${strategy.timeframe}`" 
         class="p-3 bg-gray-50 dark:bg-gray-900/50 rounded-lg">
      <div class="flex justify-between items-start mb-2">
        <div>
          <span class="font-medium text-gray-900 dark:text-white">{{ strategy.token }}</span>
          <span class="text-sm text-gray-500 ml-2">{{ strategy.timeframe }}</span>
        </div>
        <button @click="toggleStrategy(strategy)" 
                :class="`px-2 py-1 rounded text-xs font-medium ${strategy.enabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`">
          {{ strategy.enabled ? 'Active' : 'Inactive' }}
        </button>
      </div>
      <div class="flex gap-2 mt-2" v-if="strategy.enabled">
        <button @click="toggleTradeControl(strategy, !strategy.enable_buy, strategy.enable_sell)" 
                :class="`flex-1 px-2 py-1 rounded text-xs font-medium ${strategy.enable_buy ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`">
          Buy
        </button>
        <button @click="toggleTradeControl(strategy, strategy.enable_buy, !strategy.enable_sell)" 
                :class="`flex-1 px-2 py-1 rounded text-xs font-medium ${strategy.enable_sell ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`">
          Sell
        </button>
      </div>
    </div>
  </div>
</div>
    </div>
  </div>
</div>
</template>

<script>
export default {
  data() {
    return {
      ws: null,
      activeTrades: [],
      tradeHistory: [],
      totalPnL: 0,
      strategies: []
    };
  },
  
  methods: {
    formatTime(timestamp) {
      if (!timestamp) return '';
      const date = new Date(timestamp);
      return date.toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: true 
      });
    },

    formatCurrency(value) {
      return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR',
        minimumFractionDigits: 2
      }).format(value);
    },

    formatPrice(value) {
      return new Intl.NumberFormat('en-IN', {
        minimumFractionDigits: 2
      }).format(value);
    },

    calculatePrice(trade, type) {
      if (!trade || !trade.entry_price) return '-';
      const points = trade[type];
      if (!points) return '-';

      if (trade.action === 'BUY') {
        return type === 'target'
          ? this.formatPrice(trade.entry_price + points)
          : this.formatPrice(trade.entry_price - points);
      } else {
        return type === 'target'
          ? this.formatPrice(trade.entry_price - points)
          : this.formatPrice(trade.entry_price + points);
      }
    },

    connectWebSocket() {
      this.ws = new WebSocket('ws://localhost:8000/ws/trades');

      this.ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        if (data.type === 'initial_data') {
          this.activeTrades = data.active_trades;
          this.tradeHistory = data.trade_history;
        } else if (data.type === 'update') {
          this.activeTrades = data.active_trades;
          this.parseTradeHistory(data.trade_history);
          this.calculateTotalPnL();
        }
      };

      this.ws.onclose = () => {
        setTimeout(() => {
          this.connectWebSocket();
        }, 1000);
      };
    },

    parseTradeHistory(historyString) {
      if (typeof historyString !== 'string') return [];
      const lines = historyString.split('\n');
      const trades = [];

      for (const line of lines) {
        if (line.includes('Trade History:') || line.includes('Trade Key') || line.trim().startsWith('---')) {
          continue;
        }
        if (line.includes('Total P&L:')) break;
        if (line.trim()) {
          const parts = line.trim().split(/\s+/).filter(Boolean);
          if (parts.length >= 11) {
            const [tradeKey, date, time, symbol, action, entry, exit, exitTime, pnl, status, ...exitReason] = parts;
            trades.push({
              'Date': date,
              'Time': time,
              'Symbol': symbol,
              'Signal': action,
              'Entry': parseFloat(entry).toFixed(2),
              'Exit': parseFloat(exit).toFixed(2),
              'Exit Time': exitTime,
              'P&L': parseFloat(pnl).toFixed(2),
              'Status': status,
              'Exit Reason': exitReason.join(' ') || 'N/A'
            });
          }
        }
      }

      // Sort trades with active trades on top, followed by most recently closed trades
      this.tradeHistory = trades.sort((a, b) => {
        if (a.Status === 'Active' && b.Status !== 'Active') return -1;
        if (a.Status !== 'Active' && b.Status === 'Active') return 1;
        
        const dateTimeA = new Date(`${a.Date} ${a.Time}`);
        const dateTimeB = new Date(`${b.Date} ${b.Time}`);
        return dateTimeB - dateTimeA;
      });

      this.calculateTotalPnL();
    },

    async loadStrategies() {
      try {
        const response = await fetch('http://localhost:8000/get_all_strategies');
        const data = await response.json();
        this.strategies = data;
      } catch (error) {
        console.error('Error loading strategies:', error);
      }
    },
    async toggleStrategy(strategy) {
      console.log('Toggle Strategy Clicked:', strategy);
      const endpoint = strategy.enabled ? 'http://localhost:8000/disable_strategy' : 'http://localhost:8000/enable_strategy';
      try {
        const response = await fetch(endpoint, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            token: strategy.token,
            timeframe: strategy.timeframe,
            strategy_id: strategy.strategy_id
          })
        });
        console.log('Response status:', response.status);
        await this.loadStrategies();
      } catch (error) {
        console.error('Error toggling strategy:', error);
      }
    },
    async toggleTradeControl(strategy, enable_buy, enable_sell) {
      console.log('Button clicked for strategy:', strategy);
      try {
        const response = await fetch('http://localhost:8000/set_trade_control', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            token: strategy.token,
            timeframe: strategy.timeframe,
            strategy_id: strategy.strategy_id,
            enable_buy: enable_buy,
            enable_sell: enable_sell
          })
        });
        console.log('Response status:', response.status);
        await this.loadStrategies();
      } catch (error) {
        console.error('Error updating trade controls:', error);
      }
    },

    async updateSL(symbol, sl) {
      await this.sendTradeAction({
        action: 'update_sl',
        trade_key: symbol,
        new_sl: parseFloat(sl)
      });
    },

    async updateTarget(symbol, target) {
      await this.sendTradeAction({
        action: 'update_target',
        trade_key: symbol,
        new_target: parseFloat(target)
      });
    },

    async closeTrade(symbol) {
      await this.sendTradeAction({
        action: 'close_trade',
        trade_key: symbol
      });
    },

    async exitAllTrades() {
      await this.sendTradeAction({
        action: 'exit_all_trades'
      });
    },

    async sendTradeAction(action) {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify(action));
      }
    },

    calculateTotalPnL() {
      const historyPnL = this.tradeHistory.reduce((sum, trade) => sum + parseFloat(trade['P&L'] || 0), 0);
      const activePnL = this.activeTrades.reduce((sum, trade) => sum + (trade.current_pnl || 0), 0);
      this.totalPnL = historyPnL;
    }
  },

  mounted() {
    this.connectWebSocket();
    this.loadStrategies();
  },

  beforeUnmount() {
    if (this.ws) {
      this.ws.close();
    }
  }
};
</script>

<style>
/* Custom scrollbar styling */
.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  border-radius: 3px;
}

/* Add smooth transitions */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
</style>
            
         