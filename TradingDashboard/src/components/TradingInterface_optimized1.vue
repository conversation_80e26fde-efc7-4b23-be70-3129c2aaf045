<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Dashboard Header -->
    <header class="bg-white shadow-sm mb-6">
      <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-4 flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
        <div class="text-center sm:text-left">
          <h1 class="text-2xl sm:text-3xl font-bold text-gray-900">Trading Panel</h1>
          <p class="text-sm sm:text-base text-gray-600 mt-1">HFT Real-time position management and trade analytics</p>
        </div>
        <div class="flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-6">
          <div class="p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg shadow-md flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-3">
            <div class="text-sm font-medium text-gray-600">Total P&L</div>
            <div class="flex items-center space-x-2">
              <svg v-if="totalPnL > 0" class="h-5 w-5 text-green-600" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
              <svg v-else-if="totalPnL < 0" class="h-5 w-5 text-red-600" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd" />
              </svg>
              <p class="text-lg font-semibold" :class="{'text-green-600': totalPnL > 0, 'text-red-600': totalPnL < 0}">
                {{ formatCurrency(totalPnL) }}
              </p>
            </div>
          </div>
          <button @click="exitAllTrades" class="px-5 py-2.5 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200 shadow-md flex items-center justify-center">
            Exit All Positions
          </button>
        </div>
      </div>
    </header>

    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Active Trades Card -->
      <div class="bg-white rounded-lg shadow-lg mb-6">
        <div class="p-4 border-b border-gray-200 flex justify-between items-center">
          <h2 class="text-xl font-semibold text-gray-800">Active Positions</h2>
        </div>
        <div class="overflow-x-auto">
          <div class="max-h-[400px] overflow-y-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50 sticky top-0">
                <tr>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 120px;">Symbol</th>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 100px;">Direction</th>
                  <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 120px;">Entry Price</th>
                  <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 120px;">Current P&L</th>
                  <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 120px;">Stop Loss</th>
                  <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 120px;">SL-Trigger</th>
                  <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 120px;">Target</th>
                  <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 100px;">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="trade in activeTrades" :key="trade.order_id" class="hover:bg-gray-100 transition-all duration-200">
                  <td class="px-4 py-2 text-sm text-gray-900">{{ trade.symbol }}</td>
                  <td class="px-4 py-2" :class="trade.action === 'BUY' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">{{ trade.action }}</td>
                  <td class="px-4 py-2 text-sm text-gray-900 text-right">{{ formatPrice(trade.entry_price) }}</td>
                  <td class="px-4 py-2 text-sm font-medium text-right" :class="{'text-green-600': trade.current_pnl > 0, 'text-red-600': trade.current_pnl < 0}">{{ formatCurrency(trade.current_pnl) }}</td>
                  <td class="px-4 py-2 text-center">
                    <input type="number" :value="trade.sl" @input="(e) => updateSL(trade.symbol, e.target.value)" class="w-20 p-0.5 border border-gray-200 rounded-md text-center text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all bg-gray-50" step="1" />
                    <span class="text-xs text-gray-500">{{ calculatePrice(trade, 'sl') }}</span>
                  </td>
                  <td class="px-4 py-2 text-center" :class="{'bg-blue-200 text-blue-800 animate-heartbeat': trade.trail_sl_trigger, 'bg-gray-100 text-gray-800': !trade.trail_sl_trigger}">{{ trade.trail_sl_trigger ? formatPrice(trade.trail_sl_trigger) : '-' }}</td>
                  <td class="px-4 py-2 text-center">
                    <input type="number" :value="trade.target" @input="(e) => updateTarget(trade.symbol, e.target.value)" class="w-20 p-0.5 border border-gray-300 rounded-md text-center text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all bg-gray-50" step="1" />
                    <span class="text-xs text-gray-500">{{ calculatePrice(trade, 'target') }}</span>
                  </td>
                  <td class="px-4 py-2 text-center">
                    <button @click="closeTrade(trade.symbol)" class="px-3 py-1 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors duration-200">Close</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Trade History Card -->
      <div class="bg-white rounded-lg shadow-lg">
        <div class="p-4 border-b border-gray-200">
          <h2 class="text-xl font-semibold text-gray-800">Trade History</h2>
        </div>
        <div class="overflow-x-auto">
          <div class="max-h-[250px] overflow-y-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50 sticky top-0">
                <tr>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 200px;">Date & Time</th>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Signal</th>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Entry</th>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Exit</th>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 100px;">Exit Time</th>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">P&L</th>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Exit Reason</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="(trade, index) in tradeHistory" :key="trade.Symbol" :class="{'bg-white': index % 2 === 0, 'bg-gray-50': index % 2 !== 0}">
                  <td class="px-4 py-2 text-sm text-gray-900">{{ `${trade.Date} ${trade.Time}` }}</td>
                  <td class="px-4 py-2 text-sm text-gray-900">{{ trade.Symbol }}</td>
                  <td class="px-4 py-2" :class="trade.Signal === 'BUY' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">{{ trade.Signal }}</td>
                  <td class="px-4 py-2 text-sm text-gray-900">{{ trade.Entry }}</td>
                  
  <!-- ... Previous template code ... -->
									<td class="px-4 py-2 text-sm text-gray-900">{{ trade.Exit }}</td>
									<td class="px-4 py-2 text-sm text-gray-900">{{ trade['Exit Time'] }}</td>
									<td class="px-4 py-2 text-sm font-medium" :class="{'text-green-600': parseFloat(trade['P&L']) > 0, 'text-red-600': parseFloat(trade['P&L']) < 0}">{{ formatCurrency(parseFloat(trade['P&L'])) }}</td>
									<td class="px-4 py-2" :class="trade.Status === 'Active' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'">{{ trade.Status }}</td>
									<td class="px-4 py-2 text-sm text-gray-500">{{ trade['Exit Reason'] }}</td>
								</tr>
							</tbody>
							</table>
						</div>
						</div>
					</div>

					<!-- Strategy Manager Card -->
					<div class="bg-white rounded-lg shadow-lg mt-6">
						<div class="p-4 border-b border-gray-200">
						<h2 class="text-xl font-semibold text-gray-800">Strategy Manager</h2>
						</div>
						<div class="overflow-x-auto">
						<div class="p-4">
							<table class="min-w-full divide-y divide-gray-200">
							<thead class="bg-gray-50">
								<tr>
								<th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Strategy ID</th>
								<th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Token</th>
								<th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Timeframe</th>
								<th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">Status</th>
								<th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">Buy</th>
								<th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">Sell</th>
								</tr>
							</thead>
							<tbody class="bg-white divide-y divide-gray-200">
								<tr v-for="strategy in strategies" :key="`${strategy.token}-${strategy.timeframe}-${strategy.strategy_id}`" class="hover:bg-gray-100 transition-all duration-200">
								<td class="px-4 py-4 text-sm text-gray-900">{{ strategy.strategy_id }}</td>
								<td class="px-4 py-4 text-sm text-gray-500">{{ strategy.token }}</td>
								<td class="px-4 py-4 text-sm text-gray-500">{{ strategy.timeframe }}</td>
								<td class="px-4 py-4 text-center">
									<button @click="toggleStrategy(strategy)" :class="`px-3 py-1 rounded-full text-xs font-semibold ${strategy.enabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`">
									{{ strategy.enabled ? 'Enabled' : 'Disabled' }}
									</button>
								</td>
								<td class="px-4 py-4 text-center" v-if="strategy.enabled">
									<button @click="toggleTradeControl(strategy, !strategy.enable_buy, strategy.enable_sell)" :class="`px-3 py-1 rounded-full text-xs font-semibold ${strategy.enable_buy ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`">
									{{ strategy.enable_buy ? 'Enabled' : 'Disabled' }}
									</button>
								</td>
								<td class="px-4 py-4 text-center" v-if="strategy.enabled">
									<button @click="toggleTradeControl(strategy, strategy.enable_buy, !strategy.enable_sell)" :class="`px-3 py-1 rounded-full text-xs font-semibold ${strategy.enable_sell ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`">
									{{ strategy.enable_sell ? 'Enabled' : 'Disabled' }}
									</button>
								</td>
								</tr>
							</tbody>
							</table>
						</div>
						</div>
					</div>
					</div>
				</div>
				</template>

<script>
export default {
  data() {
    return {
      ws: null,
      activeTrades: [],
      tradeHistory: [],
      totalPnL: 0,
      strategies: []
    };
  },
  mounted() {
    this.connectWebSocket();
    this.loadStrategies();
  },
  beforeUnmount() {
    if (this.ws) {
      this.ws.close();
    }
  },
  methods: {
    formatCurrency(value) {
      return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR',
        minimumFractionDigits: 2
      }).format(value);
    },
    formatPrice(value) {
      return new Intl.NumberFormat('en-IN', {
        minimumFractionDigits: 2
      }).format(value);
    },
    connectWebSocket() {
      this.ws = new WebSocket('ws://localhost:8000/ws/trades');

      this.ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        if (data.type === 'initial_data') {
          this.activeTrades = data.active_trades;
          this.tradeHistory = data.trade_history;
        } else if (data.type === 'update') {
          this.activeTrades = data.active_trades;
          this.parseTradeHistory(data.trade_history);
          this.calculateTotalPnL();
        } else if (data.type === 'strategy_update') {
          this.strategies = [...data.strategy_states];
        }
      };

      this.ws.onclose = () => {
        setTimeout(() => {
          this.connectWebSocket();
        }, 1000);
      };
    },

    calculatePrice(trade, type) {
      if (!trade || !trade.entry_price) return '-'; // Check if trade or entry_price is missing
      const points = trade[type]; // Get the SL or Target points
      if (!points) return '-'; // If points are missing, return '-'

      // For both calls and puts:
      // - SL is calculated as entry_price - points
      // - Target is calculated as entry_price + points
      return type === 'target'
        ? (trade.entry_price + points).toFixed(2) // Target calculation
        : (trade.entry_price - points).toFixed(2); // SL calculation
    },

    calculateTotalPnL() {
      const historyPnL = this.tradeHistory.reduce((sum, trade) => sum + parseFloat(trade['P&L'] || 0), 0);
      const activePnL = this.activeTrades.reduce((sum, trade) => sum + (trade.current_pnl || 0), 0);
      this.totalPnL = historyPnL;
    },
    
    parseTradeHistory(historyString) {
		if (typeof historyString !== 'string') return [];
		const lines = historyString.split('\n');
		const trades = [];

		for (const line of lines) {
			if (line.includes('Trade History:') || line.includes('Trade Key') || line.trim().startsWith('---')) {
			continue;
			}
			if (line.includes('Total P&L:')) break;
			if (line.trim()) {
			const parts = line.trim().split(/\s+/).filter(Boolean);
			if (parts.length >= 11) {
				const [tradeKey, date, time, symbol, action, entry, exit, exitTime, pnl, status, ...exitReason] = parts;
				trades.push({
				'Date': date,
				'Time': time,
				'Symbol': symbol,
				'Signal': action,
				'Entry': parseFloat(entry).toFixed(2),
				'Exit': parseFloat(exit).toFixed(2),
				'Exit Time': exitTime,
				'P&L': parseFloat(pnl).toFixed(2),
				'Status': status,
				'Exit Reason': exitReason.join(' ') || 'N/A'
				});
			}
			}
		}

		this.tradeHistory = trades.sort((a, b) => {
			const dateTimeA = new Date(`${a.Date} ${a.Time}`);
			const dateTimeB = new Date(`${b.Date} ${b.Time}`);
			return dateTimeB - dateTimeA; // Sort by date & time DESCENDING (newest first)
		});

		this.calculateTotalPnL();
		},
    
    async updateSL(symbol, sl) {
      await this.sendTradeAction({
        action: 'update_sl',
        trade_key: symbol,
        new_sl: parseFloat(sl)
      });
    },
    async loadStrategies() {
      try {
        const response = await fetch('http://localhost:8000/get_all_strategies');
        const data = await response.json();
        this.strategies = data;
      } catch (error) {
        console.error('Error loading strategies:', error);
      }
    },
    async toggleStrategy(strategy) {
      const endpoint = strategy.enabled ? 'http://localhost:8000/disable_strategy' : 'http://localhost:8000/enable_strategy';
      try {
        const response = await fetch(endpoint, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            token: strategy.token,
            timeframe: strategy.timeframe,
            strategy_id: strategy.strategy_id
          })
        });
        await this.loadStrategies();
      } catch (error) {
        console.error('Error toggling strategy:', error);
      }
    },
    async toggleTradeControl(strategy, enable_buy, enable_sell) {
      try {
        const response = await fetch('http://localhost:8000/set_trade_control', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            token: strategy.token,
            timeframe: strategy.timeframe,
            strategy_id: strategy.strategy_id,
            enable_buy: enable_buy,
            enable_sell: enable_sell
          })
        });
        await this.loadStrategies();
      } catch (error) {
        console.error('Error updating trade controls:', error);
      }
    },
    async updateTarget(symbol, target) {
      await this.sendTradeAction({
        action: 'update_target',
        trade_key: symbol,
        new_target: parseFloat(target)
      });
    },
    async closeTrade(symbol) {
      await this.sendTradeAction({
        action: 'close_trade',
        trade_key: symbol
      });
    },
    async exitAllTrades() {
      await this.sendTradeAction({
        action: 'exit_all_trades'
      });
    },
    async sendTradeAction(action) {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify(action));
      }
    }
  }
};
</script>

<style scoped>
/* Add any component-specific styles here if needed */
</style>
