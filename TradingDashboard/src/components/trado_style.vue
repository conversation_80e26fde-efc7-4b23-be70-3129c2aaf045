<template>
  <div class="min-h-screen bg-gray-100">
    <!-- Dashboard Header -->
    <header class="bg-white shadow-sm mb-4">
      <div class="container mx-auto px-4 py-3 flex justify-between items-center">
        <div>
          <h1 class="text-xl font-bold text-gray-900">Trading Panel</h1>
          <p class="text-sm text-gray-600">HFT Real-time position management</p>
        </div>
        <div class="flex items-center space-x-4">
          <div class="px-3 py-2 bg-gray-50 rounded-md shadow-sm flex items-center space-x-3">
            <div class="text-sm font-medium text-gray-600">Total P&L</div>
            <div class="flex items-center space-x-1">
              <svg v-if="totalPnL > 0" class="h-4 w-4 text-green-600" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
              <svg v-else-if="totalPnL < 0" class="h-4 w-4 text-red-600" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd" />
              </svg>
              <p class="text-base font-semibold" :class="{'text-green-600': totalPnL > 0, 'text-red-600': totalPnL < 0}">
                {{ formatCurrency(totalPnL) }}
              </p>
            </div>
          </div>
          <button @click="exitAllTrades" class="px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors duration-200 text-sm font-medium">
            Exit All Positions
          </button>
        </div>
      </div>
    </header>

    <div class="container mx-auto px-4">
      <!-- Market Data -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
        <div class="bg-white rounded-md shadow-sm p-3 flex justify-between items-center">
          <div>
            <div class="text-xs text-gray-500">BANKNIFTY</div>
            <div class="text-lg font-bold">48114.30</div>
          </div>
          <div class="text-sm text-red-600">-0.48% (232.06)</div>
        </div>
        <div class="bg-white rounded-md shadow-sm p-3 flex justify-between items-center">
          <div>
            <div class="text-xs text-gray-500">NIFTY</div>
            <div class="text-lg font-bold">22119.30</div>
          </div>
          <div class="text-sm text-red-600">-0.02% (4.42)</div>
        </div>
      </div>

      <!-- Strike Selection -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-4">
        <div class="bg-white rounded-md shadow-sm p-3">
          <div class="text-xs text-gray-500 mb-1">Expiry</div>
          <select class="w-full border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
            <option>27-03-2025</option>
          </select>
        </div>
        <div class="bg-white rounded-md shadow-sm p-3">
          <div class="text-xs text-gray-500 mb-1">CE Strike</div>
          <select class="w-full border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
            <option>48100</option>
          </select>
        </div>
        <div class="bg-white rounded-md shadow-sm p-3">
          <div class="text-xs text-gray-500 mb-1">PE Strike</div>
          <select class="w-full border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
            <option>48100</option>
          </select>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
        <div class="grid grid-cols-1 gap-2">
          <div class="bg-white rounded-md shadow-sm p-2">
            <div class="text-xs text-gray-500 mb-1">LMT</div>
            <input type="text" class="w-full border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm" placeholder="937.70" />
          </div>
          <button class="bg-green-600 hover:bg-green-700 text-white rounded-md py-2 font-medium">CE BUY</button>
          <button class="bg-red-600 hover:bg-red-700 text-white rounded-md py-2 font-medium">CE SELL</button>
        </div>
        <div class="grid grid-cols-1 gap-2">
          <div class="bg-white rounded-md shadow-sm p-2">
            <div class="text-xs text-gray-500 mb-1">LMT</div>
            <input type="text" class="w-full border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm" placeholder="624.35" />
          </div>
          <button class="bg-green-600 hover:bg-green-700 text-white rounded-md py-2 font-medium">PE BUY</button>
          <button class="bg-red-600 hover:bg-red-700 text-white rounded-md py-2 font-medium">PE SELL</button>
        </div>
      </div>

      <!-- Trade Management -->
      <div class="mb-4 grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="bg-white rounded-md shadow-sm p-2">
          <div class="text-xs text-gray-500 mb-1">PD TGT Points</div>
          <input type="text" class="w-full border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm" />
        </div>
        <div class="bg-white rounded-md shadow-sm p-2">
          <div class="text-xs text-gray-500 mb-1">PD SL Points</div>
          <input type="text" class="w-full border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm" />
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <button @click="closeAllPositions" class="bg-blue-600 hover:bg-blue-700 text-white rounded-md py-2 font-medium text-sm">
          CLOSE ALL POSITIONS
        </button>
        <button @click="cancelAllOrders" class="bg-blue-600 hover:bg-blue-700 text-white rounded-md py-2 font-medium text-sm">
          CANCEL ALL ORDERS
        </button>
      </div>

      <!-- Active Positions Card -->
      <div class="bg-white rounded-md shadow-sm mb-4">
        <div class="p-3 border-b border-gray-200">
          <h2 class="text-base font-semibold text-gray-800">Active Positions</h2>
        </div>
        <div class="overflow-x-auto">
          <div class="max-h-[300px] overflow-y-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50 sticky top-0">
                <tr>
                  <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
                  <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Direction</th>
                  <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Entry</th>
                  <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">P&L</th>
                  <th class="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">SL</th>
                  <th class="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Target</th>
                  <th class="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="trade in activeTrades" :key="trade.order_id" class="hover:bg-gray-50 transition-all duration-200">
                  <td class="px-3 py-2 text-sm text-gray-900">{{ trade.symbol }}</td>
                  <td class="px-3 py-2" :class="trade.action === 'BUY' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">{{ trade.action }}</td>
                  <td class="px-3 py-2 text-sm text-gray-900 text-right">{{ formatPrice(trade.entry_price) }}</td>
                  <td class="px-3 py-2 text-sm font-medium text-right" :class="{'text-green-600': trade.current_pnl > 0, 'text-red-600': trade.current_pnl < 0}">{{ formatCurrency(trade.current_pnl) }}</td>
                  <td class="px-3 py-2 text-center">
                    <input type="number" :value="trade.sl" @input="(e) => updateSL(trade.symbol, e.target.value)" class="w-16 p-0.5 border border-gray-200 rounded text-center text-sm" step="1" />
                  </td>
                  <td class="px-3 py-2 text-center">
                    <input type="number" :value="trade.target" @input="(e) => updateTarget(trade.symbol, e.target.value)" class="w-16 p-0.5 border border-gray-200 rounded text-center text-sm" step="1" />
                  </td>
                  <td class="px-3 py-2 text-center">
                    <button @click="closeTrade(trade.symbol)" class="px-2 py-1 bg-red-500 text-white rounded text-xs hover:bg-red-600 transition-colors">Close</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Tabs for Trade History and Strategy Manager -->
      <div class="bg-white rounded-md shadow-sm">
        <div class="border-b border-gray-200">
          <nav class="flex -mb-px">
            <button 
              @click="activeTab = 'positions'"
              :class="[
                activeTab === 'positions' 
                  ? 'border-blue-500 text-blue-600' 
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
                'py-2 px-4 text-sm font-medium border-b-2 flex-1 text-center'
              ]"
            >
              Positions
            </button>
            <button 
              @click="activeTab = 'pending'"
              :class="[
                activeTab === 'pending' 
                  ? 'border-blue-500 text-blue-600' 
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
                'py-2 px-4 text-sm font-medium border-b-2 flex-1 text-center'
              ]"
            >
              Pending Orders
            </button>
            <button 
              @click="activeTab = 'history'"
              :class="[
                activeTab === 'history' 
                  ? 'border-blue-500 text-blue-600' 
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
                'py-2 px-4 text-sm font-medium border-b-2 flex-1 text-center'
              ]"
            >
              Trade History
            </button>
            <button 
              @click="activeTab = 'strategy'"
              :class="[
                activeTab === 'strategy' 
                  ? 'border-blue-500 text-blue-600' 
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
                'py-2 px-4 text-sm font-medium border-b-2 flex-1 text-center'
              ]"
            >
              Strategy Manager
            </button>
          </nav>
        </div>

        <!-- Tab content -->
        <div v-if="activeTab === 'positions'" class="p-3">
          <div v-if="activeTrades.length === 0" class="flex flex-col items-center justify-center py-6">
            <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <p class="mt-2 text-sm text-gray-500">No active positions</p>
          </div>
          <div v-else class="max-h-[300px] overflow-y-auto">
            <!-- Position content rendered elsewhere -->
          </div>
        </div>

        <div v-if="activeTab === 'pending'" class="p-3">
          <div class="flex flex-col items-center justify-center py-6">
            <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <p class="mt-2 text-sm text-gray-500">No pending orders</p>
          </div>
        </div>

        <div v-if="activeTab === 'history'" class="overflow-x-auto">
          <div class="max-h-[300px] overflow-y-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50 sticky top-0">
                <tr>
                  <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                  <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
                  <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Signal</th>
                  <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Entry</th>
                  <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Exit</th>
                  <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">P&L</th>
                  <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="(trade, index) in tradeHistory" :key="index" :class="{'bg-white': index % 2 === 0, 'bg-gray-50': index % 2 !== 0}">
                  <td class="px-3 py-2 text-sm text-gray-900">{{ `${trade.Date} ${trade.Time}` }}</td>
                  <td class="px-3 py-2 text-sm text-gray-900">{{ trade.Symbol }}</td>
                  <td class="px-3 py-2" :class="trade.Signal === 'BUY' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">{{ trade.Signal }}</td>
                  <td class="px-3 py-2 text-sm text-gray-900">{{ trade.Entry }}</td>
                  <td class="px-3 py-2 text-sm text-gray-900">{{ trade.Exit }}</td>
                  <td class="px-3 py-2 text-sm font-medium" :class="{'text-green-600': parseFloat(trade['P&L']) > 0, 'text-red-600': parseFloat(trade['P&L']) < 0}">{{ formatCurrency(parseFloat(trade['P&L'])) }}</td>
                  <td class="px-3 py-2" :class="trade.Status === 'Active' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'">{{ trade.Status }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div v-if="activeTab === 'strategy'" class="overflow-x-auto">
          <div class="p-3">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Strategy ID</th>
                  <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Token</th>
                  <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Timeframe</th>
                  <th class="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th class="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Buy</th>
                  <th class="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Sell</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="strategy in strategies" :key="`${strategy.token}-${strategy.timeframe}-${strategy.strategy_id}`" class="hover:bg-gray-50 transition-all duration-200">
                  <td class="px-3 py-2 text-sm text-gray-900">{{ strategy.strategy_id }}</td>
                  <td class="px-3 py-2 text-sm text-gray-500">{{ strategy.token }}</td>
                  <td class="px-3 py-2 text-sm text-gray-500">{{ strategy.timeframe }}</td>
                  <td class="px-3 py-2 text-center">
                    <button @click="toggleStrategy(strategy)" :class="`px-2 py-1 rounded-full text-xs font-semibold ${strategy.enabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`">
                      {{ strategy.enabled ? 'Enabled' : 'Disabled' }}
                    </button>
                  </td>
                  <td class="px-3 py-2 text-center" v-if="strategy.enabled">
                    <button @click="toggleTradeControl(strategy, !strategy.enable_buy, strategy.enable_sell)" :class="`px-2 py-1 rounded-full text-xs font-semibold ${strategy.enable_buy ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`">
                      {{ strategy.enable_buy ? 'Enabled' : 'Disabled' }}
                    </button>
                  </td>
                  <td class="px-3 py-2 text-center" v-if="strategy.enabled">
                    <button @click="toggleTradeControl(strategy, strategy.enable_buy, !strategy.enable_sell)" :class="`px-2 py-1 rounded-full text-xs font-semibold ${strategy.enable_sell ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`">
                      {{ strategy.enable_sell ? 'Enabled' : 'Disabled' }}
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      ws: null,
      activeTrades: [],
      tradeHistory: [],
      totalPnL: 0,
      strategies: [],
      activeTab: 'positions',
    };
  },
  mounted() {
    this.connectWebSocket();
    this.loadStrategies();
  },
  beforeUnmount() {
    if (this.ws) {
      this.ws.close();
    }
  },
  methods: {
    formatCurrency(value) {
      return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR',
        minimumFractionDigits: 2
      }).format(value);
    },
    formatPrice(value) {
      return new Intl.NumberFormat('en-IN', {
        minimumFractionDigits: 2
      }).format(value);
    },
    connectWebSocket() {
      this.ws = new WebSocket('ws://localhost:8000/ws/trades');

      this.ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        if (data.type === 'initial_data') {
          this.activeTrades = data.active_trades;
          this.tradeHistory = data.trade_history;
        } else if (data.type === 'update') {
          this.activeTrades = data.active_trades;
          this.parseTradeHistory(data.trade_history);
          this.calculateTotalPnL();
        } else if (data.type === 'strategy_update') {
          this.strategies = [...data.strategy_states];
        }
      };

      this.ws.onclose = () => {
        setTimeout(() => {
          this.connectWebSocket();
        }, 1000);
      };
    },
    calculateTotalPnL() {
      const historyPnL = this.tradeHistory.reduce((sum, trade) => sum + parseFloat(trade['P&L'] || 0), 0);
      const activePnL = this.activeTrades.reduce((sum, trade) => sum + (trade.current_pnl || 0), 0);
      this.totalPnL = historyPnL;
    },
    parseTradeHistory(historyString) {
      if (typeof historyString !== 'string') return [];
      const lines = historyString.split('\n');
      const trades = [];

      for (const line of lines) {
        if (line.includes('Trade History:') || line.includes('Trade Key') || line.trim().startsWith('---')) {
          continue;
        }
        if (line.includes('Total P&L:')) break;
        if (line.trim()) {
          const parts = line.trim().split(/\s+/).filter(Boolean);
          if (parts.length >= 11) {
            const [tradeKey, date, time, symbol, action, entry, exit, exitTime, pnl, status, ...exitReason] = parts;
            trades.push({
              'Date': date,
              'Time': time,
              'Symbol': symbol,
              'Signal': action,
              'Entry': parseFloat(entry).toFixed(2),
              'Exit': parseFloat(exit).toFixed(2),
              'Exit Time': exitTime,
              'P&L': parseFloat(pnl).toFixed(2),
              'Status': status,
              'Exit Reason': exitReason.join(' ') || 'N/A'
            });
          }
        }
      }

      this.tradeHistory = trades.sort((a, b) => {
        const dateTimeA = new Date(`${a.Date} ${a.Time}`);
        const dateTimeB = new Date(`${b.Date} ${b.Time}`);
        return dateTimeB - dateTimeA; // Sort by date & time DESCENDING (newest first)
      });

      this.calculateTotalPnL();
    },
    async updateSL(symbol, sl) {
      await this.sendTradeAction({
        action: 'update_sl',
        trade_key: symbol,
        new_sl: parseFloat(sl)
      });
    },
    async loadStrategies() {
      try {
        const response = await fetch('http://localhost:8000/get_all_strategies');
        const data = await response.json();
        this.strategies = data;
      } catch (error) {
        console.error('Error loading strategies:', error);
      }
    },
    async toggleStrategy(strategy) {
      const endpoint = strategy.enabled ? 'http://localhost:8000/disable_strategy' : 'http://localhost:8000/enable_strategy';
      try {
        await fetch(endpoint, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            token: strategy.token,
            timeframe: strategy.timeframe,
            strategy_id: strategy.strategy_id
          })
        });
        await this.loadStrategies();
      } catch (error) {
        console.error('Error toggling strategy:', error);
      }
    },
    async toggleTradeControl(strategy, enable_buy, enable_sell) {
      try {
        await fetch('http://localhost:8000/set_trade_control', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            token: strategy.token,
            timeframe: strategy.timeframe,
            strategy_id: strategy.strategy_id,
            enable_buy: enable_buy,
            enable_sell: enable_sell
          })
        });
        await this.loadStrategies();
      } catch (error) {
        console.error('Error updating trade controls:', error);
      }
    },
    async updateTarget(symbol, target) {
      await this.sendTradeAction({
        action: 'update_target',
        trade_key: symbol,
        new_target: parseFloat(target)
      });
    },
    async closeTrade(symbol) {
      await this.sendTradeAction({
        action: 'close_trade',
        trade_key: symbol
      });
    },
    async exitAllTrades() {
      await this.sendTradeAction({
        action: 'exit_all_trades'
      });
    },
    async sendTradeAction(action) {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify(action));
      }
    },
    closeAllPositions() {
      this.exitAllTrades();
    },
    cancelAllOrders() {
      // Implement this method to cancel all pending orders
      console.log('Cancelling all orders');
    }
  }
};
</script>

<style>
/* Add animation for visual feedback */
@keyframes heartbeat {
  0% { opacity: 1; }
  50% { opacity: 0.8; }
  100% { opacity: 1; }
}

.animate-heartbeat {
  animation: heartbeat 1.5s ease-in-out infinite;
}
</style>