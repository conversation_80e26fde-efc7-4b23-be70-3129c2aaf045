from dataclasses import dataclass
from typing import Dict, List, Any, Optional
import numpy as np
from collections import deque
import asyncio
from enum import Enum
import time
import logging

class MarketEvent(Enum):
    TICK = "TICK"
    ORDERBOOK_UPDATE = "ORDERBOOK_UPDATE"
    DEPTH_UPDATE = "DEPTH_UPDATE"
    LTP_UPDATE = "LTP_UPDATE"

@dataclass
class DepthTick:
    symbol: str
    timestamp: float
    bid_prices: np.ndarray
    bid_quantities: np.ndarray
    bid_orders: np.ndarray
    ask_prices: np.ndarray
    ask_quantities: np.ndarray
    ask_orders: np.ndarray
    is_snapshot: bool = False  # Flag to indicate if this is a snapshot
    
    @classmethod
    def from_fyers_message(cls, symbol: str, message: Any, is_snapshot: bool = False):
        """Create a DepthTick from a Fyers message"""
        try:
            logger = logging.getLogger("DepthTick")
            
            # Handle Fyers Depth object
            if hasattr(message, '__class__') and message.__class__.__name__ == 'Depth':
                # Check if message has snapshot attribute
                if hasattr(message, 'snapshot'):
                    is_snapshot = message.snapshot
                
                # Extract bid data from bidprice, bidqty, and bidordn attributes
                bid_prices = np.array(message.bidprice) if hasattr(message, 'bidprice') else np.array([])
                bid_quantities = np.array(message.bidqty) if hasattr(message, 'bidqty') else np.array([])
                bid_orders = np.array(message.bidordn) if hasattr(message, 'bidordn') else np.array([])
                
                # Extract ask data from askprice, askqty, and askordn attributes
                ask_prices = np.array(message.askprice) if hasattr(message, 'askprice') else np.array([])
                ask_quantities = np.array(message.askqty) if hasattr(message, 'askqty') else np.array([])
                ask_orders = np.array(message.askordn) if hasattr(message, 'askordn') else np.array([])
                
                # Convert to proper numeric types if needed
                bid_prices = bid_prices.astype(float) if bid_prices.size > 0 else bid_prices
                bid_quantities = bid_quantities.astype(int) if bid_quantities.size > 0 else bid_quantities
                bid_orders = bid_orders.astype(int) if bid_orders.size > 0 else bid_orders
                ask_prices = ask_prices.astype(float) if ask_prices.size > 0 else ask_prices
                ask_quantities = ask_quantities.astype(int) if ask_quantities.size > 0 else ask_quantities
                ask_orders = ask_orders.astype(int) if ask_orders.size > 0 else ask_orders
            elif isinstance(message, dict):
                # It's a dictionary
                # Check if message has snapshot attribute
                if 'snapshot' in message:
                    is_snapshot = message['snapshot']
                    
                bid_prices = np.array(message.get('bidprice', [])).astype(float) if message.get('bidprice', []) else np.array([])
                bid_quantities = np.array(message.get('bidqty', [])).astype(int) if message.get('bidqty', []) else np.array([])
                bid_orders = np.array(message.get('bidordn', [])).astype(int) if message.get('bidordn', []) else np.array([])
                
                ask_prices = np.array(message.get('askprice', [])).astype(float) if message.get('askprice', []) else np.array([])
                ask_quantities = np.array(message.get('askqty', [])).astype(int) if message.get('askqty', []) else np.array([])
                ask_orders = np.array(message.get('askordn', [])).astype(int) if message.get('askordn', []) else np.array([])
            else:
                # Unknown format, log and raise exception
                logger.error(f"Unknown message format: {message}")
                raise ValueError(f"Unknown message format: {message}")
            
            return cls(
                symbol=symbol,
                timestamp=time.time(),
                bid_prices=bid_prices,
                bid_quantities=bid_quantities,
                bid_orders=bid_orders,
                ask_prices=ask_prices,
                ask_quantities=ask_quantities,
                ask_orders=ask_orders,
                is_snapshot=is_snapshot
            )
        except Exception as e:
            logging.getLogger("DepthTick").error(f"Error creating DepthTick: {e} - Message: {message}", exc_info=True)
            raise

@dataclass
class LtpTick:
    symbol: str
    timestamp: float
    ltp: float
    prev_close_price: float = 0.0
    high_price: float = 0.0
    low_price: float = 0.0
    open_price: float = 0.0
    change: float = 0.0
    change_percent: float = 0.0
    volume: int = 0
    last_traded_time: int = 0
    bid_size: int = 0
    ask_size: int = 0
    ask_price: float = 0.0
    bid_price: float = 0.0
    last_traded_qty: int = 0
    total_buy_qty: int = 0
    total_sell_qty: int = 0
    avg_trade_price: float = 0.0
    message_type: str = ""
    
    @classmethod
    def from_fyers_message(cls, message: Dict[str, Any]):
        """Create an LtpTick from a Fyers message"""
        try:
            # Extract required fields with fallbacks for optional fields
            symbol = message.get('symbol', '')
            
            return cls(
                symbol=symbol,
                timestamp=time.time(),
                ltp=float(message.get('ltp', 0.0)),
                prev_close_price=float(message.get('prev_close_price', 0.0)),
                high_price=float(message.get('high_price', 0.0)),
                low_price=float(message.get('low_price', 0.0)),
                open_price=float(message.get('open_price', 0.0)),
                change=float(message.get('ch', 0.0)),
                change_percent=float(message.get('chp', 0.0)),
                volume=int(message.get('vol_traded_today', 0)),
                last_traded_time=int(message.get('last_traded_time', 0)),
                bid_size=int(message.get('bid_size', 0)),
                ask_size=int(message.get('ask_size', 0)),
                ask_price=float(message.get('ask_price', 0.0)),
                bid_price=float(message.get('bid_price', 0.0)),
                last_traded_qty=int(message.get('last_traded_qty', 0)),
                total_buy_qty=int(message.get('tot_buy_qty', 0)),
                total_sell_qty=int(message.get('tot_sell_qty', 0)),
                avg_trade_price=float(message.get('avg_trade_price', 0.0)),
                message_type=message.get('type', '')
            )
        except Exception as e:
            logging.getLogger("LtpTick").error(f"Error creating LtpTick: {e} - Message: {message}", exc_info=True)
            raise

class CircularBuffer:
    """A thread-safe circular buffer for storing market data"""
    def __init__(self, maxlen: int = 10000):
        self.buffer = deque(maxlen=maxlen)
        self._lock = asyncio.Lock()
    
    async def append(self, item):
        """Add an item to the buffer in a thread-safe manner"""
        async with self._lock:
            self.buffer.append(item)
    
    async def get_latest(self, n: int = 1) -> List[Any]:
        """Get the latest n items from the buffer"""
        async with self._lock:
            buffer_len = len(self.buffer)
            if buffer_len == 0:
                return []
            return list(self.buffer)[-min(n, buffer_len):]
    
    async def get_all(self) -> List[Any]:
        """Get all items from the buffer"""
        async with self._lock:
            return list(self.buffer)
    
    def __len__(self):
        return len(self.buffer)













