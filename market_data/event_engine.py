import asyncio
import logging
from typing import Dict, Callable, List, Any, Coroutine

class EventEngine:
    """Event-driven engine for distributing market events"""
    def __init__(self):
        self.handlers: Dict[str, List[Callable]] = {}
        self.logger = logging.getLogger("EventEngine")
        
    def register(self, event_type: str, handler: Callable[[Any], Coroutine]):
        """Register a handler for an event type"""
        if event_type not in self.handlers:
            self.handlers[event_type] = []
        self.handlers[event_type].append(handler)
        self.logger.debug(f"Registered handler for event: {event_type}")
        
    def unregister(self, event_type: str, handler: Callable):
        """Unregister a handler for an event type"""
        if event_type in self.handlers and handler in self.handlers[event_type]:
            self.handlers[event_type].remove(handler)
            self.logger.debug(f"Unregistered handler for event: {event_type}")
            
    async def emit(self, event_type: str, data: Any = None):
        """Emit an event to all registered handlers"""
        if event_type in self.handlers:
            tasks = []
            for handler in self.handlers[event_type]:
                tasks.append(asyncio.create_task(handler(data)))
            if tasks:
                await asyncio.gather(*tasks)